package com.careeralgo.service;

import com.careeralgo.config.WebSocketConfig;
import com.careeralgo.model.User;
import com.careeralgo.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Service for WebSocket authentication and session management
 */
@Service
public class WebSocketAuthService {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketAuthService.class);

    @Autowired
    private JwtDecoder jwtDecoder;

    @Autowired
    private UserRepository userRepository;

    // Store active WebSocket sessions
    private final ConcurrentMap<String, WebSocketSession> activeSessions = new ConcurrentHashMap<>();

    /**
     * Authenticate WebSocket connection using JWT token
     */
    public Authentication authenticateToken(String token) {
        try {
            // Decode and validate JWT token
            Jwt jwt = jwtDecoder.decode(token);
            
            // Extract user information from JWT
            String clerkUserId = jwt.getSubject();
            String email = jwt.getClaimAsString("email");
            
            // Find user in database
            User user = userRepository.findByClerkUserId(clerkUserId).orElse(null);
            
            if (user != null) {
                // Create WebSocket principal
                WebSocketConfig.WebSocketPrincipal principal = 
                    new WebSocketConfig.WebSocketPrincipal(user.getEmail(), user.getId());
                
                // Create authentication object
                Authentication auth = new UsernamePasswordAuthenticationToken(
                    principal, 
                    null, 
                    Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"))
                );
                
                // Store session information
                WebSocketSession session = new WebSocketSession(user.getId(), user.getEmail(), token);
                activeSessions.put(user.getId(), session);
                
                logger.info("WebSocket authentication successful for user: {}", user.getId());
                return auth;
            } else {
                logger.warn("User not found for Clerk ID: {}", clerkUserId);
                return null;
            }
            
        } catch (Exception e) {
            logger.error("WebSocket authentication failed", e);
            return null;
        }
    }

    /**
     * Check if user has active WebSocket session
     */
    public boolean hasActiveSession(String userId) {
        return activeSessions.containsKey(userId);
    }

    /**
     * Get active session for user
     */
    public WebSocketSession getActiveSession(String userId) {
        return activeSessions.get(userId);
    }

    /**
     * Remove session when user disconnects
     */
    public void removeSession(String userId) {
        WebSocketSession removed = activeSessions.remove(userId);
        if (removed != null) {
            logger.info("Removed WebSocket session for user: {}", userId);
        }
    }

    /**
     * Get all active sessions
     */
    public ConcurrentMap<String, WebSocketSession> getActiveSessions() {
        return activeSessions;
    }

    /**
     * Get count of active sessions
     */
    public int getActiveSessionCount() {
        return activeSessions.size();
    }

    /**
     * Update session last activity
     */
    public void updateSessionActivity(String userId) {
        WebSocketSession session = activeSessions.get(userId);
        if (session != null) {
            session.updateLastActivity();
        }
    }

    /**
     * Clean up inactive sessions
     */
    public void cleanupInactiveSessions(long inactiveThresholdMinutes) {
        long thresholdTime = System.currentTimeMillis() - (inactiveThresholdMinutes * 60 * 1000);
        
        activeSessions.entrySet().removeIf(entry -> {
            WebSocketSession session = entry.getValue();
            if (session.getLastActivity() < thresholdTime) {
                logger.info("Removing inactive WebSocket session for user: {}", entry.getKey());
                return true;
            }
            return false;
        });
    }

    /**
     * WebSocket session information
     */
    public static class WebSocketSession {
        private final String userId;
        private final String email;
        private final String token;
        private final long connectedAt;
        private long lastActivity;

        public WebSocketSession(String userId, String email, String token) {
            this.userId = userId;
            this.email = email;
            this.token = token;
            this.connectedAt = System.currentTimeMillis();
            this.lastActivity = System.currentTimeMillis();
        }

        public void updateLastActivity() {
            this.lastActivity = System.currentTimeMillis();
        }

        // Getters
        public String getUserId() {
            return userId;
        }

        public String getEmail() {
            return email;
        }

        public String getToken() {
            return token;
        }

        public long getConnectedAt() {
            return connectedAt;
        }

        public long getLastActivity() {
            return lastActivity;
        }

        public long getConnectionDuration() {
            return System.currentTimeMillis() - connectedAt;
        }

        public boolean isActive(long inactiveThresholdMinutes) {
            long threshold = System.currentTimeMillis() - (inactiveThresholdMinutes * 60 * 1000);
            return lastActivity > threshold;
        }
    }
}
