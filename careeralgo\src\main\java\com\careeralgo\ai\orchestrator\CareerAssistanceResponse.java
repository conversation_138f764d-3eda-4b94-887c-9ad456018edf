package com.careeralgo.ai.orchestrator;

import com.careeralgo.ai.agent.*;
import com.careeralgo.ai.orchestrator.ModernAIAgentOrchestrator.AgentMetrics;

import java.time.LocalDateTime;

/**
 * Response DTO for career assistance operations
 */
public class CareerAssistanceResponse {

    private ResponseType responseType;
    private String status;
    private String message;
    private LocalDateTime timestamp;
    private AgentMetrics metrics;

    // Specific response data
    private ResumeAnalysisAgent.ResumeAnalysisResult resumeAnalysis;
    private JobMatchingAgent.JobMatchResult jobMatching;
    private InterviewPrepAgent.InterviewEvaluationResult interviewPrep;
    private CareerAdvisorAgent.CareerAdviceResult careerAdvice;

    // Comprehensive analysis data
    private ComprehensiveAnalysisResult comprehensiveAnalysis;

    public enum ResponseType {
        RESUME_ANALYSIS,
        JOB_MATCHING,
        INTERVIEW_PREP,
        CAREER_ADVICE,
        COMPREHENSIVE_ANALYSIS,
        ERROR
    }

    // Constructors
    public CareerAssistanceResponse() {
        this.timestamp = LocalDateTime.now();
    }

    public CareerAssistanceResponse(ResponseType responseType, String status) {
        this();
        this.responseType = responseType;
        this.status = status;
    }

    // Static factory methods
    public static CareerAssistanceResponse resumeAnalysis(ResumeAnalysisAgent.ResumeAnalysisResult result) {
        CareerAssistanceResponse response = new CareerAssistanceResponse(ResponseType.RESUME_ANALYSIS, "SUCCESS");
        response.setResumeAnalysis(result);
        response.setMessage("Resume analysis completed successfully");
        return response;
    }

    public static CareerAssistanceResponse jobMatching(JobMatchingAgent.JobMatchResult result) {
        CareerAssistanceResponse response = new CareerAssistanceResponse(ResponseType.JOB_MATCHING, "SUCCESS");
        response.setJobMatching(result);
        response.setMessage("Job matching analysis completed successfully");
        return response;
    }

    public static CareerAssistanceResponse interviewPrep(InterviewPrepAgent.InterviewEvaluationResult result) {
        CareerAssistanceResponse response = new CareerAssistanceResponse(ResponseType.INTERVIEW_PREP, "SUCCESS");
        response.setInterviewPrep(result);
        response.setMessage("Interview preparation analysis completed successfully");
        return response;
    }

    public static CareerAssistanceResponse careerAdvice(CareerAdvisorAgent.CareerAdviceResult result) {
        CareerAssistanceResponse response = new CareerAssistanceResponse(ResponseType.CAREER_ADVICE, "SUCCESS");
        response.setCareerAdvice(result);
        response.setMessage("Career advice generated successfully");
        return response;
    }

    public static CareerAssistanceResponse comprehensive(
            ResumeAnalysisAgent.ResumeAnalysisResult resumeResult,
            JobMatchingAgent.JobMatchResult jobResult,
            CareerAdvisorAgent.CareerAdviceResult careerResult,
            AgentMetrics metrics) {
        
        CareerAssistanceResponse response = new CareerAssistanceResponse(ResponseType.COMPREHENSIVE_ANALYSIS, "SUCCESS");
        
        ComprehensiveAnalysisResult comprehensive = new ComprehensiveAnalysisResult(
            resumeResult,
            jobResult,
            careerResult,
            generateOverallInsights(resumeResult, jobResult, careerResult)
        );
        
        response.setComprehensiveAnalysis(comprehensive);
        response.setMetrics(metrics);
        response.setMessage("Comprehensive career analysis completed successfully");
        return response;
    }

    public static CareerAssistanceResponse error(String errorMessage) {
        CareerAssistanceResponse response = new CareerAssistanceResponse(ResponseType.ERROR, "ERROR");
        response.setMessage(errorMessage);
        return response;
    }

    private static String generateOverallInsights(
            ResumeAnalysisAgent.ResumeAnalysisResult resumeResult,
            JobMatchingAgent.JobMatchResult jobResult,
            CareerAdvisorAgent.CareerAdviceResult careerResult) {
        
        StringBuilder insights = new StringBuilder();
        insights.append("Overall Career Analysis Insights:\n\n");
        
        // Resume insights
        insights.append("Resume Quality: ").append(resumeResult.overallScore()).append("/100\n");
        insights.append("Key Strengths: ").append(String.join(", ", resumeResult.strengths())).append("\n\n");
        
        // Job matching insights
        insights.append("Job Compatibility: ").append(jobResult.overallMatchScore()).append("/100\n");
        insights.append("Skills Match: ").append(jobResult.skillsMatchScore()).append("/100\n\n");
        
        // Career advice summary
        insights.append("Career Recommendations:\n");
        careerResult.keyRecommendations().forEach(rec -> insights.append("- ").append(rec).append("\n"));
        
        return insights.toString();
    }

    // Nested class for comprehensive analysis
    public static class ComprehensiveAnalysisResult {
        private final ResumeAnalysisAgent.ResumeAnalysisResult resumeAnalysis;
        private final JobMatchingAgent.JobMatchResult jobMatching;
        private final CareerAdvisorAgent.CareerAdviceResult careerAdvice;
        private final String overallInsights;

        public ComprehensiveAnalysisResult(
                ResumeAnalysisAgent.ResumeAnalysisResult resumeAnalysis,
                JobMatchingAgent.JobMatchResult jobMatching,
                CareerAdvisorAgent.CareerAdviceResult careerAdvice,
                String overallInsights) {
            this.resumeAnalysis = resumeAnalysis;
            this.jobMatching = jobMatching;
            this.careerAdvice = careerAdvice;
            this.overallInsights = overallInsights;
        }

        // Getters
        public ResumeAnalysisAgent.ResumeAnalysisResult getResumeAnalysis() { return resumeAnalysis; }
        public JobMatchingAgent.JobMatchResult getJobMatching() { return jobMatching; }
        public CareerAdvisorAgent.CareerAdviceResult getCareerAdvice() { return careerAdvice; }
        public String getOverallInsights() { return overallInsights; }
    }

    // Getters and setters
    public ResponseType getResponseType() {
        return responseType;
    }

    public void setResponseType(ResponseType responseType) {
        this.responseType = responseType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public AgentMetrics getMetrics() {
        return metrics;
    }

    public void setMetrics(AgentMetrics metrics) {
        this.metrics = metrics;
    }

    public ResumeAnalysisAgent.ResumeAnalysisResult getResumeAnalysis() {
        return resumeAnalysis;
    }

    public void setResumeAnalysis(ResumeAnalysisAgent.ResumeAnalysisResult resumeAnalysis) {
        this.resumeAnalysis = resumeAnalysis;
    }

    public JobMatchingAgent.JobMatchResult getJobMatching() {
        return jobMatching;
    }

    public void setJobMatching(JobMatchingAgent.JobMatchResult jobMatching) {
        this.jobMatching = jobMatching;
    }

    public InterviewPrepAgent.InterviewEvaluationResult getInterviewPrep() {
        return interviewPrep;
    }

    public void setInterviewPrep(InterviewPrepAgent.InterviewEvaluationResult interviewPrep) {
        this.interviewPrep = interviewPrep;
    }

    public CareerAdvisorAgent.CareerAdviceResult getCareerAdvice() {
        return careerAdvice;
    }

    public void setCareerAdvice(CareerAdvisorAgent.CareerAdviceResult careerAdvice) {
        this.careerAdvice = careerAdvice;
    }

    public ComprehensiveAnalysisResult getComprehensiveAnalysis() {
        return comprehensiveAnalysis;
    }

    public void setComprehensiveAnalysis(ComprehensiveAnalysisResult comprehensiveAnalysis) {
        this.comprehensiveAnalysis = comprehensiveAnalysis;
    }
}
