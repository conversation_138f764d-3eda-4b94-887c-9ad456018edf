package com.careeralgo.service;

import com.careeralgo.model.AIAnalysis;
import com.careeralgo.model.ParsedContent;
import com.careeralgo.model.Resume;
import com.careeralgo.repository.ResumeRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * AI-powered resume analysis service
 */
@Service
public class AIResumeAnalysisService {

    private static final Logger logger = LoggerFactory.getLogger(AIResumeAnalysisService.class);

    @Autowired
    private ResumeRepository resumeRepository;

    /**
     * Analyze resume asynchronously
     */
    @Async
    public void analyzeResumeAsync(String resumeId) {
        try {
            Optional<Resume> resumeOpt = resumeRepository.findById(resumeId);
            if (resumeOpt.isEmpty()) {
                logger.warn("Resume not found for analysis: {}", resumeId);
                return;
            }

            Resume resume = resumeOpt.get();
            if (resume.getParsedContent() == null) {
                logger.warn("Resume has no parsed content for analysis: {}", resumeId);
                return;
            }

            AIAnalysis analysis = analyzeResume(resume.getParsedContent());
            resume.setAiAnalysis(analysis);
            resumeRepository.save(resume);

            logger.info("Successfully analyzed resume: {}", resumeId);

        } catch (Exception e) {
            logger.error("Error analyzing resume: {}", resumeId, e);
        }
    }

    /**
     * Analyze resume content and generate AI insights
     */
    public AIAnalysis analyzeResume(ParsedContent content) {
        AIAnalysis analysis = new AIAnalysis();
        analysis.setLastAnalyzedAt(LocalDateTime.now());

        // Calculate overall score
        int overallScore = calculateOverallScore(content);
        analysis.setOverallScore(overallScore);

        // Calculate ATS compatibility
        int atsScore = calculateATSCompatibility(content);
        analysis.setAtsCompatibility(atsScore);

        // Calculate readability score
        int readabilityScore = calculateReadabilityScore(content);
        analysis.setReadabilityScore(readabilityScore);

        // Generate strengths and improvements
        analysis.setStrengths(identifyStrengths(content));
        analysis.setImprovements(identifyImprovements(content));

        // Analyze keywords
        analysis.setKeywordAnalysis(analyzeKeywords(content));

        // Calculate section scores
        analysis.setSectionScores(calculateSectionScores(content));

        return analysis;
    }

    /**
     * Calculate overall resume score (0-100)
     */
    private int calculateOverallScore(ParsedContent content) {
        int score = 0;
        int maxScore = 100;

        // Personal information completeness (20 points)
        if (content.getPersonalInfo() != null) {
            ParsedContent.PersonalInfo info = content.getPersonalInfo();
            if (info.getFullName() != null && !info.getFullName().isEmpty()) score += 5;
            if (info.getEmail() != null && !info.getEmail().isEmpty()) score += 5;
            if (info.getPhone() != null && !info.getPhone().isEmpty()) score += 5;
            if (info.getLinkedinUrl() != null && !info.getLinkedinUrl().isEmpty()) score += 5;
        }

        // Professional summary (15 points)
        if (content.getProfessionalSummary() != null && 
            content.getProfessionalSummary().length() > 50) {
            score += 15;
        }

        // Work experience (25 points)
        if (content.getWorkExperience() != null && !content.getWorkExperience().isEmpty()) {
            score += Math.min(25, content.getWorkExperience().size() * 8);
        }

        // Education (15 points)
        if (content.getEducation() != null && !content.getEducation().isEmpty()) {
            score += 15;
        }

        // Skills (15 points)
        if (content.getSkills() != null && content.getSkills().getTechnical() != null) {
            int skillCount = content.getSkills().getTechnical().size();
            score += Math.min(15, skillCount * 2);
        }

        // Additional sections (10 points)
        int additionalSections = 0;
        if (content.getCertifications() != null && !content.getCertifications().isEmpty()) additionalSections++;
        if (content.getProjects() != null && !content.getProjects().isEmpty()) additionalSections++;
        if (content.getLanguages() != null && !content.getLanguages().isEmpty()) additionalSections++;
        
        score += Math.min(10, additionalSections * 3);

        return Math.min(score, maxScore);
    }

    /**
     * Calculate ATS compatibility score
     */
    private int calculateATSCompatibility(ParsedContent content) {
        int score = 0;

        // Check for standard section headers
        if (content.getWorkExperience() != null && !content.getWorkExperience().isEmpty()) score += 25;
        if (content.getEducation() != null && !content.getEducation().isEmpty()) score += 20;
        if (content.getSkills() != null) score += 20;

        // Check for contact information
        if (content.getPersonalInfo() != null) {
            ParsedContent.PersonalInfo info = content.getPersonalInfo();
            if (info.getEmail() != null) score += 15;
            if (info.getPhone() != null) score += 10;
        }

        // Check for keywords and industry terms
        if (content.getSkills() != null && content.getSkills().getTechnical() != null) {
            int keywordCount = content.getSkills().getTechnical().size();
            score += Math.min(10, keywordCount);
        }

        return Math.min(score, 100);
    }

    /**
     * Calculate readability score
     */
    private int calculateReadabilityScore(ParsedContent content) {
        int score = 80; // Base score

        // Check for clear structure
        if (content.getProfessionalSummary() != null && 
            content.getProfessionalSummary().length() > 200) {
            score -= 10; // Too long summary
        }

        // Check work experience descriptions
        if (content.getWorkExperience() != null) {
            for (var experience : content.getWorkExperience()) {
                if (experience.getDescription() != null && 
                    experience.getDescription().length() > 500) {
                    score -= 5; // Too verbose
                }
            }
        }

        return Math.max(score, 0);
    }

    /**
     * Identify resume strengths
     */
    private List<String> identifyStrengths(ParsedContent content) {
        List<String> strengths = new ArrayList<>();

        if (content.getPersonalInfo() != null) {
            ParsedContent.PersonalInfo info = content.getPersonalInfo();
            if (info.getLinkedinUrl() != null) {
                strengths.add("LinkedIn profile included");
            }
            if (info.getGithubUrl() != null) {
                strengths.add("GitHub profile showcases technical skills");
            }
        }

        if (content.getProfessionalSummary() != null && 
            content.getProfessionalSummary().length() > 100) {
            strengths.add("Comprehensive professional summary");
        }

        if (content.getWorkExperience() != null && content.getWorkExperience().size() >= 3) {
            strengths.add("Solid work experience history");
        }

        if (content.getSkills() != null && content.getSkills().getTechnical() != null &&
            content.getSkills().getTechnical().size() >= 8) {
            strengths.add("Diverse technical skill set");
        }

        if (content.getCertifications() != null && !content.getCertifications().isEmpty()) {
            strengths.add("Professional certifications demonstrate commitment");
        }

        if (content.getProjects() != null && !content.getProjects().isEmpty()) {
            strengths.add("Project experience shows practical application");
        }

        return strengths;
    }

    /**
     * Identify areas for improvement
     */
    private List<String> identifyImprovements(ParsedContent content) {
        List<String> improvements = new ArrayList<>();

        if (content.getPersonalInfo() == null || content.getPersonalInfo().getLinkedinUrl() == null) {
            improvements.add("Add LinkedIn profile URL");
        }

        if (content.getProfessionalSummary() == null || 
            content.getProfessionalSummary().length() < 50) {
            improvements.add("Add a compelling professional summary");
        }

        if (content.getSkills() == null || content.getSkills().getTechnical() == null ||
            content.getSkills().getTechnical().size() < 5) {
            improvements.add("Include more relevant technical skills");
        }

        if (content.getWorkExperience() != null) {
            boolean hasQuantifiableAchievements = content.getWorkExperience().stream()
                    .anyMatch(exp -> exp.getAchievements() != null && !exp.getAchievements().isEmpty());
            
            if (!hasQuantifiableAchievements) {
                improvements.add("Add quantifiable achievements to work experience");
            }
        }

        if (content.getCertifications() == null || content.getCertifications().isEmpty()) {
            improvements.add("Consider adding relevant certifications");
        }

        if (content.getEducation() != null) {
            boolean hasRelevantCoursework = content.getEducation().stream()
                    .anyMatch(edu -> edu.getRelevantCoursework() != null && !edu.getRelevantCoursework().isEmpty());
            
            if (!hasRelevantCoursework) {
                improvements.add("Include relevant coursework in education section");
            }
        }

        return improvements;
    }

    /**
     * Analyze keywords in resume
     */
    private AIAnalysis.KeywordAnalysis analyzeKeywords(ParsedContent content) {
        AIAnalysis.KeywordAnalysis keywordAnalysis = new AIAnalysis.KeywordAnalysis();

        Set<String> allKeywords = new HashSet<>();
        Map<String, Integer> keywordDensity = new HashMap<>();

        // Extract keywords from skills
        if (content.getSkills() != null && content.getSkills().getTechnical() != null) {
            for (String skill : content.getSkills().getTechnical()) {
                allKeywords.add(skill.toLowerCase());
                keywordDensity.put(skill.toLowerCase(), keywordDensity.getOrDefault(skill.toLowerCase(), 0) + 1);
            }
        }

        // Extract keywords from work experience
        if (content.getWorkExperience() != null) {
            for (var experience : content.getWorkExperience()) {
                if (experience.getTechnologies() != null) {
                    for (String tech : experience.getTechnologies()) {
                        allKeywords.add(tech.toLowerCase());
                        keywordDensity.put(tech.toLowerCase(), keywordDensity.getOrDefault(tech.toLowerCase(), 0) + 1);
                    }
                }
            }
        }

        keywordAnalysis.setTotalKeywords(allKeywords.size());
        keywordAnalysis.setIndustryRelevant((int) (allKeywords.size() * 0.7)); // Assume 70% are industry relevant
        keywordAnalysis.setKeywordDensity(keywordDensity);

        // Suggest missing keywords (placeholder)
        List<String> missingKeywords = Arrays.asList(
                "agile", "scrum", "ci/cd", "cloud", "microservices", "api", "database", "testing"
        );
        keywordAnalysis.setMissing(missingKeywords);

        return keywordAnalysis;
    }

    /**
     * Calculate scores for individual sections
     */
    private Map<String, Integer> calculateSectionScores(ParsedContent content) {
        Map<String, Integer> sectionScores = new HashMap<>();

        // Personal Information
        int personalInfoScore = 0;
        if (content.getPersonalInfo() != null) {
            ParsedContent.PersonalInfo info = content.getPersonalInfo();
            if (info.getFullName() != null) personalInfoScore += 25;
            if (info.getEmail() != null) personalInfoScore += 25;
            if (info.getPhone() != null) personalInfoScore += 25;
            if (info.getLinkedinUrl() != null) personalInfoScore += 25;
        }
        sectionScores.put("personalInfo", personalInfoScore);

        // Professional Summary
        int summaryScore = 0;
        if (content.getProfessionalSummary() != null) {
            int length = content.getProfessionalSummary().length();
            if (length > 50 && length < 300) summaryScore = 100;
            else if (length >= 300) summaryScore = 80;
            else summaryScore = 50;
        }
        sectionScores.put("professionalSummary", summaryScore);

        // Work Experience
        int experienceScore = 0;
        if (content.getWorkExperience() != null && !content.getWorkExperience().isEmpty()) {
            experienceScore = Math.min(100, content.getWorkExperience().size() * 30);
        }
        sectionScores.put("workExperience", experienceScore);

        // Education
        int educationScore = content.getEducation() != null && !content.getEducation().isEmpty() ? 100 : 0;
        sectionScores.put("education", educationScore);

        // Skills
        int skillsScore = 0;
        if (content.getSkills() != null && content.getSkills().getTechnical() != null) {
            skillsScore = Math.min(100, content.getSkills().getTechnical().size() * 10);
        }
        sectionScores.put("skills", skillsScore);

        return sectionScores;
    }
}
