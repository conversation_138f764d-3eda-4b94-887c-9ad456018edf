package com.careeralgo.controller;

import com.careeralgo.model.InterviewPrep;
import com.careeralgo.service.InterviewPrepService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for interview preparation functionality
 */
@RestController
@RequestMapping("/interview-prep")
@Tag(name = "Interview Preparation", description = "APIs for interview preparation and practice")
public class InterviewPrepController {

    @Autowired
    private InterviewPrepService interviewPrepService;

    /**
     * Get user's interview prep sessions
     */
    @GetMapping("/sessions")
    @Operation(summary = "Get interview prep sessions", description = "Get paginated list of user's interview preparation sessions")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Sessions retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "400", description = "Invalid pagination parameters")
    })
    public ResponseEntity<Page<InterviewPrep>> getUserSessions(
            Authentication authentication,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Filter by session type") @RequestParam(required = false) InterviewPrep.SessionType type,
            @Parameter(description = "Filter by completion status") @RequestParam(required = false) Boolean completed) {
        
        Page<InterviewPrep> sessions = interviewPrepService.getUserSessions(authentication, page, size, type, completed);
        return ResponseEntity.ok(sessions);
    }

    /**
     * Get specific session by ID
     */
    @GetMapping("/sessions/{sessionId}")
    @Operation(summary = "Get session by ID", description = "Get detailed information about a specific interview prep session")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Session retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Session not found")
    })
    public ResponseEntity<InterviewPrep> getSessionById(
            Authentication authentication,
            @Parameter(description = "Session ID") @PathVariable String sessionId) {
        
        InterviewPrep session = interviewPrepService.getSessionById(authentication, sessionId);
        return ResponseEntity.ok(session);
    }

    /**
     * Create new interview prep session
     */
    @PostMapping("/sessions")
    @Operation(summary = "Create interview prep session", description = "Create a new interview preparation session")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Session created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid session data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<InterviewPrep> createSession(
            Authentication authentication,
            @Valid @RequestBody InterviewPrepService.CreateSessionRequest request) {
        
        InterviewPrep session = interviewPrepService.createSession(authentication, request);
        return ResponseEntity.status(201).body(session);
    }

    /**
     * Answer a question in the session
     */
    @PostMapping("/sessions/{sessionId}/answer")
    @Operation(summary = "Answer question", description = "Submit an answer to a question in the interview prep session")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Answer submitted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid answer data or session completed"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Session or question not found")
    })
    public ResponseEntity<InterviewPrep> answerQuestion(
            Authentication authentication,
            @Parameter(description = "Session ID") @PathVariable String sessionId,
            @Valid @RequestBody InterviewPrepService.AnswerQuestionRequest request) {
        
        InterviewPrep session = interviewPrepService.answerQuestion(authentication, sessionId, request);
        return ResponseEntity.ok(session);
    }

    /**
     * Complete interview prep session
     */
    @PostMapping("/sessions/{sessionId}/complete")
    @Operation(summary = "Complete session", description = "Mark interview prep session as completed and generate feedback")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Session completed successfully"),
            @ApiResponse(responseCode = "400", description = "Session already completed"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Session not found")
    })
    public ResponseEntity<InterviewPrep> completeSession(
            Authentication authentication,
            @Parameter(description = "Session ID") @PathVariable String sessionId) {
        
        InterviewPrep session = interviewPrepService.completeSession(authentication, sessionId);
        return ResponseEntity.ok(session);
    }

    /**
     * Delete interview prep session
     */
    @DeleteMapping("/sessions/{sessionId}")
    @Operation(summary = "Delete session", description = "Delete an interview preparation session")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Session deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Session not found")
    })
    public ResponseEntity<Void> deleteSession(
            Authentication authentication,
            @Parameter(description = "Session ID") @PathVariable String sessionId) {
        
        interviewPrepService.deleteSession(authentication, sessionId);
        return ResponseEntity.noContent().build();
    }

    /**
     * Get session analytics
     */
    @GetMapping("/analytics")
    @Operation(summary = "Get session analytics", description = "Get analytics and statistics for user's interview preparation sessions")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Analytics retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<InterviewPrepService.SessionAnalytics> getSessionAnalytics(Authentication authentication) {
        InterviewPrepService.SessionAnalytics analytics = interviewPrepService.getSessionAnalytics(authentication);
        return ResponseEntity.ok(analytics);
    }

    /**
     * Get question bank
     */
    @GetMapping("/questions")
    @Operation(summary = "Get question bank", description = "Get interview questions filtered by category and difficulty")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Questions retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid filter parameters")
    })
    public ResponseEntity<List<InterviewPrep.InterviewQuestion>> getQuestionBank(
            @Parameter(description = "Question category") @RequestParam(required = false) InterviewPrep.QuestionCategory category,
            @Parameter(description = "Question difficulty") @RequestParam(required = false) InterviewPrep.QuestionDifficulty difficulty,
            @Parameter(description = "Number of questions to return") @RequestParam(defaultValue = "20") int limit) {
        
        List<InterviewPrep.InterviewQuestion> questions = interviewPrepService.getQuestionBank(category, difficulty, limit);
        return ResponseEntity.ok(questions);
    }

    /**
     * Get company research
     */
    @GetMapping("/company-research/{jobId}")
    @Operation(summary = "Get company research", description = "Get company research information for a specific job")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Company research retrieved successfully"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    public ResponseEntity<InterviewPrep.CompanyResearch> getCompanyResearch(
            @Parameter(description = "Job ID") @PathVariable String jobId) {
        
        InterviewPrep.CompanyResearch research = interviewPrepService.getCompanyResearch(jobId);
        return ResponseEntity.ok(research);
    }

    /**
     * Get session types
     */
    @GetMapping("/session-types")
    @Operation(summary = "Get session types", description = "Get all available interview preparation session types")
    @ApiResponse(responseCode = "200", description = "Session types retrieved successfully")
    public ResponseEntity<InterviewPrep.SessionType[]> getSessionTypes() {
        return ResponseEntity.ok(InterviewPrep.SessionType.values());
    }

    /**
     * Get question categories
     */
    @GetMapping("/question-categories")
    @Operation(summary = "Get question categories", description = "Get all available question categories")
    @ApiResponse(responseCode = "200", description = "Question categories retrieved successfully")
    public ResponseEntity<InterviewPrep.QuestionCategory[]> getQuestionCategories() {
        return ResponseEntity.ok(InterviewPrep.QuestionCategory.values());
    }

    /**
     * Get question difficulties
     */
    @GetMapping("/question-difficulties")
    @Operation(summary = "Get question difficulties", description = "Get all available question difficulty levels")
    @ApiResponse(responseCode = "200", description = "Question difficulties retrieved successfully")
    public ResponseEntity<InterviewPrep.QuestionDifficulty[]> getQuestionDifficulties() {
        return ResponseEntity.ok(InterviewPrep.QuestionDifficulty.values());
    }

    /**
     * Get interview tips
     */
    @GetMapping("/tips")
    @Operation(summary = "Get interview tips", description = "Get general interview tips and best practices")
    @ApiResponse(responseCode = "200", description = "Interview tips retrieved successfully")
    public ResponseEntity<Map<String, Object>> getInterviewTips(
            @Parameter(description = "Interview type") @RequestParam(required = false) String interviewType) {
        
        Map<String, Object> tips = Map.of(
                "general", List.of(
                        "Research the company thoroughly",
                        "Practice your elevator pitch",
                        "Prepare questions to ask the interviewer",
                        "Dress appropriately for the company culture",
                        "Arrive 10-15 minutes early"
                ),
                "behavioral", List.of(
                        "Use the STAR method (Situation, Task, Action, Result)",
                        "Prepare specific examples from your experience",
                        "Focus on your role and contributions",
                        "Be honest about challenges and what you learned"
                ),
                "technical", List.of(
                        "Practice coding problems on a whiteboard",
                        "Explain your thought process out loud",
                        "Ask clarifying questions before starting",
                        "Test your solution with examples",
                        "Discuss time and space complexity"
                ),
                "virtual", List.of(
                        "Test your technology beforehand",
                        "Ensure good lighting and camera angle",
                        "Minimize background distractions",
                        "Have a backup plan for technical issues",
                        "Maintain eye contact with the camera"
                )
        );
        
        return ResponseEntity.ok(tips);
    }

    /**
     * Get practice recommendations
     */
    @GetMapping("/recommendations")
    @Operation(summary = "Get practice recommendations", description = "Get personalized interview practice recommendations")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Recommendations retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, Object>> getPracticeRecommendations(Authentication authentication) {
        // TODO: Implement personalized recommendations based on user's history
        Map<String, Object> recommendations = Map.of(
                "nextSession", Map.of(
                        "type", "BEHAVIORAL_PREP",
                        "reason", "You haven't practiced behavioral questions recently",
                        "estimatedTime", "30 minutes"
                ),
                "focusAreas", List.of(
                        "Technical problem-solving",
                        "Company research",
                        "Salary negotiation"
                ),
                "weeklyGoal", Map.of(
                        "sessions", 3,
                        "questionsAnswered", 25,
                        "averageScore", 80
                ),
                "upcomingInterviews", List.of(
                        // Would be populated from user's applications
                )
        );
        
        return ResponseEntity.ok(recommendations);
    }
}
