package com.careeralgo.dto;

import com.careeralgo.constant.SubscriptionPlan;
import com.careeralgo.model.Subscription;

import java.time.LocalDateTime;

/**
 * DTO for subscription information
 */
public class SubscriptionDto {

    private SubscriptionPlan plan;
    private Subscription.SubscriptionStatus status;
    private LocalDateTime startDate;
    private LocalDateTime endDate;

    // Constructors
    public SubscriptionDto() {}

    public SubscriptionDto(Subscription subscription) {
        this.plan = subscription.getPlan();
        this.status = subscription.getStatus();
        this.startDate = subscription.getStartDate();
        this.endDate = subscription.getEndDate();
    }

    // Getters and Setters
    public SubscriptionPlan getPlan() {
        return plan;
    }

    public void setPlan(SubscriptionPlan plan) {
        this.plan = plan;
    }

    public Subscription.SubscriptionStatus getStatus() {
        return status;
    }

    public void setStatus(Subscription.SubscriptionStatus status) {
        this.status = status;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }

    public boolean isActive() {
        return status == Subscription.SubscriptionStatus.ACTIVE && 
               LocalDateTime.now().isBefore(endDate);
    }

    public boolean isPremium() {
        return isActive() && (plan == SubscriptionPlan.PRO || plan == SubscriptionPlan.ENTERPRISE);
    }
}
