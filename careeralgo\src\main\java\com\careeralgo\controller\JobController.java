package com.careeralgo.controller;

import com.careeralgo.constant.ExperienceLevel;
import com.careeralgo.dto.JobResponse;
import com.careeralgo.dto.JobSearchRequest;
import com.careeralgo.service.JobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for job search and management operations
 */
@RestController
@RequestMapping("/jobs")
@Tag(name = "Job Management", description = "APIs for job search, filtering, and recommendations")
public class JobController {

    @Autowired
    private JobService jobService;

    /**
     * Get all jobs with pagination and sorting
     */
    @GetMapping
    @Operation(summary = "Get all jobs", description = "Retrieve paginated list of all active jobs")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Jobs retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid pagination or sorting parameters")
    })
    public ResponseEntity<Page<JobResponse>> getAllJobs(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "postedDate") String sortBy,
            @Parameter(description = "Sort direction (asc/desc)") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Page<JobResponse> jobs = jobService.getAllJobs(page, size, sortBy, sortDir);
        return ResponseEntity.ok(jobs);
    }

    /**
     * Get job by ID
     */
    @GetMapping("/{jobId}")
    @Operation(summary = "Get job by ID", description = "Retrieve a specific job by its ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job retrieved successfully"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    public ResponseEntity<JobResponse> getJobById(
            @Parameter(description = "Job ID") @PathVariable String jobId) {
        
        JobResponse job = jobService.getJobById(jobId);
        return ResponseEntity.ok(job);
    }

    /**
     * Search jobs with filters
     */
    @PostMapping("/search")
    @Operation(summary = "Search jobs", description = "Search jobs with advanced filters")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search completed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid search parameters")
    })
    public ResponseEntity<Page<JobResponse>> searchJobs(
            @RequestBody JobSearchRequest searchRequest,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Page<JobResponse> jobs = jobService.searchJobs(searchRequest, page, size);
        return ResponseEntity.ok(jobs);
    }

    /**
     * Quick search jobs by keyword
     */
    @GetMapping("/search")
    @Operation(summary = "Quick job search", description = "Quick search jobs by keyword")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search completed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid search parameters")
    })
    public ResponseEntity<Page<JobResponse>> quickSearchJobs(
            @Parameter(description = "Search keyword") @RequestParam String q,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        if (q == null || q.trim().length() < 2) {
            return ResponseEntity.badRequest().build();
        }
        
        JobSearchRequest searchRequest = new JobSearchRequest();
        searchRequest.setKeyword(q.trim());
        
        Page<JobResponse> jobs = jobService.searchJobs(searchRequest, page, size);
        return ResponseEntity.ok(jobs);
    }

    /**
     * Get jobs by location
     */
    @GetMapping("/location/{location}")
    @Operation(summary = "Get jobs by location", description = "Retrieve jobs filtered by location")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Jobs retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid location parameter")
    })
    public ResponseEntity<Page<JobResponse>> getJobsByLocation(
            @Parameter(description = "Location (city, state, or country)") @PathVariable String location,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Page<JobResponse> jobs = jobService.getJobsByLocation(location, page, size);
        return ResponseEntity.ok(jobs);
    }

    /**
     * Get remote jobs
     */
    @GetMapping("/remote")
    @Operation(summary = "Get remote jobs", description = "Retrieve all remote job opportunities")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Remote jobs retrieved successfully")
    })
    public ResponseEntity<Page<JobResponse>> getRemoteJobs(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Page<JobResponse> jobs = jobService.getRemoteJobs(page, size);
        return ResponseEntity.ok(jobs);
    }

    /**
     * Get jobs by experience level
     */
    @GetMapping("/experience/{level}")
    @Operation(summary = "Get jobs by experience level", description = "Retrieve jobs filtered by experience level")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Jobs retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid experience level")
    })
    public ResponseEntity<Page<JobResponse>> getJobsByExperienceLevel(
            @Parameter(description = "Experience level") @PathVariable ExperienceLevel level,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Page<JobResponse> jobs = jobService.getJobsByExperienceLevel(level, page, size);
        return ResponseEntity.ok(jobs);
    }

    /**
     * Get jobs by company
     */
    @GetMapping("/company/{companyName}")
    @Operation(summary = "Get jobs by company", description = "Retrieve jobs from a specific company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Jobs retrieved successfully")
    })
    public ResponseEntity<Page<JobResponse>> getJobsByCompany(
            @Parameter(description = "Company name") @PathVariable String companyName,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Page<JobResponse> jobs = jobService.getJobsByCompany(companyName, page, size);
        return ResponseEntity.ok(jobs);
    }

    /**
     * Get featured jobs
     */
    @GetMapping("/featured")
    @Operation(summary = "Get featured jobs", description = "Retrieve featured job listings")
    @ApiResponse(responseCode = "200", description = "Featured jobs retrieved successfully")
    public ResponseEntity<List<JobResponse>> getFeaturedJobs() {
        List<JobResponse> jobs = jobService.getFeaturedJobs();
        return ResponseEntity.ok(jobs);
    }

    /**
     * Get trending jobs
     */
    @GetMapping("/trending")
    @Operation(summary = "Get trending jobs", description = "Retrieve trending job listings based on view count")
    @ApiResponse(responseCode = "200", description = "Trending jobs retrieved successfully")
    public ResponseEntity<List<JobResponse>> getTrendingJobs(
            @Parameter(description = "Number of trending jobs to return") @RequestParam(defaultValue = "10") int limit) {
        
        List<JobResponse> jobs = jobService.getTrendingJobs(limit);
        return ResponseEntity.ok(jobs);
    }

    /**
     * Get similar jobs
     */
    @GetMapping("/{jobId}/similar")
    @Operation(summary = "Get similar jobs", description = "Retrieve jobs similar to the specified job")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Similar jobs retrieved successfully"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    public ResponseEntity<List<JobResponse>> getSimilarJobs(
            @Parameter(description = "Job ID") @PathVariable String jobId,
            @Parameter(description = "Number of similar jobs to return") @RequestParam(defaultValue = "5") int limit) {
        
        List<JobResponse> jobs = jobService.getSimilarJobs(jobId, limit);
        return ResponseEntity.ok(jobs);
    }

    /**
     * Get personalized job recommendations
     */
    @GetMapping("/recommendations")
    @Operation(summary = "Get personalized recommendations", description = "Get AI-powered job recommendations based on user profile")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Recommendations retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<List<JobResponse>> getPersonalizedRecommendations(
            Authentication authentication,
            @Parameter(description = "Number of recommendations to return") @RequestParam(defaultValue = "10") int limit) {
        
        List<JobResponse> jobs = jobService.getPersonalizedRecommendations(authentication, limit);
        return ResponseEntity.ok(jobs);
    }

    /**
     * Get jobs matching user profile
     */
    @GetMapping("/matching")
    @Operation(summary = "Get matching jobs", description = "Get jobs that match the user's profile and preferences")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Matching jobs retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Page<JobResponse>> getMatchingJobs(
            Authentication authentication,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Page<JobResponse> jobs = jobService.getMatchingJobs(authentication, page, size);
        return ResponseEntity.ok(jobs);
    }

    /**
     * Get job statistics
     */
    @GetMapping("/stats")
    @Operation(summary = "Get job statistics", description = "Retrieve job market statistics and metrics")
    @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully")
    public ResponseEntity<JobService.JobStatsResponse> getJobStatistics() {
        JobService.JobStatsResponse stats = jobService.getJobStatistics();
        return ResponseEntity.ok(stats);
    }

    /**
     * Save job for later
     */
    @PostMapping("/{jobId}/save")
    @Operation(summary = "Save job", description = "Bookmark a job for later viewing")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job saved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    public ResponseEntity<String> saveJobForLater(
            Authentication authentication,
            @Parameter(description = "Job ID") @PathVariable String jobId) {
        
        jobService.saveJobForLater(authentication, jobId);
        return ResponseEntity.ok("Job saved successfully");
    }

    /**
     * Remove saved job
     */
    @DeleteMapping("/{jobId}/save")
    @Operation(summary = "Remove saved job", description = "Remove a job from saved/bookmarked jobs")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Saved job removed successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<String> removeSavedJob(
            Authentication authentication,
            @Parameter(description = "Job ID") @PathVariable String jobId) {
        
        jobService.removeSavedJob(authentication, jobId);
        return ResponseEntity.ok("Saved job removed successfully");
    }
}
