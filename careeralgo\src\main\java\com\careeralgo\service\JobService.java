package com.careeralgo.service;

import com.careeralgo.constant.ExperienceLevel;
import com.careeralgo.dto.JobResponse;
import com.careeralgo.dto.JobSearchRequest;
import com.careeralgo.model.Job;
import com.careeralgo.model.User;
import com.careeralgo.repository.JobRepository;
import com.careeralgo.repository.UserRepository;
import com.careeralgo.exception.ResourceNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for job management and search operations
 */
@Service
public class JobService {

    private static final Logger logger = LoggerFactory.getLogger(JobService.class);

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AIJobMatchingService aiJobMatchingService;

    /**
     * Get all active jobs with pagination
     */
    public Page<JobResponse> getAllJobs(int page, int size, String sortBy, String sortDir) {
        Sort.Direction direction = "desc".equalsIgnoreCase(sortDir) ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        Page<Job> jobs = jobRepository.findByIsActiveTrueOrderByPostedDateDesc(pageable);
        
        return jobs.map(JobResponse::new);
    }

    /**
     * Get job by ID
     */
    public JobResponse getJobById(String jobId) {
        Job job = jobRepository.findById(jobId)
                .orElseThrow(() -> new ResourceNotFoundException("Job not found: " + jobId));
        
        if (!job.isActive()) {
            throw new ResourceNotFoundException("Job not found: " + jobId);
        }
        
        // Increment view count
        job.incrementViewCount();
        jobRepository.save(job);
        
        return new JobResponse(job);
    }

    /**
     * Search jobs with filters
     */
    public Page<JobResponse> searchJobs(JobSearchRequest searchRequest, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("postedDate").descending());
        
        Page<Job> jobs;
        
        if (searchRequest.hasAdvancedFilters()) {
            jobs = jobRepository.advancedJobSearch(
                    searchRequest.getKeyword(),
                    searchRequest.getLocation(),
                    searchRequest.getExperienceLevel(),
                    searchRequest.getIsRemote(),
                    pageable
            );
        } else if (searchRequest.getKeyword() != null && !searchRequest.getKeyword().trim().isEmpty()) {
            jobs = jobRepository.searchJobs(searchRequest.getKeyword(), pageable);
        } else {
            jobs = jobRepository.findByIsActiveTrueOrderByPostedDateDesc(pageable);
        }
        
        return jobs.map(JobResponse::new);
    }

    /**
     * Get jobs by location
     */
    public Page<JobResponse> getJobsByLocation(String location, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("postedDate").descending());
        Page<Job> jobs = jobRepository.findByLocation(location, pageable);
        return jobs.map(JobResponse::new);
    }

    /**
     * Get remote jobs
     */
    public Page<JobResponse> getRemoteJobs(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("postedDate").descending());
        Page<Job> jobs = jobRepository.findRemoteJobs(pageable);
        return jobs.map(JobResponse::new);
    }

    /**
     * Get jobs by experience level
     */
    public Page<JobResponse> getJobsByExperienceLevel(ExperienceLevel experienceLevel, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("postedDate").descending());
        Page<Job> jobs = jobRepository.findByExperienceLevelAndIsActiveTrueOrderByPostedDateDesc(
                experienceLevel, pageable);
        return jobs.map(JobResponse::new);
    }

    /**
     * Get jobs by company
     */
    public Page<JobResponse> getJobsByCompany(String companyName, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("postedDate").descending());
        Page<Job> jobs = jobRepository.findByCompanyName(companyName, pageable);
        return jobs.map(JobResponse::new);
    }

    /**
     * Get jobs by skills
     */
    public Page<JobResponse> getJobsBySkills(List<String> skills, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("postedDate").descending());
        Page<Job> jobs = jobRepository.findBySkills(skills, pageable);
        return jobs.map(JobResponse::new);
    }

    /**
     * Get featured jobs
     */
    public List<JobResponse> getFeaturedJobs() {
        List<Job> featuredJobs = jobRepository.findByIsFeaturedTrueAndIsActiveTrueOrderByPostedDateDesc();
        return featuredJobs.stream()
                .map(JobResponse::new)
                .collect(Collectors.toList());
    }

    /**
     * Get trending jobs (high view count)
     */
    public List<JobResponse> getTrendingJobs(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<Job> trendingJobs = jobRepository.findTrendingJobs(pageable);
        return trendingJobs.stream()
                .map(JobResponse::new)
                .collect(Collectors.toList());
    }

    /**
     * Get similar jobs
     */
    public List<JobResponse> getSimilarJobs(String jobId, int limit) {
        Job job = jobRepository.findById(jobId)
                .orElseThrow(() -> new ResourceNotFoundException("Job not found: " + jobId));
        
        Pageable pageable = PageRequest.of(0, limit);
        List<Job> similarJobs = jobRepository.findSimilarJobs(
                job.getTitle(), job.getSkills(), jobId, pageable);
        
        return similarJobs.stream()
                .map(JobResponse::new)
                .collect(Collectors.toList());
    }

    /**
     * Get personalized job recommendations for user
     */
    public List<JobResponse> getPersonalizedRecommendations(Authentication authentication, int limit) {
        String userId = getUserId(authentication);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Use AI service to get personalized recommendations
        List<Job> recommendedJobs = aiJobMatchingService.getPersonalizedRecommendations(user, limit);
        
        return recommendedJobs.stream()
                .map(JobResponse::new)
                .collect(Collectors.toList());
    }

    /**
     * Get jobs matching user's profile
     */
    public Page<JobResponse> getMatchingJobs(Authentication authentication, int page, int size) {
        String userId = getUserId(authentication);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Build search criteria based on user profile
        JobSearchRequest searchRequest = buildSearchFromUserProfile(user);
        
        return searchJobs(searchRequest, page, size);
    }

    /**
     * Get job statistics
     */
    public JobStatsResponse getJobStatistics() {
        JobStatsResponse stats = new JobStatsResponse();
        
        stats.setTotalActiveJobs(jobRepository.countByIsActiveTrue());
        stats.setRemoteJobs(jobRepository.findRemoteJobs(PageRequest.of(0, 1)).getTotalElements());
        stats.setFeaturedJobs((long) jobRepository.findByIsFeaturedTrueAndIsActiveTrueOrderByPostedDateDesc().size());
        
        // Get jobs by experience level
        for (ExperienceLevel level : ExperienceLevel.values()) {
            long count = jobRepository.findByExperienceLevelAndIsActiveTrueOrderByPostedDateDesc(
                    level, PageRequest.of(0, 1)).getTotalElements();
            stats.getJobsByExperienceLevel().put(level.getValue(), count);
        }
        
        return stats;
    }

    /**
     * Save job for later (bookmark)
     */
    public void saveJobForLater(Authentication authentication, String jobId) {
        String userId = getUserId(authentication);
        
        // Verify job exists
        Job job = jobRepository.findById(jobId)
                .orElseThrow(() -> new ResourceNotFoundException("Job not found: " + jobId));
        
        // TODO: Implement job bookmarking functionality
        // This would require a separate SavedJob entity or adding to User model
        
        logger.info("User {} saved job {} for later", userId, jobId);
    }

    /**
     * Remove saved job
     */
    public void removeSavedJob(Authentication authentication, String jobId) {
        String userId = getUserId(authentication);
        
        // TODO: Implement removing saved job functionality
        
        logger.info("User {} removed saved job {}", userId, jobId);
    }

    // Helper methods
    
    private String getUserId(Authentication authentication) {
        if (authentication.getPrincipal() instanceof Jwt jwt) {
            String clerkUserId = jwt.getSubject();
            User user = userRepository.findByClerkUserId(clerkUserId)
                    .orElseThrow(() -> new ResourceNotFoundException("User not found"));
            return user.getId();
        }
        throw new IllegalArgumentException("Invalid authentication type");
    }

    private JobSearchRequest buildSearchFromUserProfile(User user) {
        JobSearchRequest searchRequest = new JobSearchRequest();
        
        if (user.getProfile() != null) {
            searchRequest.setKeyword(user.getProfile().getJobTitle());
            
            if (user.getProfile().getLocation() != null) {
                searchRequest.setLocation(user.getProfile().getLocation().getCity());
            }
            
            searchRequest.setExperienceLevel(user.getProfile().getExperienceLevel());
            
            if (user.getProfile().getRemotePreference() != null) {
                searchRequest.setIsRemote(user.getProfile().getRemotePreference().toString().contains("REMOTE"));
            }
            
            searchRequest.setSkills(user.getProfile().getSkills());
        }
        
        return searchRequest;
    }

    /**
     * Job statistics response DTO
     */
    public static class JobStatsResponse {
        private Long totalActiveJobs;
        private Long remoteJobs;
        private Long featuredJobs;
        private java.util.Map<String, Long> jobsByExperienceLevel = new java.util.HashMap<>();

        // Getters and Setters
        public Long getTotalActiveJobs() {
            return totalActiveJobs;
        }

        public void setTotalActiveJobs(Long totalActiveJobs) {
            this.totalActiveJobs = totalActiveJobs;
        }

        public Long getRemoteJobs() {
            return remoteJobs;
        }

        public void setRemoteJobs(Long remoteJobs) {
            this.remoteJobs = remoteJobs;
        }

        public Long getFeaturedJobs() {
            return featuredJobs;
        }

        public void setFeaturedJobs(Long featuredJobs) {
            this.featuredJobs = featuredJobs;
        }

        public java.util.Map<String, Long> getJobsByExperienceLevel() {
            return jobsByExperienceLevel;
        }

        public void setJobsByExperienceLevel(java.util.Map<String, Long> jobsByExperienceLevel) {
            this.jobsByExperienceLevel = jobsByExperienceLevel;
        }
    }
}
