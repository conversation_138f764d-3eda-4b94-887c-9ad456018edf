package com.careeralgo.model;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Resume customization for specific job applications
 */
public class ResumeCustomization {
    
    private String jobId;
    private String jobTitle;
    private Map<String, Object> customizedSections;
    private Integer matchScore;
    private LocalDateTime createdAt;

    // Constructors
    public ResumeCustomization() {}

    public ResumeCustomization(String jobId, String jobTitle) {
        this.jobId = jobId;
        this.jobTitle = jobTitle;
        this.createdAt = LocalDateTime.now();
    }

    // Getters and Setters
    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public Map<String, Object> getCustomizedSections() {
        return customizedSections;
    }

    public void setCustomizedSections(Map<String, Object> customizedSections) {
        this.customizedSections = customizedSections;
    }

    public Integer getMatchScore() {
        return matchScore;
    }

    public void setMatchScore(Integer matchScore) {
        this.matchScore = matchScore;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public String getMatchGrade() {
        if (matchScore == null) return "N/A";
        if (matchScore >= 90) return "Excellent";
        if (matchScore >= 80) return "Good";
        if (matchScore >= 70) return "Fair";
        if (matchScore >= 60) return "Poor";
        return "Very Poor";
    }
}
