package com.careeralgo.service;

import com.careeralgo.model.Notification;
import com.careeralgo.repository.NotificationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Service for real-time notification delivery via WebSocket
 */
@Service
public class RealTimeNotificationService {

    private static final Logger logger = LoggerFactory.getLogger(RealTimeNotificationService.class);

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private WebSocketAuthService webSocketAuthService;

    /**
     * Send real-time notification to user
     */
    @Async
    public CompletableFuture<Boolean> sendNotificationToUser(String userId, Notification notification) {
        try {
            // Check if user has active WebSocket session
            if (!webSocketAuthService.hasActiveSession(userId)) {
                logger.debug("User {} has no active WebSocket session, skipping real-time notification", userId);
                return CompletableFuture.completedFuture(false);
            }

            // Prepare notification payload
            Map<String, Object> payload = createNotificationPayload(notification);

            // Send to user's personal queue
            String destination = "/user/" + userId + "/queue/notifications";
            messagingTemplate.convertAndSend(destination, payload);

            // Mark notification as delivered
            notification.markAsDelivered();
            notificationRepository.save(notification);

            logger.info("Real-time notification sent to user: {} via WebSocket", userId);
            return CompletableFuture.completedFuture(true);

        } catch (Exception e) {
            logger.error("Failed to send real-time notification to user: {}", userId, e);
            return CompletableFuture.completedFuture(false);
        }
    }

    /**
     * Send notification to all connected users (broadcast)
     */
    @Async
    public void broadcastNotification(String title, String message, Notification.NotificationType type) {
        try {
            Map<String, Object> payload = Map.of(
                    "type", "BROADCAST",
                    "notificationType", type.name(),
                    "title", title,
                    "message", message,
                    "timestamp", LocalDateTime.now().toString()
            );

            // Send to all subscribers of the broadcast topic
            messagingTemplate.convertAndSend("/topic/broadcast", payload);

            logger.info("Broadcast notification sent: {}", title);

        } catch (Exception e) {
            logger.error("Failed to send broadcast notification", e);
        }
    }

    /**
     * Send typing indicator for chat-like features
     */
    public void sendTypingIndicator(String userId, String targetUserId, boolean isTyping) {
        try {
            Map<String, Object> payload = Map.of(
                    "type", "TYPING_INDICATOR",
                    "userId", userId,
                    "isTyping", isTyping,
                    "timestamp", LocalDateTime.now().toString()
            );

            String destination = "/user/" + targetUserId + "/queue/typing";
            messagingTemplate.convertAndSend(destination, payload);

        } catch (Exception e) {
            logger.error("Failed to send typing indicator", e);
        }
    }

    /**
     * Send real-time update for application status changes
     */
    @Async
    public void sendApplicationUpdate(String userId, String applicationId, String newStatus, Map<String, Object> details) {
        try {
            if (!webSocketAuthService.hasActiveSession(userId)) {
                return;
            }

            Map<String, Object> payload = Map.of(
                    "type", "APPLICATION_UPDATE",
                    "applicationId", applicationId,
                    "newStatus", newStatus,
                    "details", details != null ? details : Map.of(),
                    "timestamp", LocalDateTime.now().toString()
            );

            String destination = "/user/" + userId + "/queue/application-updates";
            messagingTemplate.convertAndSend(destination, payload);

            logger.info("Application update sent to user: {} for application: {}", userId, applicationId);

        } catch (Exception e) {
            logger.error("Failed to send application update", e);
        }
    }

    /**
     * Send real-time job match notification
     */
    @Async
    public void sendJobMatchNotification(String userId, String jobId, int matchScore, Map<String, Object> jobDetails) {
        try {
            if (!webSocketAuthService.hasActiveSession(userId)) {
                return;
            }

            Map<String, Object> payload = Map.of(
                    "type", "JOB_MATCH",
                    "jobId", jobId,
                    "matchScore", matchScore,
                    "jobDetails", jobDetails != null ? jobDetails : Map.of(),
                    "timestamp", LocalDateTime.now().toString()
            );

            String destination = "/user/" + userId + "/queue/job-matches";
            messagingTemplate.convertAndSend(destination, payload);

            logger.info("Job match notification sent to user: {} for job: {} (score: {})", userId, jobId, matchScore);

        } catch (Exception e) {
            logger.error("Failed to send job match notification", e);
        }
    }

    /**
     * Send system status update
     */
    public void sendSystemStatus(String status, String message) {
        try {
            Map<String, Object> payload = Map.of(
                    "type", "SYSTEM_STATUS",
                    "status", status,
                    "message", message,
                    "timestamp", LocalDateTime.now().toString()
            );

            messagingTemplate.convertAndSend("/topic/system-status", payload);

            logger.info("System status update sent: {} - {}", status, message);

        } catch (Exception e) {
            logger.error("Failed to send system status update", e);
        }
    }

    /**
     * Send user activity update (for presence indicators)
     */
    public void sendUserActivityUpdate(String userId, String activity) {
        try {
            Map<String, Object> payload = Map.of(
                    "type", "USER_ACTIVITY",
                    "userId", userId,
                    "activity", activity,
                    "timestamp", LocalDateTime.now().toString()
            );

            // Send to user's connections or relevant channels
            messagingTemplate.convertAndSend("/topic/user-activity", payload);

        } catch (Exception e) {
            logger.error("Failed to send user activity update", e);
        }
    }

    /**
     * Process pending notifications (scheduled task)
     */
    @Scheduled(fixedRate = 30000) // Run every 30 seconds
    public void processPendingNotifications() {
        try {
            LocalDateTime now = LocalDateTime.now();
            List<Notification> pendingNotifications = notificationRepository.findNotificationsToDeliver(now);

            for (Notification notification : pendingNotifications) {
                // Only send to users with active WebSocket sessions
                if (webSocketAuthService.hasActiveSession(notification.getUserId())) {
                    sendNotificationToUser(notification.getUserId(), notification);
                }
            }

            if (!pendingNotifications.isEmpty()) {
                logger.info("Processed {} pending notifications", pendingNotifications.size());
            }

        } catch (Exception e) {
            logger.error("Error processing pending notifications", e);
        }
    }

    /**
     * Clean up expired notifications (scheduled task)
     */
    @Scheduled(fixedRate = 3600000) // Run every hour
    public void cleanupExpiredNotifications() {
        try {
            LocalDateTime now = LocalDateTime.now();
            notificationRepository.deleteExpiredNotifications(now);
            logger.info("Cleaned up expired notifications");

        } catch (Exception e) {
            logger.error("Error cleaning up expired notifications", e);
        }
    }

    /**
     * Send connection status to user
     */
    public void sendConnectionStatus(String userId, boolean connected) {
        try {
            Map<String, Object> payload = Map.of(
                    "type", "CONNECTION_STATUS",
                    "connected", connected,
                    "timestamp", LocalDateTime.now().toString()
            );

            String destination = "/user/" + userId + "/queue/connection-status";
            messagingTemplate.convertAndSend(destination, payload);

        } catch (Exception e) {
            logger.error("Failed to send connection status", e);
        }
    }

    /**
     * Get real-time statistics
     */
    public Map<String, Object> getRealTimeStats() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("activeConnections", webSocketAuthService.getActiveSessionCount());
        stats.put("pendingNotifications", notificationRepository.findNotificationsToDeliver(LocalDateTime.now()).size());
        stats.put("timestamp", LocalDateTime.now().toString());
        
        return stats;
    }

    // Helper methods

    private Map<String, Object> createNotificationPayload(Notification notification) {
        Map<String, Object> payload = new HashMap<>();
        
        payload.put("id", notification.getId());
        payload.put("type", "NOTIFICATION");
        payload.put("notificationType", notification.getType().name());
        payload.put("title", notification.getTitle());
        payload.put("message", notification.getMessage());
        payload.put("actionUrl", notification.getActionUrl());
        payload.put("priority", notification.getPriority().name());
        payload.put("data", notification.getData());
        payload.put("timestamp", notification.getCreatedAt().toString());
        
        return payload;
    }
}
