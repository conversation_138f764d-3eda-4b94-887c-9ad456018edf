package com.careeralgo.repository;

import com.careeralgo.constant.UserRole;
import com.careeralgo.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for User document operations
 */
@Repository
public interface UserRepository extends MongoRepository<User, String> {

    /**
     * Find user by Clerk user ID
     */
    Optional<User> findByClerkUserId(String clerkUserId);

    /**
     * Find user by email address
     */
    Optional<User> findByEmail(String email);

    /**
     * Check if user exists by Clerk user ID
     */
    boolean existsByClerkUserId(String clerkUserId);

    /**
     * Check if user exists by email
     */
    boolean existsByEmail(String email);

    /**
     * Find all active users
     */
    List<User> findByIsActiveTrue();

    /**
     * Find users by role
     */
    List<User> findByRole(UserRole role);

    /**
     * Find users by role with pagination
     */
    Page<User> findByRole(UserRole role, Pageable pageable);

    /**
     * Find active users by role
     */
    List<User> findByRoleAndIsActiveTrue(UserRole role);

    /**
     * Find users created after a specific date
     */
    List<User> findByCreatedAtAfter(LocalDateTime date);

    /**
     * Find users who haven't completed onboarding
     */
    @Query("{ 'metadata.onboardingCompleted': false }")
    List<User> findUsersWithIncompleteOnboarding();

    /**
     * Find users with low profile completeness
     */
    @Query("{ 'metadata.profileCompleteness': { $lt: ?0 } }")
    List<User> findUsersWithLowProfileCompleteness(int threshold);

    /**
     * Find users by subscription plan
     */
    @Query("{ 'subscription.plan': ?0 }")
    List<User> findBySubscriptionPlan(String plan);

    /**
     * Find users with expired subscriptions
     */
    @Query("{ 'subscription.endDate': { $lt: ?0 }, 'subscription.status': 'ACTIVE' }")
    List<User> findUsersWithExpiredSubscriptions(LocalDateTime currentDate);

    /**
     * Find users by referral code
     */
    @Query("{ 'metadata.referralCode': ?0 }")
    Optional<User> findByReferralCode(String referralCode);

    /**
     * Find users referred by a specific user
     */
    @Query("{ 'metadata.referredBy': ?0 }")
    List<User> findUsersReferredBy(String referrerId);

    /**
     * Count active users
     */
    long countByIsActiveTrue();

    /**
     * Count users by role
     */
    long countByRole(UserRole role);

    /**
     * Count users created today
     */
    @Query(value = "{ 'createdAt': { $gte: ?0, $lt: ?1 } }", count = true)
    long countUsersCreatedBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find users with recent activity
     */
    @Query("{ 'metadata.lastLoginAt': { $gte: ?0 } }")
    List<User> findUsersWithRecentActivity(LocalDateTime since);

    /**
     * Search users by name or email
     */
    @Query("{ $or: [ " +
           "{ 'firstName': { $regex: ?0, $options: 'i' } }, " +
           "{ 'lastName': { $regex: ?0, $options: 'i' } }, " +
           "{ 'email': { $regex: ?0, $options: 'i' } } " +
           "] }")
    Page<User> searchUsers(String searchTerm, Pageable pageable);

    /**
     * Find users by location
     */
    @Query("{ $or: [ " +
           "{ 'profile.location.city': { $regex: ?0, $options: 'i' } }, " +
           "{ 'profile.location.state': { $regex: ?0, $options: 'i' } }, " +
           "{ 'profile.location.country': { $regex: ?0, $options: 'i' } } " +
           "] }")
    List<User> findUsersByLocation(String location);

    /**
     * Find users by skills
     */
    @Query("{ 'profile.skills': { $in: ?0 } }")
    List<User> findUsersBySkills(List<String> skills);

    /**
     * Find users by industry
     */
    @Query("{ 'profile.industry': { $regex: ?0, $options: 'i' } }")
    List<User> findUsersByIndustry(String industry);

    /**
     * Find users by experience level
     */
    @Query("{ 'profile.experienceLevel': ?0 }")
    List<User> findUsersByExperienceLevel(String experienceLevel);
}
