package com.careeralgo.repository;

import com.careeralgo.constant.FileType;
import com.careeralgo.model.Resume;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Resume document operations
 */
@Repository
public interface ResumeRepository extends MongoRepository<Resume, String> {

    /**
     * Find all resumes for a specific user
     */
    List<Resume> findByUserId(String userId);

    /**
     * Find active resumes for a specific user
     */
    List<Resume> findByUserIdAndIsActiveTrue(String userId);

    /**
     * Find user's resumes with pagination
     */
    Page<Resume> findByUserId(String userId, Pageable pageable);

    /**
     * Find user's active resumes with pagination
     */
    Page<Resume> findByUserIdAndIsActiveTrue(String userId, Pageable pageable);

    /**
     * Find user's primary resume
     */
    Optional<Resume> findByUserIdAndIsPrimaryTrue(String userId);

    /**
     * Find resumes by file type
     */
    List<Resume> findByFileType(FileType fileType);

    /**
     * Find resumes by user and file type
     */
    List<Resume> findByUserIdAndFileType(String userId, FileType fileType);

    /**
     * Find resumes created after a specific date
     */
    List<Resume> findByCreatedAtAfter(LocalDateTime date);

    /**
     * Find resumes by template ID
     */
    List<Resume> findByTemplateId(String templateId);

    /**
     * Find resumes that need AI analysis
     */
    @Query("{ 'aiAnalysis': null }")
    List<Resume> findResumesNeedingAnalysis();

    /**
     * Find resumes with low AI scores
     */
    @Query("{ 'aiAnalysis.overallScore': { $lt: ?0 } }")
    List<Resume> findResumesWithLowScores(int threshold);

    /**
     * Find resumes by shareable link
     */
    Optional<Resume> findByShareableLink(String shareableLink);

    /**
     * Count resumes by user
     */
    long countByUserId(String userId);

    /**
     * Count active resumes by user
     */
    long countByUserIdAndIsActiveTrue(String userId);

    /**
     * Find most downloaded resumes
     */
    @Query(value = "{}", sort = "{ 'downloadCount': -1 }")
    List<Resume> findMostDownloadedResumes(Pageable pageable);

    /**
     * Find recently updated resumes
     */
    @Query(value = "{}", sort = "{ 'updatedAt': -1 }")
    List<Resume> findRecentlyUpdatedResumes(Pageable pageable);

    /**
     * Search resumes by content
     */
    @Query("{ $text: { $search: ?0 } }")
    List<Resume> searchResumesByContent(String searchTerm);

    /**
     * Find resumes by skills
     */
    @Query("{ 'parsedContent.skills.technical': { $in: ?0 } }")
    List<Resume> findResumesBySkills(List<String> skills);

    /**
     * Find resumes by experience level
     */
    @Query("{ 'parsedContent.workExperience': { $exists: true, $not: { $size: 0 } } }")
    List<Resume> findResumesWithWorkExperience();

    /**
     * Find resumes by education
     */
    @Query("{ 'parsedContent.education': { $exists: true, $not: { $size: 0 } } }")
    List<Resume> findResumesWithEducation();

    /**
     * Find resumes with certifications
     */
    @Query("{ 'parsedContent.certifications': { $exists: true, $not: { $size: 0 } } }")
    List<Resume> findResumesWithCertifications();

    /**
     * Find resumes by version
     */
    List<Resume> findByUserIdAndVersion(String userId, Integer version);

    /**
     * Find latest version of user's resumes
     */
    @Query(value = "{ 'userId': ?0 }", sort = "{ 'version': -1 }")
    List<Resume> findLatestVersionsByUserId(String userId);

    /**
     * Find resumes with customizations for specific job
     */
    @Query("{ 'customizations.jobId': ?0 }")
    List<Resume> findResumesCustomizedForJob(String jobId);

    /**
     * Count total downloads across all resumes
     */
    @Query(value = "{}", fields = "{ 'downloadCount': 1 }")
    List<Resume> findAllDownloadCounts();

    /**
     * Find resumes by file size range
     */
    @Query("{ 'fileSize': { $gte: ?0, $lte: ?1 } }")
    List<Resume> findResumesByFileSizeRange(Long minSize, Long maxSize);

    /**
     * Find orphaned resumes (user doesn't exist)
     */
    @Query("{ 'userId': { $nin: ?0 } }")
    List<Resume> findOrphanedResumes(List<String> existingUserIds);

    /**
     * Count resumes by user ID updated after date
     */
    long countByUserIdAndUpdatedAtAfter(String userId, LocalDateTime date);
}
