package com.careeralgo.model;

import com.careeralgo.constant.SubscriptionPlan;

import java.time.LocalDateTime;

/**
 * Subscription information embedded in User document
 */
public class Subscription {
    
    private SubscriptionPlan plan = SubscriptionPlan.FREE;
    private SubscriptionStatus status = SubscriptionStatus.ACTIVE;
    private LocalDateTime startDate;
    private LocalDateTime endDate;

    public enum SubscriptionStatus {
        ACTIVE, CANCELLED, EXPIRED
    }

    // Constructors
    public Subscription() {
        this.startDate = LocalDateTime.now();
        this.endDate = LocalDateTime.now().plusYears(1);
    }

    public Subscription(SubscriptionPlan plan, SubscriptionStatus status) {
        this();
        this.plan = plan;
        this.status = status;
    }

    // Getters and Setters
    public SubscriptionPlan getPlan() {
        return plan;
    }

    public void setPlan(SubscriptionPlan plan) {
        this.plan = plan;
    }

    public SubscriptionStatus getStatus() {
        return status;
    }

    public void setStatus(SubscriptionStatus status) {
        this.status = status;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }

    public boolean isActive() {
        return status == SubscriptionStatus.ACTIVE && 
               LocalDateTime.now().isBefore(endDate);
    }

    public boolean isPremium() {
        return isActive() && (plan == SubscriptionPlan.PRO || plan == SubscriptionPlan.ENTERPRISE);
    }
}
