package com.careeralgo.model;

/**
 * User preferences embedded in User document
 */
public class Preferences {

    private boolean emailNotifications = true;
    private boolean pushNotifications = true;
    private boolean jobAlerts = true;
    private boolean weeklyReports = true;
    private boolean marketingEmails = false;
    private Theme theme = Theme.LIGHT;
    private String language = "en";
    private String timezone = "UTC";

    public enum Theme {
        LIGHT, DARK, AUTO
    }

    // Constructors
    public Preferences() {}

    // Getters and Setters
    public boolean isEmailNotifications() {
        return emailNotifications;
    }

    public void setEmailNotifications(boolean emailNotifications) {
        this.emailNotifications = emailNotifications;
    }

    public boolean isPushNotifications() {
        return pushNotifications;
    }

    public void setPushNotifications(boolean pushNotifications) {
        this.pushNotifications = pushNotifications;
    }

    public boolean isJobAlerts() {
        return jobAlerts;
    }

    public void setJobAlerts(boolean jobAlerts) {
        this.jobAlerts = jobAlerts;
    }

    public boolean isWeeklyReports() {
        return weeklyReports;
    }

    public void setWeeklyReports(boolean weeklyReports) {
        this.weeklyReports = weeklyReports;
    }

    public boolean isMarketingEmails() {
        return marketingEmails;
    }

    public void setMarketingEmails(boolean marketingEmails) {
        this.marketingEmails = marketingEmails;
    }

    public Theme getTheme() {
        return theme;
    }

    public void setTheme(Theme theme) {
        this.theme = theme;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }
}
