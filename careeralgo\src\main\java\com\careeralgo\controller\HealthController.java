package com.careeralgo.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Health check controller
 */
@RestController
@Tag(name = "Health Check", description = "Application health and status endpoints")
public class HealthController {

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * Basic health check
     */
    @GetMapping("/health")
    @Operation(summary = "Health check", description = "Check if the application is running")
    @ApiResponse(responseCode = "200", description = "Application is healthy")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", LocalDateTime.now());
        health.put("service", "CareerAlgo Backend");
        health.put("version", "1.0.0");
        
        return ResponseEntity.ok(health);
    }

    /**
     * Detailed health check with dependencies
     */
    @GetMapping("/health/detailed")
    @Operation(summary = "Detailed health check", description = "Check application health including dependencies")
    @ApiResponse(responseCode = "200", description = "Detailed health information")
    public ResponseEntity<Map<String, Object>> detailedHealth() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", LocalDateTime.now());
        health.put("service", "CareerAlgo Backend");
        health.put("version", "1.0.0");
        
        // Check MongoDB connection
        Map<String, Object> mongodb = new HashMap<>();
        try {
            mongoTemplate.getDb().runCommand(new org.bson.Document("ping", 1));
            mongodb.put("status", "UP");
            mongodb.put("database", mongoTemplate.getDb().getName());
        } catch (Exception e) {
            mongodb.put("status", "DOWN");
            mongodb.put("error", e.getMessage());
        }
        
        Map<String, Object> dependencies = new HashMap<>();
        dependencies.put("mongodb", mongodb);
        
        health.put("dependencies", dependencies);
        
        return ResponseEntity.ok(health);
    }
}
