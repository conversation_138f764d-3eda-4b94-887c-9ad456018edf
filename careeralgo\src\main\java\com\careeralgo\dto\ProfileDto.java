package com.careeralgo.dto;

import com.careeralgo.constant.ExperienceLevel;
import com.careeralgo.model.Location;
import com.careeralgo.model.Profile;
import com.careeralgo.model.SalaryExpectation;

import java.util.List;

/**
 * DTO for user profile information
 */
public class ProfileDto {

    private String jobTitle;
    private String industry;
    private ExperienceLevel experienceLevel;
    private Location location;
    private Profile.RemotePreference remotePreference;
    private SalaryExpectation salaryExpectation;
    private List<String> skills;
    private String linkedinProfile;
    private String githubProfile;
    private String personalWebsite;
    private String bio;
    private String phoneNumber;

    // Constructors
    public ProfileDto() {}

    public ProfileDto(Profile profile) {
        this.jobTitle = profile.getJobTitle();
        this.industry = profile.getIndustry();
        this.experienceLevel = profile.getExperienceLevel();
        this.location = profile.getLocation();
        this.remotePreference = profile.getRemotePreference();
        this.salaryExpectation = profile.getSalaryExpectation();
        this.skills = profile.getSkills();
        this.linkedinProfile = profile.getLinkedinProfile();
        this.githubProfile = profile.getGithubProfile();
        this.personalWebsite = profile.getPersonalWebsite();
        this.bio = profile.getBio();
        this.phoneNumber = profile.getPhoneNumber();
    }

    // Getters and Setters
    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public ExperienceLevel getExperienceLevel() {
        return experienceLevel;
    }

    public void setExperienceLevel(ExperienceLevel experienceLevel) {
        this.experienceLevel = experienceLevel;
    }

    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
    }

    public Profile.RemotePreference getRemotePreference() {
        return remotePreference;
    }

    public void setRemotePreference(Profile.RemotePreference remotePreference) {
        this.remotePreference = remotePreference;
    }

    public SalaryExpectation getSalaryExpectation() {
        return salaryExpectation;
    }

    public void setSalaryExpectation(SalaryExpectation salaryExpectation) {
        this.salaryExpectation = salaryExpectation;
    }

    public List<String> getSkills() {
        return skills;
    }

    public void setSkills(List<String> skills) {
        this.skills = skills;
    }

    public String getLinkedinProfile() {
        return linkedinProfile;
    }

    public void setLinkedinProfile(String linkedinProfile) {
        this.linkedinProfile = linkedinProfile;
    }

    public String getGithubProfile() {
        return githubProfile;
    }

    public void setGithubProfile(String githubProfile) {
        this.githubProfile = githubProfile;
    }

    public String getPersonalWebsite() {
        return personalWebsite;
    }

    public void setPersonalWebsite(String personalWebsite) {
        this.personalWebsite = personalWebsite;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
}
