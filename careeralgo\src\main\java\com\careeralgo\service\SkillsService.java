package com.careeralgo.service;

import com.careeralgo.constant.SkillCategory;
import com.careeralgo.model.Skill;
import com.careeralgo.model.User;
import com.careeralgo.model.UserSkill;
import com.careeralgo.repository.SkillRepository;
import com.careeralgo.repository.UserRepository;
import com.careeralgo.repository.UserSkillRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for skills management and analysis
 */
@Service
public class SkillsService {

    private static final Logger logger = LoggerFactory.getLogger(SkillsService.class);

    @Autowired
    private SkillRepository skillRepository;

    @Autowired
    private UserSkillRepository userSkillRepository;

    @Autowired
    private UserRepository userRepository;

    /**
     * Search skills by name or category
     */
    public Page<Skill> searchSkills(String query, SkillCategory category, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("popularity").descending());

        if (query != null && !query.trim().isEmpty() && category != null) {
            return skillRepository.findByNameContainingIgnoreCaseAndCategory(query.trim(), category, pageable);
        } else if (query != null && !query.trim().isEmpty()) {
            return skillRepository.findByNameContainingIgnoreCaseOrAliasesContainingIgnoreCase(
                    query.trim(), query.trim(), pageable);
        } else if (category != null) {
            return skillRepository.findByCategory(category, pageable);
        } else {
            return skillRepository.findAll(pageable);
        }
    }

    /**
     * Get trending skills
     */
    public List<Skill> getTrendingSkills(int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by("popularity").descending());
        return skillRepository.findByDemandTrend("GROWING", pageable).getContent();
    }

    /**
     * Get skills by category
     */
    public List<Skill> getSkillsByCategory(SkillCategory category, int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by("popularity").descending());
        return skillRepository.findByCategory(category, pageable).getContent();
    }

    /**
     * Get skill details by ID
     */
    public Optional<Skill> getSkillById(String skillId) {
        return skillRepository.findById(skillId);
    }

    /**
     * Get user's skills
     */
    public List<UserSkill> getUserSkills(Authentication authentication) {
        String userId = getUserId(authentication);
        return userSkillRepository.findByUserIdOrderByProficiencyLevelDesc(userId);
    }

    /**
     * Add skill to user profile
     */
    public UserSkill addUserSkill(Authentication authentication, String skillId,
                                 UserSkill.ProficiencyLevel proficiencyLevel,
                                 Double yearsOfExperience) {
        String userId = getUserId(authentication);

        // Check if skill exists
        Skill skill = skillRepository.findById(skillId)
                .orElseThrow(() -> new IllegalArgumentException("Skill not found: " + skillId));

        // Check if user already has this skill
        Optional<UserSkill> existingUserSkill = userSkillRepository.findByUserIdAndSkillId(userId, skillId);
        if (existingUserSkill.isPresent()) {
            throw new IllegalArgumentException("User already has this skill");
        }

        // Create new user skill
        UserSkill userSkill = new UserSkill();
        userSkill.setUserId(userId);
        userSkill.setSkillId(skillId);
        userSkill.setProficiencyLevel(proficiencyLevel);
        userSkill.setYearsOfExperience(yearsOfExperience != null ? yearsOfExperience : 0.0);
        userSkill.setSource(UserSkill.SkillSource.MANUAL);
        userSkill.setLastUsed(LocalDate.now());
        userSkill.setVerified(false);
        userSkill.setEndorsements(0);

        UserSkill savedUserSkill = userSkillRepository.save(userSkill);
        logger.info("Added skill {} to user {}", skillId, userId);

        return savedUserSkill;
    }

    /**
     * Update user skill proficiency
     */
    public UserSkill updateUserSkill(Authentication authentication, String skillId,
                                   UserSkill.ProficiencyLevel proficiencyLevel,
                                   Double yearsOfExperience) {
        String userId = getUserId(authentication);

        UserSkill userSkill = userSkillRepository.findByUserIdAndSkillId(userId, skillId)
                .orElseThrow(() -> new IllegalArgumentException("User skill not found"));

        userSkill.setProficiencyLevel(proficiencyLevel);
        if (yearsOfExperience != null) {
            userSkill.setYearsOfExperience(yearsOfExperience);
        }
        userSkill.setLastUsed(LocalDate.now());

        UserSkill savedUserSkill = userSkillRepository.save(userSkill);
        logger.info("Updated skill {} for user {}", skillId, userId);

        return savedUserSkill;
    }

    /**
     * Remove skill from user profile
     */
    public void removeUserSkill(Authentication authentication, String skillId) {
        String userId = getUserId(authentication);

        UserSkill userSkill = userSkillRepository.findByUserIdAndSkillId(userId, skillId)
                .orElseThrow(() -> new IllegalArgumentException("User skill not found"));

        userSkillRepository.delete(userSkill);
        logger.info("Removed skill {} from user {}", skillId, userId);
    }

    /**
     * Get skill recommendations for user
     */
    public List<Skill> getSkillRecommendations(Authentication authentication, int limit) {
        String userId = getUserId(authentication);

        // Get user's current skills
        List<UserSkill> userSkills = userSkillRepository.findByUserId(userId);
        Set<String> userSkillIds = userSkills.stream()
                .map(UserSkill::getSkillId)
                .collect(Collectors.toSet());

        // Get user's profile for context
        User user = userRepository.findById(userId).orElse(null);
        if (user == null) {
            return Collections.emptyList();
        }

        // Find related skills based on user's current skills
        List<Skill> recommendations = new ArrayList<>();

        for (UserSkill userSkill : userSkills) {
            Optional<Skill> skillOpt = skillRepository.findById(userSkill.getSkillId());
            if (skillOpt.isPresent()) {
                Skill skill = skillOpt.get();

                // Add related skills
                if (skill.getRelatedSkills() != null) {
                    for (String relatedSkillName : skill.getRelatedSkills()) {
                        List<Skill> relatedSkills = skillRepository.findByNameIgnoreCase(relatedSkillName);
                        for (Skill relatedSkill : relatedSkills) {
                            if (!userSkillIds.contains(relatedSkill.getId()) &&
                                !recommendations.contains(relatedSkill)) {
                                recommendations.add(relatedSkill);
                            }
                        }
                    }
                }
            }
        }

        // Sort by popularity and limit
        recommendations.sort((s1, s2) -> Integer.compare(s2.getPopularity(), s1.getPopularity()));

        return recommendations.stream()
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * Analyze skill gaps for user
     */
    public SkillGapAnalysis analyzeSkillGaps(Authentication authentication, String targetRole) {
        String userId = getUserId(authentication);

        // Get user's current skills
        List<UserSkill> userSkills = userSkillRepository.findByUserId(userId);
        Set<String> userSkillNames = new HashSet<>();

        for (UserSkill userSkill : userSkills) {
            Optional<Skill> skillOpt = skillRepository.findById(userSkill.getSkillId());
            skillOpt.ifPresent(skill -> userSkillNames.add(skill.getName().toLowerCase()));
        }

        // Get skills required for target role (simplified - in real implementation,
        // this would analyze job postings for the role)
        List<String> requiredSkills = getRequiredSkillsForRole(targetRole);

        // Identify gaps
        List<String> missingSkills = requiredSkills.stream()
                .filter(skill -> !userSkillNames.contains(skill.toLowerCase()))
                .collect(Collectors.toList());

        // Identify strengths
        List<String> strengths = requiredSkills.stream()
                .filter(skill -> userSkillNames.contains(skill.toLowerCase()))
                .collect(Collectors.toList());

        SkillGapAnalysis analysis = new SkillGapAnalysis();
        analysis.setTargetRole(targetRole);
        analysis.setRequiredSkills(requiredSkills);
        analysis.setCurrentSkills(new ArrayList<>(userSkillNames));
        analysis.setMissingSkills(missingSkills);
        analysis.setStrengths(strengths);
        analysis.setGapPercentage(calculateGapPercentage(requiredSkills.size(), missingSkills.size()));

        return analysis;
    }

    /**
     * Get market data for skills
     */
    public List<SkillMarketData> getSkillMarketData(List<String> skillIds) {
        List<SkillMarketData> marketData = new ArrayList<>();

        for (String skillId : skillIds) {
            Optional<Skill> skillOpt = skillRepository.findById(skillId);
            if (skillOpt.isPresent()) {
                Skill skill = skillOpt.get();
                SkillMarketData data = new SkillMarketData();
                data.setSkillId(skillId);
                data.setSkillName(skill.getName());
                data.setPopularity(skill.getPopularity());
                data.setDemandTrend(skill.getDemandTrend());
                data.setDifficultyLevel(skill.getDifficultyLevel());

                if (skill.getMarketData() != null) {
                    data.setAverageSalary(skill.getMarketData().getAverageSalary());
                    data.setJobCount(skill.getMarketData().getJobCount());
                    data.setGrowthRate(skill.getMarketData().getGrowthRate());
                }

                marketData.add(data);
            }
        }

        return marketData;
    }

    // Helper methods

    private String getUserId(Authentication authentication) {
        if (authentication.getPrincipal() instanceof Jwt jwt) {
            String clerkUserId = jwt.getSubject();
            User user = userRepository.findByClerkUserId(clerkUserId)
                    .orElseThrow(() -> new IllegalArgumentException("User not found"));
            return user.getId();
        }
        throw new IllegalArgumentException("Invalid authentication type");
    }

    private List<String> getRequiredSkillsForRole(String targetRole) {
        // Simplified mapping - in real implementation, this would analyze job market data
        Map<String, List<String>> roleSkillsMap = Map.of(
                "software engineer", List.of("Java", "Python", "JavaScript", "SQL", "Git", "Agile"),
                "data scientist", List.of("Python", "R", "SQL", "Machine Learning", "Statistics", "Pandas"),
                "product manager", List.of("Product Strategy", "Agile", "Analytics", "User Research", "Roadmapping"),
                "designer", List.of("Figma", "Adobe Creative Suite", "User Experience", "Prototyping", "Design Systems"),
                "marketing manager", List.of("Digital Marketing", "Analytics", "SEO", "Content Marketing", "Social Media")
        );

        return roleSkillsMap.getOrDefault(targetRole.toLowerCase(), Collections.emptyList());
    }

    private double calculateGapPercentage(int totalRequired, int missing) {
        if (totalRequired == 0) return 0.0;
        return (double) missing / totalRequired * 100.0;
    }

    // DTOs for responses

    public static class SkillGapAnalysis {
        private String targetRole;
        private List<String> requiredSkills;
        private List<String> currentSkills;
        private List<String> missingSkills;
        private List<String> strengths;
        private double gapPercentage;

        // Getters and setters
        public String getTargetRole() { return targetRole; }
        public void setTargetRole(String targetRole) { this.targetRole = targetRole; }
        public List<String> getRequiredSkills() { return requiredSkills; }
        public void setRequiredSkills(List<String> requiredSkills) { this.requiredSkills = requiredSkills; }
        public List<String> getCurrentSkills() { return currentSkills; }
        public void setCurrentSkills(List<String> currentSkills) { this.currentSkills = currentSkills; }
        public List<String> getMissingSkills() { return missingSkills; }
        public void setMissingSkills(List<String> missingSkills) { this.missingSkills = missingSkills; }
        public List<String> getStrengths() { return strengths; }
        public void setStrengths(List<String> strengths) { this.strengths = strengths; }
        public double getGapPercentage() { return gapPercentage; }
        public void setGapPercentage(double gapPercentage) { this.gapPercentage = gapPercentage; }
    }

    public static class SkillMarketData {
        private String skillId;
        private String skillName;
        private Integer popularity;
        private String demandTrend;
        private String difficultyLevel;
        private Double averageSalary;
        private Integer jobCount;
        private Double growthRate;

        // Getters and setters
        public String getSkillId() { return skillId; }
        public void setSkillId(String skillId) { this.skillId = skillId; }
        public String getSkillName() { return skillName; }
        public void setSkillName(String skillName) { this.skillName = skillName; }
        public Integer getPopularity() { return popularity; }
        public void setPopularity(Integer popularity) { this.popularity = popularity; }
        public String getDemandTrend() { return demandTrend; }
        public void setDemandTrend(String demandTrend) { this.demandTrend = demandTrend; }
        public String getDifficultyLevel() { return difficultyLevel; }
        public void setDifficultyLevel(String difficultyLevel) { this.difficultyLevel = difficultyLevel; }
        public Double getAverageSalary() { return averageSalary; }
        public void setAverageSalary(Double averageSalary) { this.averageSalary = averageSalary; }
        public Integer getJobCount() { return jobCount; }
        public void setJobCount(Integer jobCount) { this.jobCount = jobCount; }
        public Double getGrowthRate() { return growthRate; }
        public void setGrowthRate(Double growthRate) { this.growthRate = growthRate; }
    }
}
