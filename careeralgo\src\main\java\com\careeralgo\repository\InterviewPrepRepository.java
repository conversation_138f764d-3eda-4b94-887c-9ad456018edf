package com.careeralgo.repository;

import com.careeralgo.model.InterviewPrep;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for InterviewPrep operations
 */
@Repository
public interface InterviewPrepRepository extends MongoRepository<InterviewPrep, String> {

    /**
     * Find interview prep sessions by user ID
     */
    List<InterviewPrep> findByUserIdOrderByCreatedAtDesc(String userId);

    /**
     * Find interview prep sessions by user ID with pagination
     */
    Page<InterviewPrep> findByUserIdOrderByCreatedAtDesc(String userId, Pageable pageable);

    /**
     * Find interview prep sessions by user ID and type
     */
    List<InterviewPrep> findByUserIdAndTypeOrderByCreatedAtDesc(String userId, InterviewPrep.SessionType type);

    /**
     * Find interview prep sessions by user ID and completion status
     */
    List<InterviewPrep> findByUserIdAndIsCompletedOrderByCreatedAtDesc(String userId, boolean isCompleted);

    /**
     * Find interview prep session by user ID and session ID
     */
    Optional<InterviewPrep> findByUserIdAndSessionId(String userId, String sessionId);

    /**
     * Find interview prep sessions by user ID and job ID
     */
    List<InterviewPrep> findByUserIdAndJobIdOrderByCreatedAtDesc(String userId, String jobId);

    /**
     * Count total sessions by user ID
     */
    long countByUserId(String userId);

    /**
     * Count completed sessions by user ID
     */
    long countByUserIdAndIsCompleted(String userId, boolean isCompleted);

    /**
     * Count sessions by user ID and type
     */
    long countByUserIdAndType(String userId, InterviewPrep.SessionType type);

    /**
     * Find recent sessions by user ID
     */
    @Query("{ 'userId': ?0, 'createdAt': { $gte: ?1 } }")
    List<InterviewPrep> findByUserIdAndCreatedAtAfter(String userId, LocalDateTime date);

    /**
     * Find sessions created between dates
     */
    @Query("{ 'userId': ?0, 'createdAt': { $gte: ?1, $lte: ?2 } }")
    List<InterviewPrep> findByUserIdAndCreatedAtBetween(String userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find sessions by user ID and session name containing
     */
    List<InterviewPrep> findByUserIdAndSessionNameContainingIgnoreCaseOrderByCreatedAtDesc(String userId, String sessionName);

    /**
     * Find top performing sessions by user ID
     */
    @Query("{ 'userId': ?0, 'sessionStats.averageScore': { $exists: true } }")
    List<InterviewPrep> findByUserIdWithScores(String userId);

    /**
     * Find sessions that need completion (started but not finished)
     */
    @Query("{ 'userId': ?0, 'isCompleted': false, 'questions': { $exists: true, $ne: [] } }")
    List<InterviewPrep> findIncompleteSessionsWithQuestions(String userId);

    /**
     * Find sessions by user ID and minimum average score
     */
    @Query("{ 'userId': ?0, 'sessionStats.averageScore': { $gte: ?1 } }")
    List<InterviewPrep> findByUserIdAndMinimumScore(String userId, int minimumScore);

    /**
     * Find sessions with company research
     */
    @Query("{ 'userId': ?0, 'companyResearch': { $exists: true } }")
    List<InterviewPrep> findByUserIdWithCompanyResearch(String userId);

    /**
     * Delete sessions older than specified date
     */
    void deleteByUserIdAndCreatedAtBefore(String userId, LocalDateTime date);

    /**
     * Find sessions by multiple types
     */
    List<InterviewPrep> findByUserIdAndTypeInOrderByCreatedAtDesc(String userId, List<InterviewPrep.SessionType> types);

    /**
     * Count sessions created in date range
     */
    @Query(value = "{ 'userId': ?0, 'createdAt': { $gte: ?1, $lte: ?2 } }", count = true)
    long countByUserIdAndCreatedAtBetween(String userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find sessions with feedback
     */
    @Query("{ 'userId': ?0, 'feedback': { $exists: true } }")
    List<InterviewPrep> findByUserIdWithFeedback(String userId);

    /**
     * Find sessions by user ID ordered by average score descending
     */
    @Query(value = "{ 'userId': ?0, 'sessionStats.averageScore': { $exists: true } }", 
           sort = "{ 'sessionStats.averageScore': -1 }")
    List<InterviewPrep> findByUserIdOrderByAverageScoreDesc(String userId);

    /**
     * Find sessions with audio recordings
     */
    @Query("{ 'userId': ?0, 'questions.audioRecordingUrl': { $exists: true, $ne: null } }")
    List<InterviewPrep> findByUserIdWithAudioRecordings(String userId);

    /**
     * Find sessions by user ID and question category
     */
    @Query("{ 'userId': ?0, 'questions.category': ?1 }")
    List<InterviewPrep> findByUserIdAndQuestionCategory(String userId, InterviewPrep.QuestionCategory category);

    /**
     * Find sessions with AI evaluations
     */
    @Query("{ 'userId': ?0, 'questions.aiEvaluation': { $exists: true } }")
    List<InterviewPrep> findByUserIdWithAIEvaluations(String userId);

    /**
     * Find sessions by user ID and difficulty level
     */
    @Query("{ 'userId': ?0, 'questions.difficulty': ?1 }")
    List<InterviewPrep> findByUserIdAndQuestionDifficulty(String userId, InterviewPrep.QuestionDifficulty difficulty);

    /**
     * Get session statistics for user
     */
    @Query(value = "{ 'userId': ?0 }", 
           fields = "{ 'sessionStats': 1, 'type': 1, 'isCompleted': 1, 'createdAt': 1 }")
    List<InterviewPrep> findSessionStatsForUser(String userId);

    /**
     * Find sessions that can be resumed (incomplete with questions)
     */
    @Query("{ 'userId': ?0, 'isCompleted': false, 'questions': { $exists: true, $size: { $gt: 0 } } }")
    List<InterviewPrep> findResumableSessionsByUserId(String userId);

    /**
     * Find sessions by user ID and update timestamp after
     */
    List<InterviewPrep> findByUserIdAndUpdatedAtAfterOrderByUpdatedAtDesc(String userId, LocalDateTime date);

    /**
     * Find sessions with specific job preparation
     */
    @Query("{ 'userId': ?0, 'jobId': { $exists: true, $ne: null } }")
    List<InterviewPrep> findByUserIdWithJobPreparation(String userId);

    /**
     * Find sessions by user ID and session type with pagination
     */
    Page<InterviewPrep> findByUserIdAndTypeOrderByCreatedAtDesc(String userId, InterviewPrep.SessionType type, Pageable pageable);

    /**
     * Find sessions by user ID and completion status with pagination
     */
    Page<InterviewPrep> findByUserIdAndIsCompletedOrderByCreatedAtDesc(String userId, boolean isCompleted, Pageable pageable);

    /**
     * Search sessions by user ID and session name
     */
    @Query("{ 'userId': ?0, 'sessionName': { $regex: ?1, $options: 'i' } }")
    Page<InterviewPrep> searchByUserIdAndSessionName(String userId, String searchTerm, Pageable pageable);
}
