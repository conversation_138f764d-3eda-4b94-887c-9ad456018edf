package com.careeralgo.model;

import com.careeralgo.constant.SkillCategory;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Skill model representing a professional skill
 */
@Document(collection = "skills")
public class Skill {

    @Id
    private String id;

    @Indexed
    private String name;

    private String description;

    @Indexed
    private SkillCategory category;

    private List<String> aliases;

    private List<String> relatedSkills;

    @Indexed
    private Integer popularity;

    private String demandTrend; // GROWING, STABLE, DECLINING

    private String difficultyLevel; // BEGINNER, INTERMEDIATE, ADVANCED, EXPERT

    private MarketData marketData;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    // Constructors
    public Skill() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public Skill(String name, SkillCategory category) {
        this();
        this.name = name;
        this.category = category;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        this.updatedAt = LocalDateTime.now();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }

    public SkillCategory getCategory() {
        return category;
    }

    public void setCategory(SkillCategory category) {
        this.category = category;
        this.updatedAt = LocalDateTime.now();
    }

    public List<String> getAliases() {
        return aliases;
    }

    public void setAliases(List<String> aliases) {
        this.aliases = aliases;
        this.updatedAt = LocalDateTime.now();
    }

    public List<String> getRelatedSkills() {
        return relatedSkills;
    }

    public void setRelatedSkills(List<String> relatedSkills) {
        this.relatedSkills = relatedSkills;
        this.updatedAt = LocalDateTime.now();
    }

    public Integer getPopularity() {
        return popularity;
    }

    public void setPopularity(Integer popularity) {
        this.popularity = popularity;
        this.updatedAt = LocalDateTime.now();
    }

    public String getDemandTrend() {
        return demandTrend;
    }

    public void setDemandTrend(String demandTrend) {
        this.demandTrend = demandTrend;
        this.updatedAt = LocalDateTime.now();
    }

    public String getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(String difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
        this.updatedAt = LocalDateTime.now();
    }

    public MarketData getMarketData() {
        return marketData;
    }

    public void setMarketData(MarketData marketData) {
        this.marketData = marketData;
        this.updatedAt = LocalDateTime.now();
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Nested class for market data
    public static class MarketData {
        private Double averageSalary;
        private Integer jobCount;
        private Double growthRate;
        private String salaryRange;
        private LocalDateTime lastUpdated;

        public MarketData() {
            this.lastUpdated = LocalDateTime.now();
        }

        // Getters and Setters
        public Double getAverageSalary() {
            return averageSalary;
        }

        public void setAverageSalary(Double averageSalary) {
            this.averageSalary = averageSalary;
            this.lastUpdated = LocalDateTime.now();
        }

        public Integer getJobCount() {
            return jobCount;
        }

        public void setJobCount(Integer jobCount) {
            this.jobCount = jobCount;
            this.lastUpdated = LocalDateTime.now();
        }

        public Double getGrowthRate() {
            return growthRate;
        }

        public void setGrowthRate(Double growthRate) {
            this.growthRate = growthRate;
            this.lastUpdated = LocalDateTime.now();
        }

        public String getSalaryRange() {
            return salaryRange;
        }

        public void setSalaryRange(String salaryRange) {
            this.salaryRange = salaryRange;
            this.lastUpdated = LocalDateTime.now();
        }

        public LocalDateTime getLastUpdated() {
            return lastUpdated;
        }

        public void setLastUpdated(LocalDateTime lastUpdated) {
            this.lastUpdated = lastUpdated;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Skill skill = (Skill) o;
        return id != null ? id.equals(skill.id) : skill.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "Skill{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", category=" + category +
                ", popularity=" + popularity +
                ", demandTrend='" + demandTrend + '\'' +
                '}';
    }
}
