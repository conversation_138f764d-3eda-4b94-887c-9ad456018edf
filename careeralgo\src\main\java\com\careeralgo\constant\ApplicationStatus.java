package com.careeralgo.constant;

/**
 * Application status tracking
 */
public enum ApplicationStatus {
    SAVED("SAVED"),
    APPLIED("APPLIED"),
    SCREENING("SCREENING"),
    INTERVIEW("INTERVIEW"),
    OFFER("OFFER"),
    REJECTED("REJECTED"),
    WIT<PERSON><PERSON>WN("WITHDRAWN"),
    HIRED("HIRED");

    private final String value;

    ApplicationStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
