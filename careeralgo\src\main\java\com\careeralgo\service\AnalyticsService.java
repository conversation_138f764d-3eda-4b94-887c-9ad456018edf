package com.careeralgo.service;

import com.careeralgo.constant.ApplicationStatus;
import com.careeralgo.model.Analytics;
import com.careeralgo.model.User;
import com.careeralgo.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Service for analytics and reporting
 */
@Service
public class AnalyticsService {

    private static final Logger logger = LoggerFactory.getLogger(AnalyticsService.class);

    @Autowired
    private AnalyticsRepository analyticsRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ApplicationRepository applicationRepository;

    @Autowired
    private ResumeRepository resumeRepository;

    @Autowired
    private JobRepository jobRepository;

    /**
     * Get dashboard analytics for user
     */
    public DashboardAnalytics getDashboardAnalytics(Authentication authentication) {
        String userId = getUserId(authentication);

        DashboardAnalytics analytics = new DashboardAnalytics();
        analytics.setUserId(userId);
        analytics.setGeneratedAt(LocalDateTime.now());

        // Application metrics
        long totalApplications = applicationRepository.countByUserId(userId);
        analytics.setTotalApplications(totalApplications);

        // Applications by status
        Map<String, Long> applicationsByStatus = new HashMap<>();
        for (ApplicationStatus status : ApplicationStatus.values()) {
            long count = applicationRepository.countByUserIdAndStatus(userId, status);
            applicationsByStatus.put(status.getValue(), count);
        }
        analytics.setApplicationsByStatus(applicationsByStatus);

        // Response rate calculation
        long responseCount = applicationRepository.countByUserIdAndStatusIn(userId,
                Arrays.asList(ApplicationStatus.SCREENING, ApplicationStatus.INTERVIEW,
                             ApplicationStatus.OFFER, ApplicationStatus.HIRED));

        if (totalApplications > 0) {
            analytics.setResponseRate((double) responseCount / totalApplications * 100);
        } else {
            analytics.setResponseRate(0.0);
        }

        // Interview rate
        long interviewCount = applicationRepository.countByUserIdAndStatusIn(userId,
                Arrays.asList(ApplicationStatus.INTERVIEW, ApplicationStatus.OFFER, ApplicationStatus.HIRED));

        if (totalApplications > 0) {
            analytics.setInterviewRate((double) interviewCount / totalApplications * 100);
        } else {
            analytics.setInterviewRate(0.0);
        }

        // Resume metrics
        analytics.setTotalResumes(resumeRepository.countByUserId(userId));
        analytics.setResumeViews(resumeRepository.findByUserId(userId).stream()
                .mapToInt(resume -> resume.getDownloadCount() != null ? resume.getDownloadCount() : 0)
                .sum());

        // Recent activity
        analytics.setRecentApplications(applicationRepository.countByUserIdAndCreatedAtAfter(
                userId, LocalDateTime.now().minusDays(7)));
        analytics.setRecentResumeUpdates(resumeRepository.countByUserIdAndUpdatedAtAfter(
                userId, LocalDateTime.now().minusDays(7)));

        // Goals and targets (placeholder - would be user-defined)
        analytics.setMonthlyApplicationTarget(20);
        analytics.setCurrentMonthApplications(applicationRepository.countByUserIdAndCreatedAtAfter(
                userId, LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0)));

        return analytics;
    }

    /**
     * Get application analytics
     */
    public ApplicationAnalytics getApplicationAnalytics(Authentication authentication, String period) {
        String userId = getUserId(authentication);

        ApplicationAnalytics analytics = new ApplicationAnalytics();
        analytics.setUserId(userId);
        analytics.setPeriod(period);
        analytics.setGeneratedAt(LocalDateTime.now());

        LocalDateTime startDate = getStartDateForPeriod(period);

        // Applications over time
        Map<String, Long> applicationsOverTime = new LinkedHashMap<>();
        LocalDateTime current = startDate;
        LocalDateTime now = LocalDateTime.now();

        while (current.isBefore(now)) {
            LocalDateTime nextPeriod = getNextPeriod(current, period);
            long count = applicationRepository.countByUserIdAndCreatedAtBetween(userId, current, nextPeriod);
            applicationsOverTime.put(formatPeriod(current, period), count);
            current = nextPeriod;
        }
        analytics.setApplicationsOverTime(applicationsOverTime);

        // Success metrics
        long totalInPeriod = applicationRepository.countByUserIdAndCreatedAtAfter(userId, startDate);
        long successfulInPeriod = applicationRepository.countByUserIdAndStatusInAndCreatedAtAfter(
                userId, Arrays.asList(ApplicationStatus.OFFER, ApplicationStatus.HIRED), startDate);

        if (totalInPeriod > 0) {
            analytics.setSuccessRate((double) successfulInPeriod / totalInPeriod * 100);
        }

        // Top performing job sources
        // TODO: Implement when job sources are tracked
        analytics.setTopJobSources(new HashMap<>());

        // Average time to response
        // TODO: Implement based on status history
        analytics.setAverageTimeToResponse(0.0);

        return analytics;
    }

    /**
     * Get skill analytics
     */
    public SkillAnalytics getSkillAnalytics(Authentication authentication) {
        String userId = getUserId(authentication);

        SkillAnalytics analytics = new SkillAnalytics();
        analytics.setUserId(userId);
        analytics.setGeneratedAt(LocalDateTime.now());

        // TODO: Implement skill analytics when UserSkill repository is available
        analytics.setTotalSkills(0);
        analytics.setSkillsByCategory(new HashMap<>());
        analytics.setSkillGrowth(new HashMap<>());
        analytics.setMarketDemandScore(0.0);

        return analytics;
    }

    /**
     * Track user activity
     */
    public void trackActivity(String userId, String activityType, Map<String, Object> metadata) {
        try {
            // Create analytics record
            Analytics analytics = new Analytics(userId, activityType);

            // Update activity data
            updateActivityData(analytics, activityType, metadata);

            analyticsRepository.save(analytics);

        } catch (Exception e) {
            logger.error("Error tracking activity for user: {}", userId, e);
        }
    }

    /**
     * Generate insights for user
     */
    public List<String> generateInsights(Authentication authentication) {
        String userId = getUserId(authentication);
        List<String> insights = new ArrayList<>();

        try {
            // Application insights
            long totalApplications = applicationRepository.countByUserId(userId);
            if (totalApplications > 0) {
                long responseCount = applicationRepository.countByUserIdAndStatusIn(userId,
                        Arrays.asList(ApplicationStatus.SCREENING, ApplicationStatus.INTERVIEW,
                                     ApplicationStatus.OFFER, ApplicationStatus.HIRED));

                double responseRate = (double) responseCount / totalApplications * 100;

                if (responseRate > 30) {
                    insights.add("Your application response rate (" + String.format("%.1f", responseRate) +
                               "%) is above average! Keep up the great work.");
                } else if (responseRate < 10) {
                    insights.add("Your application response rate is below average. Consider improving your resume or targeting more relevant positions.");
                }
            }

            // Resume insights
            long resumeCount = resumeRepository.countByUserId(userId);
            if (resumeCount == 0) {
                insights.add("Upload your resume to get started with job applications and AI-powered recommendations.");
            } else if (resumeCount == 1) {
                insights.add("Consider creating multiple resume versions tailored to different job types for better results.");
            }

            // Activity insights
            long recentApplications = applicationRepository.countByUserIdAndCreatedAtAfter(
                    userId, LocalDateTime.now().minusDays(7));

            if (recentApplications == 0) {
                insights.add("You haven't applied to any jobs this week. Consistent application activity improves your chances.");
            } else if (recentApplications > 10) {
                insights.add("You're very active with job applications! Make sure to follow up on your applications.");
            }

            // Default insights if none generated
            if (insights.isEmpty()) {
                insights.add("Keep building your profile and applying to relevant positions to improve your job search success.");
            }

        } catch (Exception e) {
            logger.error("Error generating insights for user: {}", userId, e);
            insights.add("Continue using CareerAlgo to track your progress and get personalized insights.");
        }

        return insights;
    }

    // Helper methods

    private String getUserId(Authentication authentication) {
        if (authentication.getPrincipal() instanceof Jwt jwt) {
            String clerkUserId = jwt.getSubject();
            User user = userRepository.findByClerkUserId(clerkUserId)
                    .orElseThrow(() -> new IllegalArgumentException("User not found"));
            return user.getId();
        }
        throw new IllegalArgumentException("Invalid authentication type");
    }

    private LocalDateTime getStartDateForPeriod(String period) {
        LocalDateTime now = LocalDateTime.now();
        return switch (period.toLowerCase()) {
            case "week" -> now.minusWeeks(1);
            case "month" -> now.minusMonths(1);
            case "quarter" -> now.minusMonths(3);
            case "year" -> now.minusYears(1);
            default -> now.minusMonths(1);
        };
    }

    private LocalDateTime getNextPeriod(LocalDateTime current, String period) {
        return switch (period.toLowerCase()) {
            case "week" -> current.plusWeeks(1);
            case "month" -> current.plusMonths(1);
            case "quarter" -> current.plusMonths(3);
            case "year" -> current.plusYears(1);
            default -> current.plusDays(1);
        };
    }

    private String formatPeriod(LocalDateTime date, String period) {
        return switch (period.toLowerCase()) {
            case "week" -> date.format(DateTimeFormatter.ofPattern("yyyy-'W'ww"));
            case "month" -> date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            case "quarter" -> date.format(DateTimeFormatter.ofPattern("yyyy-'Q'Q"));
            case "year" -> date.format(DateTimeFormatter.ofPattern("yyyy"));
            default -> date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        };
    }

    private void updateActivityData(Analytics analytics, String activityType, Map<String, Object> metadata) {
        // Add metadata to event data
        if (metadata != null) {
            for (Map.Entry<String, Object> entry : metadata.entrySet()) {
                analytics.addEventData(entry.getKey(), entry.getValue());
            }
        }

        // Set event category based on activity type
        switch (activityType.toLowerCase()) {
            case "job_viewed" -> analytics.setEventCategory("job_search");
            case "job_applied" -> analytics.setEventCategory("application");
            case "resume_updated" -> analytics.setEventCategory("resume");
            case "profile_updated" -> analytics.setEventCategory("profile");
            case "skill_assessment" -> analytics.setEventCategory("skills");
            case "interview_prep" -> analytics.setEventCategory("interview");
            case "career_advice" -> analytics.setEventCategory("career");
            default -> analytics.setEventCategory("general");
        }
    }

    // Response DTOs

    public static class DashboardAnalytics {
        private String userId;
        private LocalDateTime generatedAt;
        private Long totalApplications;
        private Map<String, Long> applicationsByStatus;
        private Double responseRate;
        private Double interviewRate;
        private Long totalResumes;
        private Integer resumeViews;
        private Long recentApplications;
        private Long recentResumeUpdates;
        private Integer monthlyApplicationTarget;
        private Long currentMonthApplications;

        // Getters and setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public LocalDateTime getGeneratedAt() { return generatedAt; }
        public void setGeneratedAt(LocalDateTime generatedAt) { this.generatedAt = generatedAt; }
        public Long getTotalApplications() { return totalApplications; }
        public void setTotalApplications(Long totalApplications) { this.totalApplications = totalApplications; }
        public Map<String, Long> getApplicationsByStatus() { return applicationsByStatus; }
        public void setApplicationsByStatus(Map<String, Long> applicationsByStatus) { this.applicationsByStatus = applicationsByStatus; }
        public Double getResponseRate() { return responseRate; }
        public void setResponseRate(Double responseRate) { this.responseRate = responseRate; }
        public Double getInterviewRate() { return interviewRate; }
        public void setInterviewRate(Double interviewRate) { this.interviewRate = interviewRate; }
        public Long getTotalResumes() { return totalResumes; }
        public void setTotalResumes(Long totalResumes) { this.totalResumes = totalResumes; }
        public Integer getResumeViews() { return resumeViews; }
        public void setResumeViews(Integer resumeViews) { this.resumeViews = resumeViews; }
        public Long getRecentApplications() { return recentApplications; }
        public void setRecentApplications(Long recentApplications) { this.recentApplications = recentApplications; }
        public Long getRecentResumeUpdates() { return recentResumeUpdates; }
        public void setRecentResumeUpdates(Long recentResumeUpdates) { this.recentResumeUpdates = recentResumeUpdates; }
        public Integer getMonthlyApplicationTarget() { return monthlyApplicationTarget; }
        public void setMonthlyApplicationTarget(Integer monthlyApplicationTarget) { this.monthlyApplicationTarget = monthlyApplicationTarget; }
        public Long getCurrentMonthApplications() { return currentMonthApplications; }
        public void setCurrentMonthApplications(Long currentMonthApplications) { this.currentMonthApplications = currentMonthApplications; }
    }

    public static class ApplicationAnalytics {
        private String userId;
        private String period;
        private LocalDateTime generatedAt;
        private Map<String, Long> applicationsOverTime;
        private Double successRate;
        private Map<String, Long> topJobSources;
        private Double averageTimeToResponse;

        // Getters and setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public String getPeriod() { return period; }
        public void setPeriod(String period) { this.period = period; }
        public LocalDateTime getGeneratedAt() { return generatedAt; }
        public void setGeneratedAt(LocalDateTime generatedAt) { this.generatedAt = generatedAt; }
        public Map<String, Long> getApplicationsOverTime() { return applicationsOverTime; }
        public void setApplicationsOverTime(Map<String, Long> applicationsOverTime) { this.applicationsOverTime = applicationsOverTime; }
        public Double getSuccessRate() { return successRate; }
        public void setSuccessRate(Double successRate) { this.successRate = successRate; }
        public Map<String, Long> getTopJobSources() { return topJobSources; }
        public void setTopJobSources(Map<String, Long> topJobSources) { this.topJobSources = topJobSources; }
        public Double getAverageTimeToResponse() { return averageTimeToResponse; }
        public void setAverageTimeToResponse(Double averageTimeToResponse) { this.averageTimeToResponse = averageTimeToResponse; }
    }

    public static class SkillAnalytics {
        private String userId;
        private LocalDateTime generatedAt;
        private Integer totalSkills;
        private Map<String, Integer> skillsByCategory;
        private Map<String, Integer> skillGrowth;
        private Double marketDemandScore;

        // Getters and setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public LocalDateTime getGeneratedAt() { return generatedAt; }
        public void setGeneratedAt(LocalDateTime generatedAt) { this.generatedAt = generatedAt; }
        public Integer getTotalSkills() { return totalSkills; }
        public void setTotalSkills(Integer totalSkills) { this.totalSkills = totalSkills; }
        public Map<String, Integer> getSkillsByCategory() { return skillsByCategory; }
        public void setSkillsByCategory(Map<String, Integer> skillsByCategory) { this.skillsByCategory = skillsByCategory; }
        public Map<String, Integer> getSkillGrowth() { return skillGrowth; }
        public void setSkillGrowth(Map<String, Integer> skillGrowth) { this.skillGrowth = skillGrowth; }
        public Double getMarketDemandScore() { return marketDemandScore; }
        public void setMarketDemandScore(Double marketDemandScore) { this.marketDemandScore = marketDemandScore; }
    }
}
