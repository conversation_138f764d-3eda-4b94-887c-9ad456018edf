package com.careeralgo.model;

import java.time.LocalDate;

/**
 * Certification entry from resume
 */
public class Certification {
    
    private String name;
    private String issuer;
    private LocalDate issueDate;
    private LocalDate expiryDate;
    private String credentialId;

    // Constructors
    public Certification() {}

    public Certification(String name, String issuer, LocalDate issueDate) {
        this.name = name;
        this.issuer = issuer;
        this.issueDate = issueDate;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public LocalDate getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(LocalDate issueDate) {
        this.issueDate = issueDate;
    }

    public LocalDate getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(LocalDate expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getCredentialId() {
        return credentialId;
    }

    public void setCredentialId(String credentialId) {
        this.credentialId = credentialId;
    }

    public boolean isExpired() {
        return expiryDate != null && expiryDate.isBefore(LocalDate.now());
    }

    public boolean isValid() {
        return !isExpired();
    }

    public String getStatusString() {
        if (expiryDate == null) {
            return "No expiration";
        }
        return isExpired() ? "Expired" : "Valid";
    }
}
