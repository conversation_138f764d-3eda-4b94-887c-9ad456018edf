package com.careeralgo.repository;

import com.careeralgo.constant.SkillCategory;
import com.careeralgo.model.Skill;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Skill entities
 */
@Repository
public interface SkillRepository extends MongoRepository<Skill, String> {

    /**
     * Find skills by name containing the given text (case insensitive)
     */
    Page<Skill> findByNameContainingIgnoreCase(String name, Pageable pageable);

    /**
     * Find skills by name or aliases containing the given text (case insensitive)
     */
    @Query("{'$or': [{'name': {'$regex': ?0, '$options': 'i'}}, {'aliases': {'$regex': ?1, '$options': 'i'}}]}")
    Page<Skill> findByNameContainingIgnoreCaseOrAliasesContainingIgnoreCase(String name, String alias, Pageable pageable);

    /**
     * Find skills by category
     */
    Page<Skill> findByCategory(SkillCategory category, Pageable pageable);

    /**
     * Find skills by name and category containing the given text (case insensitive)
     */
    Page<Skill> findByNameContainingIgnoreCaseAndCategory(String name, SkillCategory category, Pageable pageable);

    /**
     * Find skills by demand trend
     */
    Page<Skill> findByDemandTrend(String demandTrend, Pageable pageable);

    /**
     * Find skills by exact name (case insensitive)
     */
    List<Skill> findByNameIgnoreCase(String name);

    /**
     * Find skills by difficulty level
     */
    Page<Skill> findByDifficultyLevel(String difficultyLevel, Pageable pageable);

    /**
     * Find skills by popularity range
     */
    @Query("{'popularity': {'$gte': ?0, '$lte': ?1}}")
    Page<Skill> findByPopularityBetween(Integer minPopularity, Integer maxPopularity, Pageable pageable);

    /**
     * Find top skills by popularity
     */
    @Query(value = "{}", sort = "{'popularity': -1}")
    Page<Skill> findTopByPopularity(Pageable pageable);

    /**
     * Find skills by category and demand trend
     */
    Page<Skill> findByCategoryAndDemandTrend(SkillCategory category, String demandTrend, Pageable pageable);

    /**
     * Find skills that have related skills
     */
    @Query("{'relatedSkills': {'$exists': true, '$ne': []}}")
    Page<Skill> findSkillsWithRelatedSkills(Pageable pageable);

    /**
     * Find skills by name in list
     */
    List<Skill> findByNameIn(List<String> names);

    /**
     * Find skills by category in list
     */
    List<Skill> findByCategoryIn(List<SkillCategory> categories);

    /**
     * Search skills by text in name, description, or aliases
     */
    @Query("{'$or': [" +
           "{'name': {'$regex': ?0, '$options': 'i'}}, " +
           "{'description': {'$regex': ?0, '$options': 'i'}}, " +
           "{'aliases': {'$regex': ?0, '$options': 'i'}}" +
           "]}")
    Page<Skill> searchSkillsByText(String searchText, Pageable pageable);

    /**
     * Find trending skills (growing demand and high popularity)
     */
    @Query("{'demandTrend': 'GROWING', 'popularity': {'$gte': ?0}}")
    Page<Skill> findTrendingSkills(Integer minPopularity, Pageable pageable);

    /**
     * Find skills by multiple criteria
     */
    @Query("{'$and': [" +
           "{'category': {'$in': ?0}}, " +
           "{'demandTrend': {'$in': ?1}}, " +
           "{'popularity': {'$gte': ?2}}" +
           "]}")
    Page<Skill> findByCriteria(List<SkillCategory> categories, List<String> demandTrends, Integer minPopularity, Pageable pageable);

    /**
     * Count skills by category
     */
    long countByCategory(SkillCategory category);

    /**
     * Count skills by demand trend
     */
    long countByDemandTrend(String demandTrend);

    /**
     * Find skills that are related to a given skill name
     */
    @Query("{'relatedSkills': {'$in': [?0]}}")
    List<Skill> findSkillsRelatedTo(String skillName);

    /**
     * Find skills with market data
     */
    @Query("{'marketData': {'$exists': true, '$ne': null}}")
    Page<Skill> findSkillsWithMarketData(Pageable pageable);

    /**
     * Find skills by difficulty and category
     */
    Page<Skill> findByDifficultyLevelAndCategory(String difficultyLevel, SkillCategory category, Pageable pageable);

    /**
     * Find skills updated after a certain date
     */
    @Query("{'updatedAt': {'$gte': ?0}}")
    List<Skill> findRecentlyUpdatedSkills(java.time.LocalDateTime since);

    /**
     * Find skills with high job count in market data
     */
    @Query("{'marketData.jobCount': {'$gte': ?0}}")
    Page<Skill> findSkillsWithHighJobCount(Integer minJobCount, Pageable pageable);

    /**
     * Find skills with salary data above threshold
     */
    @Query("{'marketData.averageSalary': {'$gte': ?0}}")
    Page<Skill> findSkillsWithHighSalary(Double minSalary, Pageable pageable);

    /**
     * Find skills by growth rate
     */
    @Query("{'marketData.growthRate': {'$gte': ?0}}")
    Page<Skill> findSkillsWithHighGrowthRate(Double minGrowthRate, Pageable pageable);

    /**
     * Check if skill exists by name
     */
    boolean existsByNameIgnoreCase(String name);

    /**
     * Find skills by partial name match and category
     */
    @Query("{'name': {'$regex': ?0, '$options': 'i'}, 'category': ?1}")
    List<Skill> findByPartialNameAndCategory(String partialName, SkillCategory category);

    /**
     * Find skills excluding certain IDs
     */
    @Query("{'_id': {'$nin': ?0}}")
    Page<Skill> findByIdNotIn(List<String> excludeIds, Pageable pageable);

    /**
     * Find skills with aliases
     */
    @Query("{'aliases': {'$exists': true, '$ne': []}}")
    List<Skill> findSkillsWithAliases();

    /**
     * Find skills by exact category and sort by popularity
     */
    List<Skill> findByCategoryOrderByPopularityDesc(SkillCategory category);

    /**
     * Find skills by demand trend and sort by popularity
     */
    List<Skill> findByDemandTrendOrderByPopularityDesc(String demandTrend);
}
