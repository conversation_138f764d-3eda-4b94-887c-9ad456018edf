package com.careeralgo.model;

import java.time.LocalDate;
import java.util.List;

/**
 * Project entry from resume
 */
public class Project {
    
    private String name;
    private String description;
    private List<String> technologies;
    private String url;
    private LocalDate startDate;
    private LocalDate endDate;

    // Constructors
    public Project() {}

    public Project(String name, String description) {
        this.name = name;
        this.description = description;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getTechnologies() {
        return technologies;
    }

    public void setTechnologies(List<String> technologies) {
        this.technologies = technologies;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getDurationString() {
        if (startDate == null && endDate == null) return "Unknown duration";
        
        String start = startDate != null ? (startDate.getMonthValue() + "/" + startDate.getYear()) : "Unknown";
        String end = endDate != null ? (endDate.getMonthValue() + "/" + endDate.getYear()) : "Ongoing";
        
        return start + " - " + end;
    }

    public String getTechnologiesString() {
        return technologies != null ? String.join(", ", technologies) : "";
    }
}
