package com.careeralgo.repository;

import com.careeralgo.model.Notification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for Notification operations
 */
@Repository
public interface NotificationRepository extends MongoRepository<Notification, String> {

    /**
     * Find notifications by user ID
     */
    List<Notification> findByUserIdOrderByCreatedAtDesc(String userId);

    /**
     * Find notifications by user ID with pagination
     */
    Page<Notification> findByUserIdOrderByCreatedAtDesc(String userId, Pageable pageable);

    /**
     * Find unread notifications by user ID
     */
    List<Notification> findByUserIdAndIsReadFalseOrderByCreatedAtDesc(String userId);

    /**
     * Find unread notifications by user ID with pagination
     */
    Page<Notification> findByUserIdAndIsReadFalseOrderByCreatedAtDesc(String userId, Pageable pageable);

    /**
     * Find notifications by user ID and type
     */
    List<Notification> findByUserIdAndTypeOrderByCreatedAtDesc(String userId, Notification.NotificationType type);

    /**
     * Find notifications by user ID and priority
     */
    List<Notification> findByUserIdAndPriorityOrderByCreatedAtDesc(String userId, Notification.NotificationPriority priority);

    /**
     * Find notifications by user ID and read status
     */
    List<Notification> findByUserIdAndIsReadOrderByCreatedAtDesc(String userId, boolean isRead);

    /**
     * Count unread notifications by user ID
     */
    long countByUserIdAndIsReadFalse(String userId);

    /**
     * Count notifications by user ID and type
     */
    long countByUserIdAndType(String userId, Notification.NotificationType type);

    /**
     * Find notifications that should be delivered
     */
    @Query("{ 'isDelivered': false, 'expiresAt': { $gt: ?0 }, '$or': [ { 'scheduledAt': null }, { 'scheduledAt': { $lte: ?0 } } ] }")
    List<Notification> findNotificationsToDeliver(LocalDateTime now);

    /**
     * Find notifications by user ID that should be delivered
     */
    @Query("{ 'userId': ?0, 'isDelivered': false, 'expiresAt': { $gt: ?1 }, '$or': [ { 'scheduledAt': null }, { 'scheduledAt': { $lte: ?1 } } ] }")
    List<Notification> findNotificationsToDeliverForUser(String userId, LocalDateTime now);

    /**
     * Find expired notifications
     */
    @Query("{ 'expiresAt': { $lt: ?0 } }")
    List<Notification> findExpiredNotifications(LocalDateTime now);

    /**
     * Find notifications created after date
     */
    List<Notification> findByUserIdAndCreatedAtAfterOrderByCreatedAtDesc(String userId, LocalDateTime date);

    /**
     * Find notifications created between dates
     */
    @Query("{ 'userId': ?0, 'createdAt': { $gte: ?1, $lte: ?2 } }")
    List<Notification> findByUserIdAndCreatedAtBetween(String userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find high priority unread notifications
     */
    @Query("{ 'userId': ?0, 'isRead': false, 'priority': { $in: ['HIGH', 'URGENT'] } }")
    List<Notification> findHighPriorityUnreadNotifications(String userId);

    /**
     * Find notifications by multiple types
     */
    List<Notification> findByUserIdAndTypeInOrderByCreatedAtDesc(String userId, List<Notification.NotificationType> types);

    /**
     * Find recent notifications (last 24 hours)
     */
    @Query("{ 'userId': ?0, 'createdAt': { $gte: ?1 } }")
    List<Notification> findRecentNotifications(String userId, LocalDateTime since);

    /**
     * Find notifications by user ID and delivery status
     */
    List<Notification> findByUserIdAndIsDeliveredOrderByCreatedAtDesc(String userId, boolean isDelivered);

    /**
     * Delete old notifications (older than specified date)
     */
    void deleteByUserIdAndCreatedAtBefore(String userId, LocalDateTime date);

    /**
     * Delete expired notifications
     */
    @Query(value = "{ 'expiresAt': { $lt: ?0 } }", delete = true)
    void deleteExpiredNotifications(LocalDateTime now);

    /**
     * Find notifications by user ID and action URL
     */
    List<Notification> findByUserIdAndActionUrlOrderByCreatedAtDesc(String userId, String actionUrl);

    /**
     * Find scheduled notifications
     */
    @Query("{ 'scheduledAt': { $gte: ?0, $lte: ?1 }, 'isDelivered': false }")
    List<Notification> findScheduledNotifications(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find notifications by user ID with filters
     */
    @Query("{ 'userId': ?0, 'type': { $in: ?1 }, 'isRead': ?2 }")
    Page<Notification> findByUserIdAndTypeInAndIsRead(String userId, List<Notification.NotificationType> types, 
                                                     boolean isRead, Pageable pageable);

    /**
     * Count notifications by user ID and date range
     */
    @Query(value = "{ 'userId': ?0, 'createdAt': { $gte: ?1, $lte: ?2 } }", count = true)
    long countByUserIdAndCreatedAtBetween(String userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find notifications by user ID and priority levels
     */
    List<Notification> findByUserIdAndPriorityInOrderByCreatedAtDesc(String userId, 
                                                                   List<Notification.NotificationPriority> priorities);

    /**
     * Mark notifications as read by IDs
     */
    @Query("{ '_id': { $in: ?0 } }")
    List<Notification> findByIdIn(List<String> ids);

    /**
     * Find undelivered notifications for user
     */
    @Query("{ 'userId': ?0, 'isDelivered': false }")
    List<Notification> findUndeliveredNotifications(String userId);

    /**
     * Search notifications by title or message
     */
    @Query("{ 'userId': ?0, '$or': [ { 'title': { $regex: ?1, $options: 'i' } }, { 'message': { $regex: ?1, $options: 'i' } } ] }")
    Page<Notification> searchNotifications(String userId, String searchTerm, Pageable pageable);

    /**
     * Find notifications by user ID and read status with pagination
     */
    Page<Notification> findByUserIdAndIsReadOrderByCreatedAtDesc(String userId, boolean isRead, Pageable pageable);

    /**
     * Find notifications by user ID and type with pagination
     */
    Page<Notification> findByUserIdAndTypeOrderByCreatedAtDesc(String userId, Notification.NotificationType type, Pageable pageable);

    /**
     * Find notifications by user ID and priority with pagination
     */
    Page<Notification> findByUserIdAndPriorityOrderByCreatedAtDesc(String userId, Notification.NotificationPriority priority, Pageable pageable);

    /**
     * Get notification statistics for user
     */
    @Query(value = "{ 'userId': ?0 }", fields = "{ 'type': 1, 'priority': 1, 'isRead': 1, 'createdAt': 1 }")
    List<Notification> findNotificationStatsForUser(String userId);
}
