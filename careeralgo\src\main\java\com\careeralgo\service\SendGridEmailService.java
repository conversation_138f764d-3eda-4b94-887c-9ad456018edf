package com.careeralgo.service;

import com.careeralgo.model.Application;
import com.careeralgo.model.User;
import com.sendgrid.*;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Content;
import com.sendgrid.helpers.mail.objects.Email;
import com.sendgrid.helpers.mail.objects.Personalization;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * SendGrid email service implementation
 */
@Service
public class SendGridEmailService {

    private static final Logger logger = LoggerFactory.getLogger(SendGridEmailService.class);

    @Value("${careeralgo.email.sendgrid.api-key:}")
    private String apiKey;

    @Value("${careeralgo.email.sendgrid.enabled:false}")
    private boolean enabled;

    @Value("${careeralgo.email.from-address:<EMAIL>}")
    private String fromAddress;

    @Value("${careeralgo.email.from-name:CareerAlgo}")
    private String fromName;

    // SendGrid template IDs
    @Value("${careeralgo.email.templates.welcome:}")
    private String welcomeTemplateId;

    @Value("${careeralgo.email.templates.offer-received:}")
    private String offerReceivedTemplateId;

    @Value("${careeralgo.email.templates.interview-reminder:}")
    private String interviewReminderTemplateId;

    @Value("${careeralgo.email.templates.weekly-summary:}")
    private String weeklySummaryTemplateId;

    @Value("${careeralgo.email.templates.job-alert:}")
    private String jobAlertTemplateId;

    /**
     * Check if SendGrid is properly configured
     */
    private boolean isConfigured() {
        return apiKey != null && !apiKey.isEmpty() && !apiKey.startsWith("your-");
    }

    /**
     * Send welcome email to new user
     */
    public boolean sendWelcomeEmail(User user) {
        if (!enabled || !isConfigured() || !user.getPreferences().isEmailNotifications()) {
            logger.debug("Email disabled, not configured, or user opted out: {}", user.getEmail());
            return false;
        }

        try {
            Map<String, Object> templateData = new HashMap<>();
            templateData.put("firstName", user.getFirstName());
            templateData.put("lastName", user.getLastName());
            templateData.put("dashboardUrl", "https://careeralgo.com/dashboard");

            return sendTemplateEmail(
                    user.getEmail(),
                    "Welcome to CareerAlgo! 🚀",
                    welcomeTemplateId,
                    templateData
            );

        } catch (Exception e) {
            logger.error("Error sending welcome email to: {}", user.getEmail(), e);
            return false;
        }
    }

    /**
     * Send offer received notification
     */
    public boolean sendOfferReceivedNotification(User user, Application application) {
        if (!enabled || !isConfigured() || !user.getPreferences().isEmailNotifications()) {
            return false;
        }

        try {
            Map<String, Object> templateData = new HashMap<>();
            templateData.put("firstName", user.getFirstName());
            templateData.put("applicationId", application.getId());
            templateData.put("jobTitle", "Job Position"); // TODO: Get from job
            templateData.put("companyName", "Company"); // TODO: Get from job
            templateData.put("dashboardUrl", "https://careeralgo.com/applications/" + application.getId());

            if (application.getOffer() != null) {
                templateData.put("salary", application.getOffer().getFormattedSalary());
                templateData.put("deadline", application.getOffer().getDeadline());
            }

            return sendTemplateEmail(
                    user.getEmail(),
                    "🎉 You received a job offer!",
                    offerReceivedTemplateId,
                    templateData
            );

        } catch (Exception e) {
            logger.error("Error sending offer notification to: {}", user.getEmail(), e);
            return false;
        }
    }

    /**
     * Send interview reminder
     */
    public boolean sendInterviewReminder(User user, Application application) {
        if (!enabled || !isConfigured() || !user.getPreferences().isEmailNotifications()) {
            return false;
        }

        try {
            Map<String, Object> templateData = new HashMap<>();
            templateData.put("firstName", user.getFirstName());
            templateData.put("applicationId", application.getId());
            templateData.put("jobTitle", "Job Position"); // TODO: Get from job
            templateData.put("companyName", "Company"); // TODO: Get from job
            templateData.put("dashboardUrl", "https://careeralgo.com/applications/" + application.getId());

            if (application.getInterviews() != null && !application.getInterviews().isEmpty()) {
                var interview = application.getInterviews().get(application.getInterviews().size() - 1);
                templateData.put("interviewDate", interview.getScheduledDate());
                templateData.put("interviewType", interview.getType());
                templateData.put("interviewLocation", interview.getLocation());
            }

            return sendTemplateEmail(
                    user.getEmail(),
                    "📅 Interview Reminder",
                    interviewReminderTemplateId,
                    templateData
            );

        } catch (Exception e) {
            logger.error("Error sending interview reminder to: {}", user.getEmail(), e);
            return false;
        }
    }

    /**
     * Send weekly application summary
     */
    public boolean sendWeeklySummary(User user, Map<String, Object> summaryData) {
        if (!enabled || !isConfigured() || !user.getPreferences().isWeeklyReports()) {
            return false;
        }

        try {
            Map<String, Object> templateData = new HashMap<>();
            templateData.put("firstName", user.getFirstName());
            templateData.put("dashboardUrl", "https://careeralgo.com/dashboard");
            templateData.putAll(summaryData);

            return sendTemplateEmail(
                    user.getEmail(),
                    "📊 Your Weekly Job Search Summary",
                    weeklySummaryTemplateId,
                    templateData
            );

        } catch (Exception e) {
            logger.error("Error sending weekly summary to: {}", user.getEmail(), e);
            return false;
        }
    }

    /**
     * Send job alert notification
     */
    public boolean sendJobAlert(User user, String jobTitle, int jobCount, String searchUrl) {
        if (!enabled || !isConfigured() || !user.getPreferences().isJobAlerts()) {
            return false;
        }

        try {
            Map<String, Object> templateData = new HashMap<>();
            templateData.put("firstName", user.getFirstName());
            templateData.put("jobTitle", jobTitle);
            templateData.put("jobCount", jobCount);
            templateData.put("searchUrl", searchUrl);
            templateData.put("dashboardUrl", "https://careeralgo.com/jobs");

            return sendTemplateEmail(
                    user.getEmail(),
                    String.format("🔔 %d new %s jobs found", jobCount, jobTitle),
                    jobAlertTemplateId,
                    templateData
            );

        } catch (Exception e) {
            logger.error("Error sending job alert to: {}", user.getEmail(), e);
            return false;
        }
    }

    /**
     * Send custom email with template
     */
    public boolean sendTemplateEmail(String toEmail, String subject, String templateId,
                                   Map<String, Object> templateData) {
        if (!enabled || !isConfigured()) {
            logger.debug("SendGrid email service disabled or not configured");
            return false;
        }

        try {
            Email from = new Email(fromAddress, fromName);
            Email to = new Email(toEmail);

            Mail mail;

            if (templateId != null && !templateId.isEmpty()) {
                // Use SendGrid template
                mail = new Mail();
                mail.setFrom(from);
                mail.setSubject(subject);
                mail.setTemplateId(templateId);

                Personalization personalization = new Personalization();
                personalization.addTo(to);

                // Add template data
                if (templateData != null) {
                    for (Map.Entry<String, Object> entry : templateData.entrySet()) {
                        personalization.addDynamicTemplateData(entry.getKey(), entry.getValue());
                    }
                }

                mail.addPersonalization(personalization);
            } else {
                // Use plain text/HTML content
                Content content = new Content("text/html", buildDefaultEmailContent(subject, templateData));
                mail = new Mail(from, subject, to, content);
            }

            SendGrid sg = new SendGrid(apiKey);
            Request request = new Request();

            request.setMethod(Method.POST);
            request.setEndpoint("mail/send");
            request.setBody(mail.build());

            Response response = sg.api(request);

            if (response.getStatusCode() >= 200 && response.getStatusCode() < 300) {
                logger.info("Email sent successfully to: {}", toEmail);
                return true;
            } else {
                logger.warn("SendGrid API returned status: {} for email to: {}",
                        response.getStatusCode(), toEmail);
                return false;
            }

        } catch (IOException e) {
            logger.error("Error sending email via SendGrid to: {}", toEmail, e);
            return false;
        }
    }

    /**
     * Send plain text email
     */
    public boolean sendPlainEmail(String toEmail, String subject, String content) {
        if (!enabled || !isConfigured()) {
            return false;
        }

        try {
            Email from = new Email(fromAddress, fromName);
            Email to = new Email(toEmail);
            Content emailContent = new Content("text/plain", content);

            Mail mail = new Mail(from, subject, to, emailContent);

            SendGrid sg = new SendGrid(apiKey);
            Request request = new Request();

            request.setMethod(Method.POST);
            request.setEndpoint("mail/send");
            request.setBody(mail.build());

            Response response = sg.api(request);

            return response.getStatusCode() >= 200 && response.getStatusCode() < 300;

        } catch (IOException e) {
            logger.error("Error sending plain email via SendGrid to: {}", toEmail, e);
            return false;
        }
    }

    /**
     * Send HTML email
     */
    public boolean sendHtmlEmail(String toEmail, String subject, String htmlContent) {
        if (!enabled || !isConfigured()) {
            return false;
        }

        try {
            Email from = new Email(fromAddress, fromName);
            Email to = new Email(toEmail);
            Content content = new Content("text/html", htmlContent);

            Mail mail = new Mail(from, subject, to, content);

            SendGrid sg = new SendGrid(apiKey);
            Request request = new Request();

            request.setMethod(Method.POST);
            request.setEndpoint("mail/send");
            request.setBody(mail.build());

            Response response = sg.api(request);

            return response.getStatusCode() >= 200 && response.getStatusCode() < 300;

        } catch (IOException e) {
            logger.error("Error sending HTML email via SendGrid to: {}", toEmail, e);
            return false;
        }
    }

    /**
     * Build default email content when no template is available
     */
    private String buildDefaultEmailContent(String subject, Map<String, Object> data) {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html><head><title>").append(subject).append("</title></head>");
        html.append("<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>");
        html.append("<div style='max-width: 600px; margin: 0 auto; padding: 20px;'>");
        html.append("<h2 style='color: #2c3e50;'>").append(subject).append("</h2>");

        if (data != null && data.containsKey("firstName")) {
            html.append("<p>Hi ").append(data.get("firstName")).append(",</p>");
        }

        html.append("<p>Thank you for using CareerAlgo!</p>");

        if (data != null) {
            html.append("<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>");
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                if (!"firstName".equals(entry.getKey()) && !"lastName".equals(entry.getKey())) {
                    html.append("<p><strong>").append(entry.getKey()).append(":</strong> ")
                        .append(entry.getValue()).append("</p>");
                }
            }
            html.append("</div>");
        }

        html.append("<p>Best regards,<br>The CareerAlgo Team</p>");
        html.append("<hr style='border: none; border-top: 1px solid #eee; margin: 30px 0;'>");
        html.append("<p style='font-size: 12px; color: #666;'>");
        html.append("This email was sent by CareerAlgo. ");
        html.append("If you no longer wish to receive these emails, you can ");
        html.append("<a href='https://careeralgo.com/unsubscribe'>unsubscribe here</a>.");
        html.append("</p>");
        html.append("</div></body></html>");

        return html.toString();
    }
}
