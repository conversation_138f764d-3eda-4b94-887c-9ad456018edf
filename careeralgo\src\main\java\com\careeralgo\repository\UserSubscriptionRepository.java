package com.careeralgo.repository;

import com.careeralgo.constant.SubscriptionPlan;
import com.careeralgo.model.UserSubscription;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for UserSubscription operations
 */
@Repository
public interface UserSubscriptionRepository extends MongoRepository<UserSubscription, String> {

    /**
     * Find subscription by user ID
     */
    Optional<UserSubscription> findByUserId(String userId);

    /**
     * Find active subscription by user ID
     */
    @Query("{ 'userId': ?0, 'status': { $in: ['ACTIVE', 'TRIALING'] } }")
    Optional<UserSubscription> findActiveSubscriptionByUserId(String userId);

    /**
     * Find subscriptions by plan
     */
    List<UserSubscription> findByPlan(SubscriptionPlan plan);

    /**
     * Find subscriptions by status
     */
    List<UserSubscription> findByStatus(UserSubscription.SubscriptionStatus status);

    /**
     * Find subscriptions by plan and status
     */
    List<UserSubscription> findByPlanAndStatus(SubscriptionPlan plan, UserSubscription.SubscriptionStatus status);

    /**
     * Find subscriptions by Stripe customer ID
     */
    Optional<UserSubscription> findByStripeCustomerId(String stripeCustomerId);

    /**
     * Find subscriptions by Stripe subscription ID
     */
    Optional<UserSubscription> findByStripeSubscriptionId(String stripeSubscriptionId);

    /**
     * Find subscriptions expiring soon
     */
    @Query("{ 'currentPeriodEnd': { $lte: ?0 }, 'status': 'ACTIVE' }")
    List<UserSubscription> findSubscriptionsExpiringBefore(LocalDateTime date);

    /**
     * Find expired subscriptions
     */
    @Query("{ 'currentPeriodEnd': { $lt: ?0 }, 'status': 'ACTIVE' }")
    List<UserSubscription> findExpiredSubscriptions(LocalDateTime now);

    /**
     * Find trial subscriptions expiring soon
     */
    @Query("{ 'trialEnd': { $lte: ?0 }, 'isTrialActive': true }")
    List<UserSubscription> findTrialsExpiringBefore(LocalDateTime date);

    /**
     * Find expired trial subscriptions
     */
    @Query("{ 'trialEnd': { $lt: ?0 }, 'isTrialActive': true }")
    List<UserSubscription> findExpiredTrials(LocalDateTime now);

    /**
     * Find subscriptions created between dates
     */
    @Query("{ 'createdAt': { $gte: ?0, $lte: ?1 } }")
    List<UserSubscription> findSubscriptionsCreatedBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find subscriptions updated after date
     */
    List<UserSubscription> findByUpdatedAtAfter(LocalDateTime date);

    /**
     * Find subscriptions with specific billing cycle
     */
    List<UserSubscription> findByBillingCycle(UserSubscription.BillingCycle billingCycle);

    /**
     * Find subscriptions marked for cancellation at period end
     */
    @Query("{ 'cancelAtPeriodEnd': true, 'status': 'ACTIVE' }")
    List<UserSubscription> findSubscriptionsToCancel();

    /**
     * Find subscriptions by multiple statuses
     */
    List<UserSubscription> findByStatusIn(List<UserSubscription.SubscriptionStatus> statuses);

    /**
     * Count subscriptions by plan
     */
    long countByPlan(SubscriptionPlan plan);

    /**
     * Count subscriptions by status
     */
    long countByStatus(UserSubscription.SubscriptionStatus status);

    /**
     * Count active subscriptions
     */
    @Query(value = "{ 'status': { $in: ['ACTIVE', 'TRIALING'] } }", count = true)
    long countActiveSubscriptions();

    /**
     * Find subscriptions with past due payments
     */
    @Query("{ 'status': 'PAST_DUE' }")
    List<UserSubscription> findPastDueSubscriptions();

    /**
     * Find subscriptions that need usage reset
     */
    @Query("{ 'usageTracking.lastResetDate': { $lt: ?0 } }")
    List<UserSubscription> findSubscriptionsNeedingUsageReset(LocalDateTime firstOfMonth);

    /**
     * Find subscriptions by plan with pagination
     */
    Page<UserSubscription> findByPlan(SubscriptionPlan plan, Pageable pageable);

    /**
     * Find subscriptions by status with pagination
     */
    Page<UserSubscription> findByStatus(UserSubscription.SubscriptionStatus status, Pageable pageable);

    /**
     * Find all subscriptions with pagination
     */
    Page<UserSubscription> findAllByOrderByCreatedAtDesc(Pageable pageable);

    /**
     * Find subscriptions by user ID list
     */
    List<UserSubscription> findByUserIdIn(List<String> userIds);

    /**
     * Find subscriptions with high usage
     */
    @Query("{ '$or': [ " +
           "{ 'usageTracking.currentMonthApplications': { $gte: ?0 } }, " +
           "{ 'usageTracking.currentMonthAiAnalyses': { $gte: ?1 } } ] }")
    List<UserSubscription> findHighUsageSubscriptions(int applicationThreshold, int aiThreshold);

    /**
     * Find subscriptions approaching usage limits
     */
    @Query("{ '$and': [ " +
           "{ 'usageLimits.monthlyApplications': { $ne: -1 } }, " +
           "{ '$expr': { '$gte': [ '$usageTracking.currentMonthApplications', { '$multiply': [ '$usageLimits.monthlyApplications', 0.8 ] } ] } } ] }")
    List<UserSubscription> findSubscriptionsApproachingLimits();

    /**
     * Find subscriptions by currency
     */
    List<UserSubscription> findByCurrency(String currency);

    /**
     * Find subscriptions created in date range with specific plan
     */
    @Query("{ 'plan': ?0, 'createdAt': { $gte: ?1, $lte: ?2 } }")
    List<UserSubscription> findByPlanAndCreatedAtBetween(SubscriptionPlan plan, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find subscriptions with payment method
     */
    @Query("{ 'paymentMethodId': { $exists: true, $ne: null } }")
    List<UserSubscription> findSubscriptionsWithPaymentMethod();

    /**
     * Find subscriptions without payment method
     */
    @Query("{ '$or': [ { 'paymentMethodId': { $exists: false } }, { 'paymentMethodId': null } ] }")
    List<UserSubscription> findSubscriptionsWithoutPaymentMethod();

    /**
     * Delete subscriptions older than specified date
     */
    void deleteByCreatedAtBeforeAndStatus(LocalDateTime date, UserSubscription.SubscriptionStatus status);

    /**
     * Find subscriptions by amount range
     */
    @Query("{ 'amount': { $gte: ?0, $lte: ?1 } }")
    List<UserSubscription> findByAmountBetween(java.math.BigDecimal minAmount, java.math.BigDecimal maxAmount);

    /**
     * Find subscriptions with metadata key
     */
    @Query("{ 'metadata.?0': { $exists: true } }")
    List<UserSubscription> findByMetadataKey(String key);

    /**
     * Find subscriptions with specific metadata value
     */
    @Query("{ 'metadata.?0': ?1 }")
    List<UserSubscription> findByMetadataKeyValue(String key, Object value);

    /**
     * Get subscription statistics
     */
    @Query(value = "{ }", fields = "{ 'plan': 1, 'status': 1, 'amount': 1, 'billingCycle': 1, 'createdAt': 1 }")
    List<UserSubscription> findSubscriptionStats();
}
