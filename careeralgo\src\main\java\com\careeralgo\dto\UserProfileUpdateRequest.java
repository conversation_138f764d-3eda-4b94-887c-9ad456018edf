package com.careeralgo.dto;

import com.careeralgo.constant.ExperienceLevel;
import com.careeralgo.model.Profile;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * DTO for updating user profile information
 */
public class UserProfileUpdateRequest {

    @Size(max = 100, message = "Job title must not exceed 100 characters")
    private String jobTitle;

    @Size(max = 100, message = "Industry must not exceed 100 characters")
    private String industry;

    private ExperienceLevel experienceLevel;

    private LocationDto location;

    private Profile.RemotePreference remotePreference;

    private SalaryExpectationDto salaryExpectation;

    private List<String> skills;

    @Size(max = 200, message = "LinkedIn profile URL must not exceed 200 characters")
    private String linkedinProfile;

    @Size(max = 200, message = "GitHub profile URL must not exceed 200 characters")
    private String githubProfile;

    @Size(max = 200, message = "Personal website URL must not exceed 200 characters")
    private String personalWebsite;

    @Size(max = 1000, message = "Bio must not exceed 1000 characters")
    private String bio;

    @Size(max = 20, message = "Phone number must not exceed 20 characters")
    private String phoneNumber;

    // Constructors
    public UserProfileUpdateRequest() {}

    // Getters and Setters
    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public ExperienceLevel getExperienceLevel() {
        return experienceLevel;
    }

    public void setExperienceLevel(ExperienceLevel experienceLevel) {
        this.experienceLevel = experienceLevel;
    }

    public LocationDto getLocation() {
        return location;
    }

    public void setLocation(LocationDto location) {
        this.location = location;
    }

    public Profile.RemotePreference getRemotePreference() {
        return remotePreference;
    }

    public void setRemotePreference(Profile.RemotePreference remotePreference) {
        this.remotePreference = remotePreference;
    }

    public SalaryExpectationDto getSalaryExpectation() {
        return salaryExpectation;
    }

    public void setSalaryExpectation(SalaryExpectationDto salaryExpectation) {
        this.salaryExpectation = salaryExpectation;
    }

    public List<String> getSkills() {
        return skills;
    }

    public void setSkills(List<String> skills) {
        this.skills = skills;
    }

    public String getLinkedinProfile() {
        return linkedinProfile;
    }

    public void setLinkedinProfile(String linkedinProfile) {
        this.linkedinProfile = linkedinProfile;
    }

    public String getGithubProfile() {
        return githubProfile;
    }

    public void setGithubProfile(String githubProfile) {
        this.githubProfile = githubProfile;
    }

    public String getPersonalWebsite() {
        return personalWebsite;
    }

    public void setPersonalWebsite(String personalWebsite) {
        this.personalWebsite = personalWebsite;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    /**
     * Location DTO
     */
    public static class LocationDto {
        private String city;
        private String state;
        private String country;

        // Constructors
        public LocationDto() {}

        public LocationDto(String city, String state, String country) {
            this.city = city;
            this.state = state;
            this.country = country;
        }

        // Getters and Setters
        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }
    }

    /**
     * Salary expectation DTO
     */
    public static class SalaryExpectationDto {
        private Integer min;
        private Integer max;
        private String currency;

        // Constructors
        public SalaryExpectationDto() {}

        public SalaryExpectationDto(Integer min, Integer max, String currency) {
            this.min = min;
            this.max = max;
            this.currency = currency;
        }

        // Getters and Setters
        public Integer getMin() {
            return min;
        }

        public void setMin(Integer min) {
            this.min = min;
        }

        public Integer getMax() {
            return max;
        }

        public void setMax(Integer max) {
            this.max = max;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }
}
