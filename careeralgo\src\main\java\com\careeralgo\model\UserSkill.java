package com.careeralgo.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.index.CompoundIndex;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * UserSkill model representing a user's skill with proficiency level
 */
@Document(collection = "user_skills")
@CompoundIndex(def = "{'userId': 1, 'skillId': 1}", unique = true)
public class UserSkill {

    @Id
    private String id;

    @Indexed
    private String userId;

    @Indexed
    private String skillId;

    private ProficiencyLevel proficiencyLevel;

    private Double yearsOfExperience;

    private SkillSource source;

    private LocalDate lastUsed;

    private Boolean verified;

    private Integer endorsements;

    private String notes;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    // Enums
    public enum ProficiencyLevel {
        BEGINNER(1, "Beginner"),
        INTERMEDIATE(2, "Intermediate"),
        ADVANCED(3, "Advanced"),
        EXPERT(4, "Expert");

        private final int level;
        private final String displayName;

        ProficiencyLevel(int level, String displayName) {
            this.level = level;
            this.displayName = displayName;
        }

        public int getLevel() {
            return level;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    public enum SkillSource {
        MANUAL("Manual Entry"),
        RESUME_PARSED("Resume Parsing"),
        LINKEDIN_IMPORT("LinkedIn Import"),
        ASSESSMENT("Skill Assessment"),
        CERTIFICATION("Certification"),
        PROJECT("Project Experience");

        private final String displayName;

        SkillSource(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // Constructors
    public UserSkill() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.verified = false;
        this.endorsements = 0;
    }

    public UserSkill(String userId, String skillId, ProficiencyLevel proficiencyLevel) {
        this();
        this.userId = userId;
        this.skillId = skillId;
        this.proficiencyLevel = proficiencyLevel;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
        this.updatedAt = LocalDateTime.now();
    }

    public String getSkillId() {
        return skillId;
    }

    public void setSkillId(String skillId) {
        this.skillId = skillId;
        this.updatedAt = LocalDateTime.now();
    }

    public ProficiencyLevel getProficiencyLevel() {
        return proficiencyLevel;
    }

    public void setProficiencyLevel(ProficiencyLevel proficiencyLevel) {
        this.proficiencyLevel = proficiencyLevel;
        this.updatedAt = LocalDateTime.now();
    }

    public Double getYearsOfExperience() {
        return yearsOfExperience;
    }

    public void setYearsOfExperience(Double yearsOfExperience) {
        this.yearsOfExperience = yearsOfExperience;
        this.updatedAt = LocalDateTime.now();
    }

    public SkillSource getSource() {
        return source;
    }

    public void setSource(SkillSource source) {
        this.source = source;
        this.updatedAt = LocalDateTime.now();
    }

    public LocalDate getLastUsed() {
        return lastUsed;
    }

    public void setLastUsed(LocalDate lastUsed) {
        this.lastUsed = lastUsed;
        this.updatedAt = LocalDateTime.now();
    }

    public Boolean getVerified() {
        return verified;
    }

    public void setVerified(Boolean verified) {
        this.verified = verified;
        this.updatedAt = LocalDateTime.now();
    }

    public Integer getEndorsements() {
        return endorsements;
    }

    public void setEndorsements(Integer endorsements) {
        this.endorsements = endorsements;
        this.updatedAt = LocalDateTime.now();
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
        this.updatedAt = LocalDateTime.now();
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Helper methods
    public boolean isVerified() {
        return verified != null && verified;
    }

    public int getProficiencyScore() {
        return proficiencyLevel != null ? proficiencyLevel.getLevel() : 0;
    }

    public boolean isRecent() {
        if (lastUsed == null) return false;
        return lastUsed.isAfter(LocalDate.now().minusYears(2));
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserSkill userSkill = (UserSkill) o;
        return id != null ? id.equals(userSkill.id) : userSkill.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "UserSkill{" +
                "id='" + id + '\'' +
                ", userId='" + userId + '\'' +
                ", skillId='" + skillId + '\'' +
                ", proficiencyLevel=" + proficiencyLevel +
                ", yearsOfExperience=" + yearsOfExperience +
                ", verified=" + verified +
                '}';
    }
}
