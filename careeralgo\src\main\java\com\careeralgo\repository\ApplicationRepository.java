package com.careeralgo.repository;

import com.careeralgo.constant.ApplicationStatus;
import com.careeralgo.model.Application;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Application document operations
 */
@Repository
public interface ApplicationRepository extends MongoRepository<Application, String> {

    /**
     * Find all applications for a specific user
     */
    Page<Application> findByUserIdOrderByCreatedAtDesc(String userId, Pageable pageable);

    /**
     * Find applications by user and status
     */
    Page<Application> findByUserIdAndStatusOrderByCreatedAtDesc(
            String userId, ApplicationStatus status, Pageable pageable);

    /**
     * Find applications for a specific job
     */
    Page<Application> findByJobIdOrderByCreatedAtDesc(String jobId, Pageable pageable);

    /**
     * Find application by user and job
     */
    Optional<Application> findByUserIdAndJobId(String userId, String jobId);

    /**
     * Check if user has already applied to job
     */
    boolean existsByUserIdAndJobId(String userId, String jobId);

    /**
     * Find applications by status
     */
    Page<Application> findByStatusOrderByCreatedAtDesc(ApplicationStatus status, Pageable pageable);

    /**
     * Find active applications for user
     */
    @Query("{ 'userId': ?0, 'status': { $nin: ['REJECTED', 'WITHDRAWN', 'HIRED'] } }")
    List<Application> findActiveApplicationsByUserId(String userId);

    /**
     * Find applications with upcoming interviews
     */
    @Query("{ 'interviews.scheduledDate': { $gte: ?0, $lte: ?1 } }")
    List<Application> findApplicationsWithUpcomingInterviews(
            LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find applications needing follow-up
     */
    @Query("{ 'followUpReminder': { $lte: ?0 }, " +
           "'status': { $nin: ['REJECTED', 'WITHDRAWN', 'HIRED'] } }")
    List<Application> findApplicationsNeedingFollowUp(LocalDateTime currentDate);

    /**
     * Find applications by priority
     */
    List<Application> findByUserIdAndPriorityOrderByCreatedAtDesc(
            String userId, Application.Priority priority);

    /**
     * Find applications with offers
     */
    @Query("{ 'offer': { $exists: true, $ne: null } }")
    List<Application> findApplicationsWithOffers();

    /**
     * Find applications with pending offers
     */
    @Query("{ 'offer.status': 'PENDING', 'offer.deadline': { $gte: ?0 } }")
    List<Application> findApplicationsWithPendingOffers(LocalDateTime currentDate);

    /**
     * Find applications with expired offers
     */
    @Query("{ 'offer.status': 'PENDING', 'offer.deadline': { $lt: ?0 } }")
    List<Application> findApplicationsWithExpiredOffers(LocalDateTime currentDate);

    /**
     * Find applications by resume
     */
    List<Application> findByResumeId(String resumeId);

    /**
     * Find applications created between dates
     */
    List<Application> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find applications applied between dates
     */
    List<Application> findByAppliedDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Count applications by user
     */
    long countByUserId(String userId);

    /**
     * Count applications by user and status
     */
    long countByUserIdAndStatus(String userId, ApplicationStatus status);

    /**
     * Count applications for job
     */
    long countByJobId(String jobId);

    /**
     * Find applications with high match scores
     */
    @Query("{ 'matchScore': { $gte: ?0 } }")
    List<Application> findApplicationsWithHighMatchScore(int threshold);

    /**
     * Find applications by application method
     */
    List<Application> findByApplicationMethod(Application.ApplicationMethod method);

    /**
     * Find applications with customized resumes
     */
    @Query("{ 'customizedResume': true }")
    List<Application> findApplicationsWithCustomizedResumes();

    /**
     * Find recent applications
     */
    @Query(value = "{}", sort = "{ 'createdAt': -1 }")
    List<Application> findRecentApplications(Pageable pageable);

    /**
     * Get application statistics for user
     */
    @Query("{ 'userId': ?0 }")
    List<Application> findAllByUserId(String userId);

    /**
     * Find applications by status list
     */
    @Query("{ 'status': { $in: ?0 } }")
    List<Application> findByStatusIn(List<ApplicationStatus> statuses);

    /**
     * Find applications with notes
     */
    @Query("{ 'notes': { $exists: true, $ne: null, $ne: '' } }")
    List<Application> findApplicationsWithNotes();

    /**
     * Search applications by job title or company
     */
    @Query("{ 'userId': ?0, $or: [ " +
           "{ 'jobTitle': { $regex: ?1, $options: 'i' } }, " +
           "{ 'companyName': { $regex: ?1, $options: 'i' } } " +
           "] }")
    Page<Application> searchUserApplications(String userId, String searchTerm, Pageable pageable);

    /**
     * Find applications with interview feedback
     */
    @Query("{ 'interviews.feedback': { $exists: true, $ne: null } }")
    List<Application> findApplicationsWithInterviewFeedback();

    /**
     * Find applications by date range and status
     */
    @Query("{ 'createdAt': { $gte: ?0, $lte: ?1 }, 'status': ?2 }")
    List<Application> findByDateRangeAndStatus(
            LocalDateTime startDate, LocalDateTime endDate, ApplicationStatus status);

    /**
     * Get user's application timeline
     */
    @Query(value = "{ 'userId': ?0 }", sort = "{ 'appliedDate': -1 }")
    List<Application> findUserApplicationTimeline(String userId);

    /**
     * Count applications by user ID and status list
     */
    long countByUserIdAndStatusIn(String userId, List<ApplicationStatus> statuses);

    /**
     * Count applications by user ID created after date
     */
    long countByUserIdAndCreatedAtAfter(String userId, LocalDateTime date);

    /**
     * Count applications by user ID created between dates
     */
    long countByUserIdAndCreatedAtBetween(String userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Count applications by user ID, status list, and created after date
     */
    long countByUserIdAndStatusInAndCreatedAtAfter(String userId, List<ApplicationStatus> statuses, LocalDateTime date);
}
