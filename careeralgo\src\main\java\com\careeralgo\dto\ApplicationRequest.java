package com.careeralgo.dto;

import com.careeralgo.model.Application;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * DTO for creating new job application
 */
public class ApplicationRequest {

    @NotBlank(message = "Job ID is required")
    private String jobId;

    private String resumeId;

    @Size(max = 5000, message = "Cover letter must not exceed 5000 characters")
    private String coverLetter;

    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    private String notes;

    private Application.Priority priority;

    private Application.ApplicationMethod applicationMethod;

    // Constructors
    public ApplicationRequest() {}

    public ApplicationRequest(String jobId, String resumeId, String coverLetter) {
        this.jobId = jobId;
        this.resumeId = resumeId;
        this.coverLetter = coverLetter;
    }

    // Getters and Setters
    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getResumeId() {
        return resumeId;
    }

    public void setResumeId(String resumeId) {
        this.resumeId = resumeId;
    }

    public String getCoverLetter() {
        return coverLetter;
    }

    public void setCoverLetter(String coverLetter) {
        this.coverLetter = coverLetter;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Application.Priority getPriority() {
        return priority;
    }

    public void setPriority(Application.Priority priority) {
        this.priority = priority;
    }

    public Application.ApplicationMethod getApplicationMethod() {
        return applicationMethod;
    }

    public void setApplicationMethod(Application.ApplicationMethod applicationMethod) {
        this.applicationMethod = applicationMethod;
    }
}
