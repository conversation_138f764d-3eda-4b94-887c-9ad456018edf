package com.careeralgo.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;

/**
 * Analytics model for tracking user activity and platform metrics
 */
@Document(collection = "analytics")
public class Analytics {

    @Id
    private String id;

    @Indexed
    private String userId;

    @Indexed
    private String eventType;

    private String eventCategory;

    private Map<String, Object> eventData;

    private Map<String, Object> userProperties;

    private Map<String, Object> sessionProperties;

    @Indexed
    private LocalDateTime timestamp;

    private String sessionId;

    private String ipAddress;

    private String userAgent;

    private String platform;

    private String source;

    private String medium;

    private String campaign;

    // Nested classes for specific analytics types
    public static class UserActivity {
        public static final String LOGIN = "user_login";
        public static final String LOGOUT = "user_logout";
        public static final String PROFILE_UPDATE = "profile_update";
        public static final String RESUME_UPLOAD = "resume_upload";
        public static final String JOB_SEARCH = "job_search";
        public static final String JOB_APPLICATION = "job_application";
        public static final String SKILL_ASSESSMENT = "skill_assessment";
        public static final String INTERVIEW_PREP = "interview_prep";
        public static final String CAREER_ADVICE = "career_advice";
    }

    public static class SystemMetrics {
        public static final String API_CALL = "api_call";
        public static final String ERROR_OCCURRED = "error_occurred";
        public static final String PERFORMANCE_METRIC = "performance_metric";
        public static final String FEATURE_USAGE = "feature_usage";
    }

    public static class BusinessMetrics {
        public static final String SUBSCRIPTION_CREATED = "subscription_created";
        public static final String SUBSCRIPTION_CANCELLED = "subscription_cancelled";
        public static final String PAYMENT_PROCESSED = "payment_processed";
        public static final String TRIAL_STARTED = "trial_started";
        public static final String CONVERSION = "conversion";
    }

    // Constructors
    public Analytics() {
        this.timestamp = LocalDateTime.now();
        this.eventData = new HashMap<>();
        this.userProperties = new HashMap<>();
        this.sessionProperties = new HashMap<>();
    }

    public Analytics(String userId, String eventType) {
        this();
        this.userId = userId;
        this.eventType = eventType;
    }

    public Analytics(String userId, String eventType, String eventCategory) {
        this(userId, eventType);
        this.eventCategory = eventCategory;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventCategory() {
        return eventCategory;
    }

    public void setEventCategory(String eventCategory) {
        this.eventCategory = eventCategory;
    }

    public Map<String, Object> getEventData() {
        return eventData;
    }

    public void setEventData(Map<String, Object> eventData) {
        this.eventData = eventData;
    }

    public Map<String, Object> getUserProperties() {
        return userProperties;
    }

    public void setUserProperties(Map<String, Object> userProperties) {
        this.userProperties = userProperties;
    }

    public Map<String, Object> getSessionProperties() {
        return sessionProperties;
    }

    public void setSessionProperties(Map<String, Object> sessionProperties) {
        this.sessionProperties = sessionProperties;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getMedium() {
        return medium;
    }

    public void setMedium(String medium) {
        this.medium = medium;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    // Helper methods
    public void addEventData(String key, Object value) {
        if (this.eventData == null) {
            this.eventData = new HashMap<>();
        }
        this.eventData.put(key, value);
    }

    public void addUserProperty(String key, Object value) {
        if (this.userProperties == null) {
            this.userProperties = new HashMap<>();
        }
        this.userProperties.put(key, value);
    }

    public void addSessionProperty(String key, Object value) {
        if (this.sessionProperties == null) {
            this.sessionProperties = new HashMap<>();
        }
        this.sessionProperties.put(key, value);
    }

    public Object getEventDataValue(String key) {
        return eventData != null ? eventData.get(key) : null;
    }

    public Object getUserPropertyValue(String key) {
        return userProperties != null ? userProperties.get(key) : null;
    }

    public Object getSessionPropertyValue(String key) {
        return sessionProperties != null ? sessionProperties.get(key) : null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Analytics analytics = (Analytics) o;
        return id != null ? id.equals(analytics.id) : analytics.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "Analytics{" +
                "id='" + id + '\'' +
                ", userId='" + userId + '\'' +
                ", eventType='" + eventType + '\'' +
                ", eventCategory='" + eventCategory + '\'' +
                ", timestamp=" + timestamp +
                ", sessionId='" + sessionId + '\'' +
                '}';
    }
}
