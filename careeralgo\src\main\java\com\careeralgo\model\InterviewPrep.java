package com.careeralgo.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Interview preparation session model
 */
@Document(collection = "interview_prep")
public class InterviewPrep {

    @Id
    private String id;
    private String userId;
    private String sessionId;
    private String jobId; // Optional - if practicing for specific job
    private SessionType type;
    private String sessionName;
    private List<InterviewQuestion> questions;
    private SessionStats sessionStats;
    private CompanyResearch companyResearch;
    private SessionFeedback feedback;
    private boolean isCompleted;
    private LocalDateTime completedAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public InterviewPrep() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.isCompleted = false;
    }

    public InterviewPrep(String userId, SessionType type, String sessionName) {
        this();
        this.userId = userId;
        this.type = type;
        this.sessionName = sessionName;
        this.sessionId = generateSessionId();
    }

    // Enums
    public enum SessionType {
        MOCK_INTERVIEW("Mock Interview"),
        QUESTION_PRACTICE("Question Practice"),
        COMPANY_RESEARCH("Company Research"),
        TECHNICAL_PREP("Technical Preparation"),
        BEHAVIORAL_PREP("Behavioral Preparation");

        private final String displayName;

        SessionType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // Nested classes
    public static class InterviewQuestion {
        private String questionId;
        private String question;
        private QuestionCategory category;
        private QuestionDifficulty difficulty;
        private Integer timeLimit; // seconds
        private String userAnswer;
        private String audioRecordingUrl; // Optional voice recording
        private Integer answerDuration; // seconds
        private AIEvaluation aiEvaluation;
        private Integer attemptCount;
        private Integer bestScore;
        private LocalDateTime answeredAt;

        // Constructors
        public InterviewQuestion() {}

        public InterviewQuestion(String question, QuestionCategory category, QuestionDifficulty difficulty) {
            this.question = question;
            this.category = category;
            this.difficulty = difficulty;
            this.attemptCount = 0;
        }

        // Getters and setters
        public String getQuestionId() { return questionId; }
        public void setQuestionId(String questionId) { this.questionId = questionId; }
        public String getQuestion() { return question; }
        public void setQuestion(String question) { this.question = question; }
        public QuestionCategory getCategory() { return category; }
        public void setCategory(QuestionCategory category) { this.category = category; }
        public QuestionDifficulty getDifficulty() { return difficulty; }
        public void setDifficulty(QuestionDifficulty difficulty) { this.difficulty = difficulty; }
        public Integer getTimeLimit() { return timeLimit; }
        public void setTimeLimit(Integer timeLimit) { this.timeLimit = timeLimit; }
        public String getUserAnswer() { return userAnswer; }
        public void setUserAnswer(String userAnswer) { this.userAnswer = userAnswer; }
        public String getAudioRecordingUrl() { return audioRecordingUrl; }
        public void setAudioRecordingUrl(String audioRecordingUrl) { this.audioRecordingUrl = audioRecordingUrl; }
        public Integer getAnswerDuration() { return answerDuration; }
        public void setAnswerDuration(Integer answerDuration) { this.answerDuration = answerDuration; }
        public AIEvaluation getAiEvaluation() { return aiEvaluation; }
        public void setAiEvaluation(AIEvaluation aiEvaluation) { this.aiEvaluation = aiEvaluation; }
        public Integer getAttemptCount() { return attemptCount; }
        public void setAttemptCount(Integer attemptCount) { this.attemptCount = attemptCount; }
        public Integer getBestScore() { return bestScore; }
        public void setBestScore(Integer bestScore) { this.bestScore = bestScore; }
        public LocalDateTime getAnsweredAt() { return answeredAt; }
        public void setAnsweredAt(LocalDateTime answeredAt) { this.answeredAt = answeredAt; }
    }

    public static class AIEvaluation {
        private Integer overallScore; // 0-100
        private Map<String, Integer> criteria; // clarity, relevance, structure, confidence
        private String feedback;
        private List<String> improvements;
        private String suggestedAnswer;

        // Constructors
        public AIEvaluation() {}

        // Getters and setters
        public Integer getOverallScore() { return overallScore; }
        public void setOverallScore(Integer overallScore) { this.overallScore = overallScore; }
        public Map<String, Integer> getCriteria() { return criteria; }
        public void setCriteria(Map<String, Integer> criteria) { this.criteria = criteria; }
        public String getFeedback() { return feedback; }
        public void setFeedback(String feedback) { this.feedback = feedback; }
        public List<String> getImprovements() { return improvements; }
        public void setImprovements(List<String> improvements) { this.improvements = improvements; }
        public String getSuggestedAnswer() { return suggestedAnswer; }
        public void setSuggestedAnswer(String suggestedAnswer) { this.suggestedAnswer = suggestedAnswer; }
    }

    public static class SessionStats {
        private Integer totalQuestions;
        private Integer completedQuestions;
        private Integer averageScore;
        private Integer totalDuration; // seconds
        private List<String> strongCategories;
        private List<String> weakCategories;
        private List<String> improvementAreas;

        // Constructors
        public SessionStats() {}

        // Getters and setters
        public Integer getTotalQuestions() { return totalQuestions; }
        public void setTotalQuestions(Integer totalQuestions) { this.totalQuestions = totalQuestions; }
        public Integer getCompletedQuestions() { return completedQuestions; }
        public void setCompletedQuestions(Integer completedQuestions) { this.completedQuestions = completedQuestions; }
        public Integer getAverageScore() { return averageScore; }
        public void setAverageScore(Integer averageScore) { this.averageScore = averageScore; }
        public Integer getTotalDuration() { return totalDuration; }
        public void setTotalDuration(Integer totalDuration) { this.totalDuration = totalDuration; }
        public List<String> getStrongCategories() { return strongCategories; }
        public void setStrongCategories(List<String> strongCategories) { this.strongCategories = strongCategories; }
        public List<String> getWeakCategories() { return weakCategories; }
        public void setWeakCategories(List<String> weakCategories) { this.weakCategories = weakCategories; }
        public List<String> getImprovementAreas() { return improvementAreas; }
        public void setImprovementAreas(List<String> improvementAreas) { this.improvementAreas = improvementAreas; }
    }

    public static class CompanyResearch {
        private String companyName;
        private List<String> keyFacts;
        private List<String> recentNews;
        private List<String> interviewTips;
        private List<String> commonQuestions;
        private String culture;
        private String mission;
        private List<String> values;

        // Constructors
        public CompanyResearch() {}

        // Getters and setters
        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }
        public List<String> getKeyFacts() { return keyFacts; }
        public void setKeyFacts(List<String> keyFacts) { this.keyFacts = keyFacts; }
        public List<String> getRecentNews() { return recentNews; }
        public void setRecentNews(List<String> recentNews) { this.recentNews = recentNews; }
        public List<String> getInterviewTips() { return interviewTips; }
        public void setInterviewTips(List<String> interviewTips) { this.interviewTips = interviewTips; }
        public List<String> getCommonQuestions() { return commonQuestions; }
        public void setCommonQuestions(List<String> commonQuestions) { this.commonQuestions = commonQuestions; }
        public String getCulture() { return culture; }
        public void setCulture(String culture) { this.culture = culture; }
        public String getMission() { return mission; }
        public void setMission(String mission) { this.mission = mission; }
        public List<String> getValues() { return values; }
        public void setValues(List<String> values) { this.values = values; }
    }

    public static class SessionFeedback {
        private Integer overallScore;
        private List<String> strengths;
        private List<String> improvementAreas;
        private List<String> nextSteps;
        private List<String> recommendedResources;

        // Constructors
        public SessionFeedback() {}

        // Getters and setters
        public Integer getOverallScore() { return overallScore; }
        public void setOverallScore(Integer overallScore) { this.overallScore = overallScore; }
        public List<String> getStrengths() { return strengths; }
        public void setStrengths(List<String> strengths) { this.strengths = strengths; }
        public List<String> getImprovementAreas() { return improvementAreas; }
        public void setImprovementAreas(List<String> improvementAreas) { this.improvementAreas = improvementAreas; }
        public List<String> getNextSteps() { return nextSteps; }
        public void setNextSteps(List<String> nextSteps) { this.nextSteps = nextSteps; }
        public List<String> getRecommendedResources() { return recommendedResources; }
        public void setRecommendedResources(List<String> recommendedResources) { this.recommendedResources = recommendedResources; }
    }

    // Enums for questions
    public enum QuestionCategory {
        BEHAVIORAL("Behavioral"),
        TECHNICAL("Technical"),
        SITUATIONAL("Situational"),
        CASE_STUDY("Case Study"),
        COMPANY_SPECIFIC("Company Specific"),
        LEADERSHIP("Leadership"),
        PROBLEM_SOLVING("Problem Solving");

        private final String displayName;

        QuestionCategory(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    public enum QuestionDifficulty {
        EASY("Easy"),
        MEDIUM("Medium"),
        HARD("Hard");

        private final String displayName;

        QuestionDifficulty(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // Helper methods
    private String generateSessionId() {
        return "session_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    public void updateTimestamp() {
        this.updatedAt = LocalDateTime.now();
    }

    public void completeSession() {
        this.isCompleted = true;
        this.completedAt = LocalDateTime.now();
        updateTimestamp();
    }

    // Main getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    public String getSessionId() { return sessionId; }
    public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    public String getJobId() { return jobId; }
    public void setJobId(String jobId) { this.jobId = jobId; }
    public SessionType getType() { return type; }
    public void setType(SessionType type) { this.type = type; }
    public String getSessionName() { return sessionName; }
    public void setSessionName(String sessionName) { this.sessionName = sessionName; }
    public List<InterviewQuestion> getQuestions() { return questions; }
    public void setQuestions(List<InterviewQuestion> questions) { this.questions = questions; }
    public SessionStats getSessionStats() { return sessionStats; }
    public void setSessionStats(SessionStats sessionStats) { this.sessionStats = sessionStats; }
    public CompanyResearch getCompanyResearch() { return companyResearch; }
    public void setCompanyResearch(CompanyResearch companyResearch) { this.companyResearch = companyResearch; }
    public SessionFeedback getFeedback() { return feedback; }
    public void setFeedback(SessionFeedback feedback) { this.feedback = feedback; }
    public boolean isCompleted() { return isCompleted; }
    public void setCompleted(boolean completed) { isCompleted = completed; }
    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
