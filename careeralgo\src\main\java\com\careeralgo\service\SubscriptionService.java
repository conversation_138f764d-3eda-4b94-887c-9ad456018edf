package com.careeralgo.service;

import com.careeralgo.constant.SubscriptionPlan;
import com.careeralgo.exception.ResourceNotFoundException;
import com.careeralgo.model.User;
import com.careeralgo.model.UserSubscription;
import com.careeralgo.repository.UserRepository;
import com.careeralgo.repository.UserSubscriptionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Service for subscription management
 */
@Service
public class SubscriptionService {

    private static final Logger logger = LoggerFactory.getLogger(SubscriptionService.class);

    @Autowired
    private UserSubscriptionRepository subscriptionRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private NotificationService notificationService;

    /**
     * Get user's current subscription
     */
    public UserSubscription getUserSubscription(Authentication authentication) {
        String userId = getUserId(authentication);
        return subscriptionRepository.findByUserId(userId)
                .orElse(createFreeSubscription(userId));
    }

    /**
     * Get subscription by ID
     */
    public UserSubscription getSubscriptionById(String subscriptionId) {
        return subscriptionRepository.findById(subscriptionId)
                .orElseThrow(() -> new ResourceNotFoundException("Subscription not found: " + subscriptionId));
    }

    /**
     * Create or upgrade subscription
     */
    public UserSubscription createOrUpgradeSubscription(Authentication authentication, 
                                                       SubscriptionPlan plan, 
                                                       UserSubscription.BillingCycle billingCycle) {
        String userId = getUserId(authentication);
        
        Optional<UserSubscription> existingSubscription = subscriptionRepository.findByUserId(userId);
        
        if (existingSubscription.isPresent()) {
            // Upgrade existing subscription
            return upgradeSubscription(existingSubscription.get(), plan, billingCycle);
        } else {
            // Create new subscription
            return createNewSubscription(userId, plan, billingCycle);
        }
    }

    /**
     * Cancel subscription
     */
    public UserSubscription cancelSubscription(Authentication authentication, String reason, boolean immediate) {
        String userId = getUserId(authentication);
        UserSubscription subscription = subscriptionRepository.findByUserId(userId)
                .orElseThrow(() -> new ResourceNotFoundException("No active subscription found"));
        
        if (immediate) {
            subscription.setStatus(UserSubscription.SubscriptionStatus.CANCELED);
            subscription.setCanceledAt(LocalDateTime.now());
        } else {
            subscription.setCancelAtPeriodEnd(true);
        }
        
        subscription.setCancellationReason(reason);
        subscription.updateTimestamp();
        
        UserSubscription savedSubscription = subscriptionRepository.save(subscription);
        
        // Send cancellation notification
        String title = immediate ? "Subscription Canceled" : "Subscription Will Cancel at Period End";
        String message = immediate ? 
                "Your subscription has been canceled immediately." :
                "Your subscription will cancel at the end of the current billing period.";
        
        notificationService.createNotification(userId, 
                com.careeralgo.model.Notification.NotificationType.SYSTEM_ANNOUNCEMENT,
                title, message, null, null);
        
        logger.info("Subscription {} for user: {}", immediate ? "canceled" : "scheduled for cancellation", userId);
        return savedSubscription;
    }

    /**
     * Reactivate canceled subscription
     */
    public UserSubscription reactivateSubscription(Authentication authentication) {
        String userId = getUserId(authentication);
        UserSubscription subscription = subscriptionRepository.findByUserId(userId)
                .orElseThrow(() -> new ResourceNotFoundException("No subscription found"));
        
        if (subscription.getStatus() == UserSubscription.SubscriptionStatus.CANCELED && 
            !subscription.isPeriodExpired()) {
            
            subscription.setStatus(UserSubscription.SubscriptionStatus.ACTIVE);
            subscription.setCancelAtPeriodEnd(false);
            subscription.setCanceledAt(null);
            subscription.setCancellationReason(null);
            subscription.updateTimestamp();
            
            UserSubscription savedSubscription = subscriptionRepository.save(subscription);
            
            // Send reactivation notification
            notificationService.createNotification(userId,
                    com.careeralgo.model.Notification.NotificationType.SYSTEM_ANNOUNCEMENT,
                    "Subscription Reactivated",
                    "Your subscription has been successfully reactivated.",
                    null, null);
            
            logger.info("Reactivated subscription for user: {}", userId);
            return savedSubscription;
        } else {
            throw new IllegalStateException("Subscription cannot be reactivated");
        }
    }

    /**
     * Check if user can use feature
     */
    public boolean canUseFeature(Authentication authentication, String feature) {
        String userId = getUserId(authentication);
        UserSubscription subscription = subscriptionRepository.findByUserId(userId)
                .orElse(createFreeSubscription(userId));
        
        return subscription.canUseFeature(feature);
    }

    /**
     * Check if user has usage remaining
     */
    public boolean hasUsageRemaining(Authentication authentication, String usageType) {
        String userId = getUserId(authentication);
        UserSubscription subscription = subscriptionRepository.findByUserId(userId)
                .orElse(createFreeSubscription(userId));
        
        return subscription.hasUsageRemaining(usageType);
    }

    /**
     * Increment usage counter
     */
    public void incrementUsage(Authentication authentication, String usageType) {
        String userId = getUserId(authentication);
        UserSubscription subscription = subscriptionRepository.findByUserId(userId)
                .orElse(createFreeSubscription(userId));
        
        if (subscription.hasUsageRemaining(usageType)) {
            subscription.incrementUsage(usageType);
            subscriptionRepository.save(subscription);
            
            // Check if approaching limits and send notification
            checkUsageLimitsAndNotify(subscription);
        } else {
            throw new IllegalStateException("Usage limit exceeded for: " + usageType);
        }
    }

    /**
     * Get subscription usage summary
     */
    public SubscriptionUsageSummary getUsageSummary(Authentication authentication) {
        String userId = getUserId(authentication);
        UserSubscription subscription = subscriptionRepository.findByUserId(userId)
                .orElse(createFreeSubscription(userId));
        
        SubscriptionUsageSummary summary = new SubscriptionUsageSummary();
        summary.setUserId(userId);
        summary.setPlan(subscription.getPlan());
        summary.setStatus(subscription.getStatus());
        summary.setCurrentPeriodEnd(subscription.getCurrentPeriodEnd());
        
        UserSubscription.UsageLimits limits = subscription.getUsageLimits();
        UserSubscription.UsageTracking tracking = subscription.getUsageTracking();
        
        summary.setApplicationsUsed(tracking.getCurrentMonthApplications());
        summary.setApplicationsLimit(limits.getMonthlyApplications());
        summary.setResumeVersionsUsed(tracking.getCurrentMonthResumeVersions());
        summary.setResumeVersionsLimit(limits.getResumeVersions());
        summary.setInterviewSessionsUsed(tracking.getCurrentMonthInterviewSessions());
        summary.setInterviewSessionsLimit(limits.getInterviewSessions());
        summary.setAiAnalysesUsed(tracking.getCurrentMonthAiAnalyses());
        summary.setAiAnalysesLimit(limits.getAiAnalyses());
        
        return summary;
    }

    /**
     * Get available subscription plans
     */
    public List<SubscriptionPlanInfo> getAvailablePlans() {
        List<SubscriptionPlanInfo> plans = new ArrayList<>();
        
        for (SubscriptionPlan plan : SubscriptionPlan.values()) {
            SubscriptionPlanInfo planInfo = new SubscriptionPlanInfo();
            planInfo.setPlan(plan);
            planInfo.setName(plan.name());
            planInfo.setMonthlyPrice(getPlanPrice(plan, UserSubscription.BillingCycle.MONTHLY));
            planInfo.setYearlyPrice(getPlanPrice(plan, UserSubscription.BillingCycle.YEARLY));
            planInfo.setFeatures(getPlanFeatures(plan));
            planInfo.setUsageLimits(getPlanUsageLimits(plan));
            
            plans.add(planInfo);
        }
        
        return plans;
    }

    /**
     * Get subscription analytics (admin)
     */
    public SubscriptionAnalytics getSubscriptionAnalytics() {
        SubscriptionAnalytics analytics = new SubscriptionAnalytics();
        analytics.setGeneratedAt(LocalDateTime.now());
        
        // Count by plan
        Map<String, Long> subscriptionsByPlan = new HashMap<>();
        for (SubscriptionPlan plan : SubscriptionPlan.values()) {
            long count = subscriptionRepository.countByPlan(plan);
            subscriptionsByPlan.put(plan.name(), count);
        }
        analytics.setSubscriptionsByPlan(subscriptionsByPlan);
        
        // Count by status
        Map<String, Long> subscriptionsByStatus = new HashMap<>();
        for (UserSubscription.SubscriptionStatus status : UserSubscription.SubscriptionStatus.values()) {
            long count = subscriptionRepository.countByStatus(status);
            subscriptionsByStatus.put(status.name(), count);
        }
        analytics.setSubscriptionsByStatus(subscriptionsByStatus);
        
        // Revenue metrics
        analytics.setTotalActiveSubscriptions(subscriptionRepository.countActiveSubscriptions());
        analytics.setMonthlyRecurringRevenue(calculateMonthlyRecurringRevenue());
        analytics.setAnnualRecurringRevenue(analytics.getMonthlyRecurringRevenue().multiply(new BigDecimal("12")));
        
        return analytics;
    }

    /**
     * Process subscription renewals (scheduled task)
     */
    @Scheduled(fixedRate = 3600000) // Run every hour
    public void processSubscriptionRenewals() {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime tomorrow = now.plusDays(1);
            
            // Find subscriptions expiring soon
            List<UserSubscription> expiringSubscriptions = subscriptionRepository.findSubscriptionsExpiringBefore(tomorrow);
            
            for (UserSubscription subscription : expiringSubscriptions) {
                processSubscriptionRenewal(subscription);
            }
            
            // Find expired trials
            List<UserSubscription> expiredTrials = subscriptionRepository.findExpiredTrials(now);
            
            for (UserSubscription trial : expiredTrials) {
                processTrialExpiration(trial);
            }
            
            logger.info("Processed {} subscription renewals and {} trial expirations", 
                    expiringSubscriptions.size(), expiredTrials.size());
            
        } catch (Exception e) {
            logger.error("Error processing subscription renewals", e);
        }
    }

    /**
     * Reset monthly usage counters (scheduled task)
     */
    @Scheduled(cron = "0 0 0 1 * ?") // Run on the 1st day of every month at midnight
    public void resetMonthlyUsage() {
        try {
            LocalDateTime firstOfMonth = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
            List<UserSubscription> subscriptions = subscriptionRepository.findSubscriptionsNeedingUsageReset(firstOfMonth);
            
            for (UserSubscription subscription : subscriptions) {
                subscription.getUsageTracking().resetMonthlyUsage();
                subscriptionRepository.save(subscription);
            }
            
            logger.info("Reset monthly usage for {} subscriptions", subscriptions.size());
            
        } catch (Exception e) {
            logger.error("Error resetting monthly usage", e);
        }
    }

    // Private helper methods
    
    private String getUserId(Authentication authentication) {
        if (authentication.getPrincipal() instanceof Jwt jwt) {
            String clerkUserId = jwt.getSubject();
            User user = userRepository.findByClerkUserId(clerkUserId)
                    .orElseThrow(() -> new ResourceNotFoundException("User not found"));
            return user.getId();
        }
        throw new IllegalArgumentException("Invalid authentication type");
    }

    private UserSubscription createFreeSubscription(String userId) {
        UserSubscription subscription = new UserSubscription(userId, SubscriptionPlan.FREE);
        subscription.setStatus(UserSubscription.SubscriptionStatus.ACTIVE);
        subscription.setCurrentPeriodStart(LocalDateTime.now());
        subscription.setCurrentPeriodEnd(LocalDateTime.now().plusYears(10)); // Free plan doesn't expire
        subscription.setAmount(BigDecimal.ZERO);
        subscription.setBillingCycle(UserSubscription.BillingCycle.MONTHLY);
        
        return subscriptionRepository.save(subscription);
    }

    private UserSubscription createNewSubscription(String userId, SubscriptionPlan plan, 
                                                 UserSubscription.BillingCycle billingCycle) {
        UserSubscription subscription = new UserSubscription(userId, plan);
        subscription.setStatus(UserSubscription.SubscriptionStatus.PENDING);
        subscription.setBillingCycle(billingCycle);
        subscription.setAmount(getPlanPrice(plan, billingCycle));
        subscription.setCurrentPeriodStart(LocalDateTime.now());
        subscription.setCurrentPeriodEnd(calculatePeriodEnd(billingCycle));
        
        // Start trial if applicable
        if (plan != SubscriptionPlan.FREE) {
            subscription.setTrialStart(LocalDateTime.now());
            subscription.setTrialEnd(LocalDateTime.now().plusDays(14)); // 14-day trial
            subscription.setTrialActive(true);
            subscription.setStatus(UserSubscription.SubscriptionStatus.TRIALING);
        }
        
        return subscriptionRepository.save(subscription);
    }

    private UserSubscription upgradeSubscription(UserSubscription subscription, SubscriptionPlan newPlan, 
                                                UserSubscription.BillingCycle billingCycle) {
        subscription.setPlan(newPlan);
        subscription.setBillingCycle(billingCycle);
        subscription.setAmount(getPlanPrice(newPlan, billingCycle));
        subscription.setUsageLimits(getPlanUsageLimits(newPlan));
        subscription.updateTimestamp();
        
        return subscriptionRepository.save(subscription);
    }

    private BigDecimal getPlanPrice(SubscriptionPlan plan, UserSubscription.BillingCycle billingCycle) {
        BigDecimal basePrice = switch (plan) {
            case FREE -> BigDecimal.ZERO;
            case BASIC -> new BigDecimal("9.99");
            case PRO -> new BigDecimal("19.99");
            case ENTERPRISE -> new BigDecimal("49.99");
        };
        
        // Apply yearly discount (20% off)
        if (billingCycle == UserSubscription.BillingCycle.YEARLY) {
            return basePrice.multiply(new BigDecimal("12")).multiply(new BigDecimal("0.8"));
        }
        
        return basePrice;
    }

    private List<String> getPlanFeatures(SubscriptionPlan plan) {
        return switch (plan) {
            case FREE -> List.of("Basic job search", "Resume upload", "5 applications per month");
            case BASIC -> List.of("Everything in Free", "50 applications per month", "AI resume analysis", "Email notifications");
            case PRO -> List.of("Everything in Basic", "Unlimited applications", "LinkedIn integration", "Advanced interview prep");
            case ENTERPRISE -> List.of("Everything in Pro", "Team management", "Custom integrations", "Priority support");
        };
    }

    private UserSubscription.UsageLimits getPlanUsageLimits(SubscriptionPlan plan) {
        return switch (plan) {
            case FREE -> new UserSubscription.UsageLimits(5, 1, 0, 0, false, false, false, false);
            case BASIC -> new UserSubscription.UsageLimits(50, 3, 10, 5, true, false, false, false);
            case PRO -> new UserSubscription.UsageLimits(-1, -1, -1, -1, true, true, true, true);
            case ENTERPRISE -> new UserSubscription.UsageLimits(-1, -1, -1, -1, true, true, true, true);
        };
    }

    private LocalDateTime calculatePeriodEnd(UserSubscription.BillingCycle billingCycle) {
        LocalDateTime now = LocalDateTime.now();
        return billingCycle == UserSubscription.BillingCycle.YEARLY ? 
                now.plusYears(1) : now.plusMonths(1);
    }

    private void checkUsageLimitsAndNotify(UserSubscription subscription) {
        UserSubscription.UsageLimits limits = subscription.getUsageLimits();
        UserSubscription.UsageTracking tracking = subscription.getUsageTracking();
        
        // Check if user is approaching limits (80% threshold)
        if (limits.getMonthlyApplications() != -1) {
            double usagePercentage = (double) tracking.getCurrentMonthApplications() / limits.getMonthlyApplications();
            if (usagePercentage >= 0.8) {
                notificationService.createNotification(subscription.getUserId(),
                        com.careeralgo.model.Notification.NotificationType.SYSTEM_ANNOUNCEMENT,
                        "Approaching Usage Limit",
                        "You've used " + Math.round(usagePercentage * 100) + "% of your monthly application limit.",
                        null, null);
            }
        }
    }

    private void processSubscriptionRenewal(UserSubscription subscription) {
        // TODO: Implement actual payment processing with Stripe
        logger.info("Processing renewal for subscription: {}", subscription.getId());
    }

    private void processTrialExpiration(UserSubscription subscription) {
        subscription.setTrialActive(false);
        subscription.setStatus(UserSubscription.SubscriptionStatus.ACTIVE);
        subscriptionRepository.save(subscription);
        
        notificationService.createNotification(subscription.getUserId(),
                com.careeralgo.model.Notification.NotificationType.SYSTEM_ANNOUNCEMENT,
                "Trial Expired",
                "Your free trial has expired. Your subscription is now active.",
                null, null);
    }

    private BigDecimal calculateMonthlyRecurringRevenue() {
        // TODO: Implement actual MRR calculation
        return new BigDecimal("10000.00");
    }

    // DTOs
    
    public static class SubscriptionUsageSummary {
        private String userId;
        private SubscriptionPlan plan;
        private UserSubscription.SubscriptionStatus status;
        private LocalDateTime currentPeriodEnd;
        private Integer applicationsUsed;
        private Integer applicationsLimit;
        private Integer resumeVersionsUsed;
        private Integer resumeVersionsLimit;
        private Integer interviewSessionsUsed;
        private Integer interviewSessionsLimit;
        private Integer aiAnalysesUsed;
        private Integer aiAnalysesLimit;

        // Getters and setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public SubscriptionPlan getPlan() { return plan; }
        public void setPlan(SubscriptionPlan plan) { this.plan = plan; }
        public UserSubscription.SubscriptionStatus getStatus() { return status; }
        public void setStatus(UserSubscription.SubscriptionStatus status) { this.status = status; }
        public LocalDateTime getCurrentPeriodEnd() { return currentPeriodEnd; }
        public void setCurrentPeriodEnd(LocalDateTime currentPeriodEnd) { this.currentPeriodEnd = currentPeriodEnd; }
        public Integer getApplicationsUsed() { return applicationsUsed; }
        public void setApplicationsUsed(Integer applicationsUsed) { this.applicationsUsed = applicationsUsed; }
        public Integer getApplicationsLimit() { return applicationsLimit; }
        public void setApplicationsLimit(Integer applicationsLimit) { this.applicationsLimit = applicationsLimit; }
        public Integer getResumeVersionsUsed() { return resumeVersionsUsed; }
        public void setResumeVersionsUsed(Integer resumeVersionsUsed) { this.resumeVersionsUsed = resumeVersionsUsed; }
        public Integer getResumeVersionsLimit() { return resumeVersionsLimit; }
        public void setResumeVersionsLimit(Integer resumeVersionsLimit) { this.resumeVersionsLimit = resumeVersionsLimit; }
        public Integer getInterviewSessionsUsed() { return interviewSessionsUsed; }
        public void setInterviewSessionsUsed(Integer interviewSessionsUsed) { this.interviewSessionsUsed = interviewSessionsUsed; }
        public Integer getInterviewSessionsLimit() { return interviewSessionsLimit; }
        public void setInterviewSessionsLimit(Integer interviewSessionsLimit) { this.interviewSessionsLimit = interviewSessionsLimit; }
        public Integer getAiAnalysesUsed() { return aiAnalysesUsed; }
        public void setAiAnalysesUsed(Integer aiAnalysesUsed) { this.aiAnalysesUsed = aiAnalysesUsed; }
        public Integer getAiAnalysesLimit() { return aiAnalysesLimit; }
        public void setAiAnalysesLimit(Integer aiAnalysesLimit) { this.aiAnalysesLimit = aiAnalysesLimit; }
    }

    public static class SubscriptionPlanInfo {
        private SubscriptionPlan plan;
        private String name;
        private BigDecimal monthlyPrice;
        private BigDecimal yearlyPrice;
        private List<String> features;
        private UserSubscription.UsageLimits usageLimits;

        // Getters and setters
        public SubscriptionPlan getPlan() { return plan; }
        public void setPlan(SubscriptionPlan plan) { this.plan = plan; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public BigDecimal getMonthlyPrice() { return monthlyPrice; }
        public void setMonthlyPrice(BigDecimal monthlyPrice) { this.monthlyPrice = monthlyPrice; }
        public BigDecimal getYearlyPrice() { return yearlyPrice; }
        public void setYearlyPrice(BigDecimal yearlyPrice) { this.yearlyPrice = yearlyPrice; }
        public List<String> getFeatures() { return features; }
        public void setFeatures(List<String> features) { this.features = features; }
        public UserSubscription.UsageLimits getUsageLimits() { return usageLimits; }
        public void setUsageLimits(UserSubscription.UsageLimits usageLimits) { this.usageLimits = usageLimits; }
    }

    public static class SubscriptionAnalytics {
        private LocalDateTime generatedAt;
        private Map<String, Long> subscriptionsByPlan;
        private Map<String, Long> subscriptionsByStatus;
        private Long totalActiveSubscriptions;
        private BigDecimal monthlyRecurringRevenue;
        private BigDecimal annualRecurringRevenue;

        // Getters and setters
        public LocalDateTime getGeneratedAt() { return generatedAt; }
        public void setGeneratedAt(LocalDateTime generatedAt) { this.generatedAt = generatedAt; }
        public Map<String, Long> getSubscriptionsByPlan() { return subscriptionsByPlan; }
        public void setSubscriptionsByPlan(Map<String, Long> subscriptionsByPlan) { this.subscriptionsByPlan = subscriptionsByPlan; }
        public Map<String, Long> getSubscriptionsByStatus() { return subscriptionsByStatus; }
        public void setSubscriptionsByStatus(Map<String, Long> subscriptionsByStatus) { this.subscriptionsByStatus = subscriptionsByStatus; }
        public Long getTotalActiveSubscriptions() { return totalActiveSubscriptions; }
        public void setTotalActiveSubscriptions(Long totalActiveSubscriptions) { this.totalActiveSubscriptions = totalActiveSubscriptions; }
        public BigDecimal getMonthlyRecurringRevenue() { return monthlyRecurringRevenue; }
        public void setMonthlyRecurringRevenue(BigDecimal monthlyRecurringRevenue) { this.monthlyRecurringRevenue = monthlyRecurringRevenue; }
        public BigDecimal getAnnualRecurringRevenue() { return annualRecurringRevenue; }
        public void setAnnualRecurringRevenue(BigDecimal annualRecurringRevenue) { this.annualRecurringRevenue = annualRecurringRevenue; }
    }
}
