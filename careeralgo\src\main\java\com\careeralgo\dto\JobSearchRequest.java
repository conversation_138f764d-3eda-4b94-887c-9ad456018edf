package com.careeralgo.dto;

import com.careeralgo.constant.ExperienceLevel;
import com.careeralgo.model.Job;

import java.util.List;

/**
 * DTO for job search request parameters
 */
public class JobSearchRequest {

    private String keyword;
    private String location;
    private ExperienceLevel experienceLevel;
    private Job.EmploymentType employmentType;
    private Boolean isRemote;
    private Boolean isHybrid;
    private List<String> skills;
    private String company;
    private String industry;
    private Integer minSalary;
    private Integer maxSalary;
    private String salaryPeriod;
    private List<String> tags;
    private Integer postedWithinDays;

    // Constructors
    public JobSearchRequest() {}

    // Getters and Setters
    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public ExperienceLevel getExperienceLevel() {
        return experienceLevel;
    }

    public void setExperienceLevel(ExperienceLevel experienceLevel) {
        this.experienceLevel = experienceLevel;
    }

    public Job.EmploymentType getEmploymentType() {
        return employmentType;
    }

    public void setEmploymentType(Job.EmploymentType employmentType) {
        this.employmentType = employmentType;
    }

    public Boolean getIsRemote() {
        return isRemote;
    }

    public void setIsRemote(Boolean isRemote) {
        this.isRemote = isRemote;
    }

    public Boolean getIsHybrid() {
        return isHybrid;
    }

    public void setIsHybrid(Boolean isHybrid) {
        this.isHybrid = isHybrid;
    }

    public List<String> getSkills() {
        return skills;
    }

    public void setSkills(List<String> skills) {
        this.skills = skills;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public Integer getMinSalary() {
        return minSalary;
    }

    public void setMinSalary(Integer minSalary) {
        this.minSalary = minSalary;
    }

    public Integer getMaxSalary() {
        return maxSalary;
    }

    public void setMaxSalary(Integer maxSalary) {
        this.maxSalary = maxSalary;
    }

    public String getSalaryPeriod() {
        return salaryPeriod;
    }

    public void setSalaryPeriod(String salaryPeriod) {
        this.salaryPeriod = salaryPeriod;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Integer getPostedWithinDays() {
        return postedWithinDays;
    }

    public void setPostedWithinDays(Integer postedWithinDays) {
        this.postedWithinDays = postedWithinDays;
    }

    // Helper methods
    public boolean hasAdvancedFilters() {
        return experienceLevel != null || 
               employmentType != null || 
               isRemote != null || 
               isHybrid != null || 
               (skills != null && !skills.isEmpty()) ||
               company != null || 
               industry != null || 
               minSalary != null || 
               maxSalary != null ||
               (tags != null && !tags.isEmpty()) ||
               postedWithinDays != null;
    }

    public boolean hasKeywordSearch() {
        return keyword != null && !keyword.trim().isEmpty();
    }

    public boolean hasLocationFilter() {
        return location != null && !location.trim().isEmpty();
    }

    public boolean hasSalaryFilter() {
        return minSalary != null || maxSalary != null;
    }

    public boolean hasSkillsFilter() {
        return skills != null && !skills.isEmpty();
    }
}
