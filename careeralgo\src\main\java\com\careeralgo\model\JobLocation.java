package com.careeralgo.model;

/**
 * Job location information with remote work options
 */
public class JobLocation {
    
    private String city;
    private String state;
    private String country;
    private boolean isRemote = false;
    private boolean isHybrid = false;
    private String remotePolicy;

    // Constructors
    public JobLocation() {}

    public JobLocation(String city, String state, String country) {
        this.city = city;
        this.state = state;
        this.country = country;
    }

    public JobLocation(boolean isRemote) {
        this.isRemote = isRemote;
        if (isRemote) {
            this.city = "Remote";
            this.country = "Worldwide";
        }
    }

    // Getters and Setters
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public boolean isRemote() {
        return isRemote;
    }

    public void setRemote(boolean remote) {
        isRemote = remote;
    }

    public boolean isHybrid() {
        return isHybrid;
    }

    public void setHybrid(boolean hybrid) {
        isHybrid = hybrid;
    }

    public String getRemotePolicy() {
        return remotePolicy;
    }

    public void setRemotePolicy(String remotePolicy) {
        this.remotePolicy = remotePolicy;
    }

    public String getDisplayLocation() {
        if (isRemote && !isHybrid) {
            return "Remote";
        }
        
        if (isHybrid) {
            String baseLocation = getFullLocation();
            return baseLocation + " (Hybrid)";
        }
        
        return getFullLocation();
    }

    public String getFullLocation() {
        StringBuilder sb = new StringBuilder();
        if (city != null && !city.isEmpty()) {
            sb.append(city);
        }
        if (state != null && !state.isEmpty()) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(state);
        }
        if (country != null && !country.isEmpty()) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(country);
        }
        return sb.toString();
    }

    public String getWorkArrangement() {
        if (isRemote && !isHybrid) return "Remote";
        if (isHybrid) return "Hybrid";
        return "On-site";
    }

    @Override
    public String toString() {
        return getDisplayLocation();
    }
}
