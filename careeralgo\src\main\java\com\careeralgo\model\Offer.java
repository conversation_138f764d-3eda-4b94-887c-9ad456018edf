package com.careeralgo.model;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Job offer information
 */
public class Offer {
    
    private Integer salary;
    private String currency = "USD";
    private String equity;
    private Integer bonus;
    private LocalDate startDate;
    private List<String> benefits;
    private LocalDateTime receivedDate;
    private LocalDateTime deadline;
    private OfferStatus status = OfferStatus.PENDING;

    public enum OfferStatus {
        PENDING, ACCEPTED, DECLINED, NEGOTIATING
    }

    // Constructors
    public Offer() {}

    public Offer(Integer salary, String currency, LocalDateTime deadline) {
        this.salary = salary;
        this.currency = currency;
        this.deadline = deadline;
        this.receivedDate = LocalDateTime.now();
    }

    // Getters and Setters
    public Integer getSalary() {
        return salary;
    }

    public void setSalary(Integer salary) {
        this.salary = salary;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getEquity() {
        return equity;
    }

    public void setEquity(String equity) {
        this.equity = equity;
    }

    public Integer getBonus() {
        return bonus;
    }

    public void setBonus(Integer bonus) {
        this.bonus = bonus;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public List<String> getBenefits() {
        return benefits;
    }

    public void setBenefits(List<String> benefits) {
        this.benefits = benefits;
    }

    public LocalDateTime getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(LocalDateTime receivedDate) {
        this.receivedDate = receivedDate;
    }

    public LocalDateTime getDeadline() {
        return deadline;
    }

    public void setDeadline(LocalDateTime deadline) {
        this.deadline = deadline;
    }

    public OfferStatus getStatus() {
        return status;
    }

    public void setStatus(OfferStatus status) {
        this.status = status;
    }

    public String getFormattedSalary() {
        if (salary == null) return "Not specified";
        return String.format("%,d %s", salary, currency);
    }

    public String getTotalCompensation() {
        StringBuilder sb = new StringBuilder(getFormattedSalary());
        
        if (bonus != null && bonus > 0) {
            sb.append(" + ").append(String.format("%,d", bonus)).append(" bonus");
        }
        
        if (equity != null && !equity.isEmpty()) {
            sb.append(" + ").append(equity).append(" equity");
        }
        
        return sb.toString();
    }

    public boolean isExpired() {
        return deadline != null && deadline.isBefore(LocalDateTime.now());
    }

    public boolean isPending() {
        return status == OfferStatus.PENDING && !isExpired();
    }

    public long getDaysUntilDeadline() {
        if (deadline == null) return -1;
        return java.time.Duration.between(LocalDateTime.now(), deadline).toDays();
    }
}
