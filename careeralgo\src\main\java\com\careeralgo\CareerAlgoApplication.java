package com.careeralgo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableCaching
@EnableMongoAuditing
@EnableAsync
@EnableScheduling
public class CareerAlgoApplication {

	public static void main(String[] args) {
		SpringApplication.run(CareerAlgoApplication.class, args);
	}

}
