package com.careeralgo.security;

import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Converts Clerk JWT to Spring Security Authentication
 */
public class ClerkJwtAuthenticationConverter implements Converter<Jwt, AbstractAuthenticationToken> {

    @Override
    public AbstractAuthenticationToken convert(Jwt jwt) {
        Collection<GrantedAuthority> authorities = extractAuthorities(jwt);
        return new JwtAuthenticationToken(jwt, authorities);
    }

    private Collection<GrantedAuthority> extractAuthorities(Jwt jwt) {
        // Extract roles from Clerk JWT claims
        Object rolesClaim = jwt.getClaim("roles");
        
        if (rolesClaim instanceof List<?>) {
            @SuppressWarnings("unchecked")
            List<String> roles = (List<String>) rolesClaim;
            return roles.stream()
                    .map(role -> new SimpleGrantedAuthority("ROLE_" + role.toUpperCase()))
                    .collect(Collectors.toList());
        }
        
        // Extract single role from metadata
        Object metadataClaim = jwt.getClaim("metadata");
        if (metadataClaim instanceof java.util.Map<?, ?>) {
            @SuppressWarnings("unchecked")
            java.util.Map<String, Object> metadata = (java.util.Map<String, Object>) metadataClaim;
            Object role = metadata.get("role");
            if (role instanceof String) {
                return List.of(new SimpleGrantedAuthority("ROLE_" + ((String) role).toUpperCase()));
            }
        }
        
        // Default role
        return Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"));
    }
}
