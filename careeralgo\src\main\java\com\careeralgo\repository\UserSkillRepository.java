package com.careeralgo.repository;

import com.careeralgo.model.UserSkill;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for UserSkill entities
 */
@Repository
public interface UserSkillRepository extends MongoRepository<UserSkill, String> {

    /**
     * Find all skills for a specific user
     */
    List<UserSkill> findByUserId(String userId);

    /**
     * Find all skills for a specific user ordered by proficiency level descending
     */
    List<UserSkill> findByUserIdOrderByProficiencyLevelDesc(String userId);

    /**
     * Find all skills for a specific user ordered by years of experience descending
     */
    List<UserSkill> findByUserIdOrderByYearsOfExperienceDesc(String userId);

    /**
     * Find a specific user skill by user ID and skill ID
     */
    Optional<UserSkill> findByUserIdAndSkillId(String userId, String skillId);

    /**
     * Find all users who have a specific skill
     */
    List<UserSkill> findBySkillId(String skillId);

    /**
     * Find user skills by proficiency level
     */
    List<UserSkill> findByUserIdAndProficiencyLevel(String userId, UserSkill.ProficiencyLevel proficiencyLevel);

    /**
     * Find user skills by source
     */
    List<UserSkill> findByUserIdAndSource(String userId, UserSkill.SkillSource source);

    /**
     * Find verified user skills
     */
    List<UserSkill> findByUserIdAndVerifiedTrue(String userId);

    /**
     * Find unverified user skills
     */
    List<UserSkill> findByUserIdAndVerifiedFalse(String userId);

    /**
     * Find user skills with endorsements above threshold
     */
    @Query("{'userId': ?0, 'endorsements': {'$gte': ?1}}")
    List<UserSkill> findByUserIdAndEndorsementsGreaterThanEqual(String userId, Integer minEndorsements);

    /**
     * Find user skills by years of experience range
     */
    @Query("{'userId': ?0, 'yearsOfExperience': {'$gte': ?1, '$lte': ?2}}")
    List<UserSkill> findByUserIdAndYearsOfExperienceBetween(String userId, Double minYears, Double maxYears);

    /**
     * Find recently used skills (used within specified date)
     */
    @Query("{'userId': ?0, 'lastUsed': {'$gte': ?1}}")
    List<UserSkill> findByUserIdAndLastUsedAfter(String userId, LocalDate since);

    /**
     * Find user skills by multiple skill IDs
     */
    List<UserSkill> findByUserIdAndSkillIdIn(String userId, List<String> skillIds);

    /**
     * Find user skills by proficiency level range
     */
    @Query("{'userId': ?0, 'proficiencyLevel': {'$in': ?1}}")
    List<UserSkill> findByUserIdAndProficiencyLevelIn(String userId, List<UserSkill.ProficiencyLevel> proficiencyLevels);

    /**
     * Count user skills by proficiency level
     */
    long countByUserIdAndProficiencyLevel(String userId, UserSkill.ProficiencyLevel proficiencyLevel);

    /**
     * Count verified skills for a user
     */
    long countByUserIdAndVerifiedTrue(String userId);

    /**
     * Count total skills for a user
     */
    long countByUserId(String userId);

    /**
     * Find user skills created after a certain date
     */
    @Query("{'userId': ?0, 'createdAt': {'$gte': ?1}}")
    List<UserSkill> findByUserIdAndCreatedAtAfter(String userId, LocalDateTime since);

    /**
     * Find user skills updated after a certain date
     */
    @Query("{'userId': ?0, 'updatedAt': {'$gte': ?1}}")
    List<UserSkill> findByUserIdAndUpdatedAtAfter(String userId, LocalDateTime since);

    /**
     * Find user skills with notes
     */
    @Query("{'userId': ?0, 'notes': {'$exists': true, '$ne': null, '$ne': ''}}")
    List<UserSkill> findByUserIdWithNotes(String userId);

    /**
     * Find user skills without notes
     */
    @Query("{'userId': ?0, '$or': [{'notes': {'$exists': false}}, {'notes': null}, {'notes': ''}]}")
    List<UserSkill> findByUserIdWithoutNotes(String userId);

    /**
     * Find top skills by endorsements for a user
     */
    @Query(value = "{'userId': ?0}", sort = "{'endorsements': -1}")
    List<UserSkill> findTopSkillsByEndorsements(String userId, Pageable pageable);

    /**
     * Find user skills by source type
     */
    List<UserSkill> findByUserIdAndSourceIn(String userId, List<UserSkill.SkillSource> sources);

    /**
     * Find all users with a specific skill and minimum proficiency
     */
    @Query("{'skillId': ?0, 'proficiencyLevel': {'$gte': ?1}}")
    List<UserSkill> findBySkillIdAndMinimumProficiency(String skillId, UserSkill.ProficiencyLevel minProficiency);

    /**
     * Find user skills that need verification (unverified and high endorsements)
     */
    @Query("{'userId': ?0, 'verified': false, 'endorsements': {'$gte': ?1}}")
    List<UserSkill> findUnverifiedSkillsWithHighEndorsements(String userId, Integer minEndorsements);

    /**
     * Find stale skills (not used recently)
     */
    @Query("{'userId': ?0, '$or': [{'lastUsed': {'$lt': ?1}}, {'lastUsed': null}]}")
    List<UserSkill> findStaleSkills(String userId, LocalDate cutoffDate);

    /**
     * Find user skills by multiple criteria
     */
    @Query("{'userId': ?0, 'proficiencyLevel': {'$in': ?1}, 'verified': ?2, 'yearsOfExperience': {'$gte': ?3}}")
    List<UserSkill> findByMultipleCriteria(String userId, List<UserSkill.ProficiencyLevel> proficiencyLevels, 
                                          Boolean verified, Double minYearsExperience);

    /**
     * Find user skills excluding certain skill IDs
     */
    @Query("{'userId': ?0, 'skillId': {'$nin': ?1}}")
    List<UserSkill> findByUserIdAndSkillIdNotIn(String userId, List<String> excludeSkillIds);

    /**
     * Find user skills with experience above threshold
     */
    @Query("{'userId': ?0, 'yearsOfExperience': {'$gte': ?1}}")
    List<UserSkill> findByUserIdAndMinimumExperience(String userId, Double minYears);

    /**
     * Find user skills by creation date range
     */
    @Query("{'userId': ?0, 'createdAt': {'$gte': ?1, '$lte': ?2}}")
    List<UserSkill> findByUserIdAndCreatedAtBetween(String userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find paginated user skills
     */
    Page<UserSkill> findByUserId(String userId, Pageable pageable);

    /**
     * Find paginated user skills by proficiency level
     */
    Page<UserSkill> findByUserIdAndProficiencyLevel(String userId, UserSkill.ProficiencyLevel proficiencyLevel, Pageable pageable);

    /**
     * Check if user has a specific skill
     */
    boolean existsByUserIdAndSkillId(String userId, String skillId);

    /**
     * Delete all skills for a user
     */
    void deleteByUserId(String userId);

    /**
     * Delete user skill by user ID and skill ID
     */
    void deleteByUserIdAndSkillId(String userId, String skillId);

    /**
     * Find user skills with high proficiency (Advanced or Expert)
     */
    @Query("{'userId': ?0, 'proficiencyLevel': {'$in': ['ADVANCED', 'EXPERT']}}")
    List<UserSkill> findHighProficiencySkills(String userId);

    /**
     * Find user skills with low proficiency (Beginner or Intermediate)
     */
    @Query("{'userId': ?0, 'proficiencyLevel': {'$in': ['BEGINNER', 'INTERMEDIATE']}}")
    List<UserSkill> findLowProficiencySkills(String userId);

    /**
     * Find recently added skills (within last N days)
     */
    @Query("{'userId': ?0, 'createdAt': {'$gte': ?1}}")
    List<UserSkill> findRecentlyAddedSkills(String userId, LocalDateTime since);

    /**
     * Find skills that need attention (low proficiency, not used recently)
     */
    @Query("{'userId': ?0, 'proficiencyLevel': {'$in': ['BEGINNER', 'INTERMEDIATE']}, " +
           "'$or': [{'lastUsed': {'$lt': ?1}}, {'lastUsed': null}]}")
    List<UserSkill> findSkillsNeedingAttention(String userId, LocalDate cutoffDate);

    /**
     * Find user's strongest skills (high proficiency and recent usage)
     */
    @Query("{'userId': ?0, 'proficiencyLevel': {'$in': ['ADVANCED', 'EXPERT']}, 'lastUsed': {'$gte': ?1}}")
    List<UserSkill> findStrongestSkills(String userId, LocalDate recentUsageThreshold);
}
