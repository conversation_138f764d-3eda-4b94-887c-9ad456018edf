package com.careeralgo.dto;

import com.careeralgo.constant.ApplicationStatus;
import com.careeralgo.model.Application;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * DTO for updating job application
 */
public class ApplicationUpdateRequest {

    private ApplicationStatus status;

    @Size(max = 5000, message = "Cover letter must not exceed 5000 characters")
    private String coverLetter;

    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    private String notes;

    private Application.Priority priority;

    private LocalDateTime followUpReminder;

    @Size(max = 500, message = "Status notes must not exceed 500 characters")
    private String statusNotes;

    // Constructors
    public ApplicationUpdateRequest() {}

    // Getters and Setters
    public ApplicationStatus getStatus() {
        return status;
    }

    public void setStatus(ApplicationStatus status) {
        this.status = status;
    }

    public String getCoverLetter() {
        return coverLetter;
    }

    public void setCoverLetter(String coverLetter) {
        this.coverLetter = coverLetter;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Application.Priority getPriority() {
        return priority;
    }

    public void setPriority(Application.Priority priority) {
        this.priority = priority;
    }

    public LocalDateTime getFollowUpReminder() {
        return followUpReminder;
    }

    public void setFollowUpReminder(LocalDateTime followUpReminder) {
        this.followUpReminder = followUpReminder;
    }

    public String getStatusNotes() {
        return statusNotes;
    }

    public void setStatusNotes(String statusNotes) {
        this.statusNotes = statusNotes;
    }
}
