package com.careeralgo.controller;

import com.careeralgo.dto.ApplicationRequest;
import com.careeralgo.dto.ApplicationResponse;
import com.careeralgo.dto.ApplicationUpdateRequest;
import com.careeralgo.model.Interview;
import com.careeralgo.model.Offer;
import com.careeralgo.service.ApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for job application management operations
 */
@RestController
@RequestMapping("/applications")
@Tag(name = "Application Management", description = "APIs for job application tracking and management")
public class ApplicationController {

    @Autowired
    private ApplicationService applicationService;

    /**
     * Get user's applications with pagination and filtering
     */
    @GetMapping
    @Operation(summary = "Get user applications", description = "Retrieve paginated list of user's job applications")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Applications retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "400", description = "Invalid pagination parameters")
    })
    public ResponseEntity<Page<ApplicationResponse>> getUserApplications(
            Authentication authentication,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Filter by status") @RequestParam(required = false) String status) {
        
        Page<ApplicationResponse> applications = applicationService.getUserApplications(authentication, page, size, status);
        return ResponseEntity.ok(applications);
    }

    /**
     * Get specific application by ID
     */
    @GetMapping("/{applicationId}")
    @Operation(summary = "Get application by ID", description = "Retrieve a specific application by its ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Application retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Application not found")
    })
    public ResponseEntity<ApplicationResponse> getApplicationById(
            Authentication authentication,
            @Parameter(description = "Application ID") @PathVariable String applicationId) {
        
        ApplicationResponse application = applicationService.getApplicationById(authentication, applicationId);
        return ResponseEntity.ok(application);
    }

    /**
     * Create new job application
     */
    @PostMapping
    @Operation(summary = "Create application", description = "Create a new job application")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Application created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data or already applied"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Job or resume not found")
    })
    public ResponseEntity<ApplicationResponse> createApplication(
            Authentication authentication,
            @Valid @RequestBody ApplicationRequest request) {
        
        ApplicationResponse application = applicationService.createApplication(authentication, request);
        return ResponseEntity.status(201).body(application);
    }

    /**
     * Update application
     */
    @PutMapping("/{applicationId}")
    @Operation(summary = "Update application", description = "Update application status, notes, and other details")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Application updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Application not found")
    })
    public ResponseEntity<ApplicationResponse> updateApplication(
            Authentication authentication,
            @Parameter(description = "Application ID") @PathVariable String applicationId,
            @Valid @RequestBody ApplicationUpdateRequest request) {
        
        ApplicationResponse application = applicationService.updateApplication(authentication, applicationId, request);
        return ResponseEntity.ok(application);
    }

    /**
     * Delete application
     */
    @DeleteMapping("/{applicationId}")
    @Operation(summary = "Delete application", description = "Delete a job application")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Application deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Application not found")
    })
    public ResponseEntity<Void> deleteApplication(
            Authentication authentication,
            @Parameter(description = "Application ID") @PathVariable String applicationId) {
        
        applicationService.deleteApplication(authentication, applicationId);
        return ResponseEntity.noContent().build();
    }

    /**
     * Get application statistics
     */
    @GetMapping("/stats")
    @Operation(summary = "Get application statistics", description = "Retrieve application statistics and metrics for the user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<ApplicationService.ApplicationStatsResponse> getApplicationStats(Authentication authentication) {
        ApplicationService.ApplicationStatsResponse stats = applicationService.getApplicationStats(authentication);
        return ResponseEntity.ok(stats);
    }

    /**
     * Get applications needing follow-up
     */
    @GetMapping("/follow-up")
    @Operation(summary = "Get follow-up applications", description = "Retrieve applications that need follow-up action")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Follow-up applications retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<List<ApplicationResponse>> getApplicationsNeedingFollowUp(Authentication authentication) {
        List<ApplicationResponse> applications = applicationService.getApplicationsNeedingFollowUp(authentication);
        return ResponseEntity.ok(applications);
    }

    /**
     * Add interview to application
     */
    @PostMapping("/{applicationId}/interviews")
    @Operation(summary = "Add interview", description = "Add an interview to a job application")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview added successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid interview data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Application not found")
    })
    public ResponseEntity<ApplicationResponse> addInterview(
            Authentication authentication,
            @Parameter(description = "Application ID") @PathVariable String applicationId,
            @Valid @RequestBody Interview interview) {
        
        ApplicationResponse application = applicationService.addInterview(authentication, applicationId, interview);
        return ResponseEntity.ok(application);
    }

    /**
     * Update interview feedback
     */
    @PutMapping("/{applicationId}/interviews/{interviewIndex}/feedback")
    @Operation(summary = "Update interview feedback", description = "Update feedback for a specific interview")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview feedback updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid feedback data or interview index"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Application or interview not found")
    })
    public ResponseEntity<ApplicationResponse> updateInterviewFeedback(
            Authentication authentication,
            @Parameter(description = "Application ID") @PathVariable String applicationId,
            @Parameter(description = "Interview index (0-based)") @PathVariable int interviewIndex,
            @Valid @RequestBody Interview.InterviewFeedback feedback) {
        
        ApplicationResponse application = applicationService.updateInterviewFeedback(
                authentication, applicationId, interviewIndex, feedback);
        return ResponseEntity.ok(application);
    }

    /**
     * Add offer to application
     */
    @PostMapping("/{applicationId}/offer")
    @Operation(summary = "Add job offer", description = "Add a job offer to an application")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Offer added successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid offer data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Application not found")
    })
    public ResponseEntity<ApplicationResponse> addOffer(
            Authentication authentication,
            @Parameter(description = "Application ID") @PathVariable String applicationId,
            @Valid @RequestBody Offer offer) {
        
        ApplicationResponse application = applicationService.addOffer(authentication, applicationId, offer);
        return ResponseEntity.ok(application);
    }

    /**
     * Search user's applications
     */
    @GetMapping("/search")
    @Operation(summary = "Search applications", description = "Search through user's applications by job title or company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search completed successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "400", description = "Invalid search parameters")
    })
    public ResponseEntity<List<ApplicationResponse>> searchApplications(
            Authentication authentication,
            @Parameter(description = "Search term") @RequestParam String q) {
        
        if (q == null || q.trim().length() < 2) {
            return ResponseEntity.badRequest().build();
        }
        
        List<ApplicationResponse> applications = applicationService.searchApplications(authentication, q.trim());
        return ResponseEntity.ok(applications);
    }
}
