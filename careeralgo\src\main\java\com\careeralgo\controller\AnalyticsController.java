package com.careeralgo.controller;

import com.careeralgo.service.AnalyticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for analytics and reporting
 */
@RestController
@RequestMapping("/analytics")
@Tag(name = "Analytics & Reporting", description = "APIs for user analytics, insights, and reporting")
public class AnalyticsController {

    @Autowired
    private AnalyticsService analyticsService;

    /**
     * Get dashboard analytics
     */
    @GetMapping("/dashboard")
    @Operation(summary = "Get dashboard analytics", description = "Get comprehensive dashboard analytics for the user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Dashboard analytics retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<AnalyticsService.DashboardAnalytics> getDashboardAnalytics(Authentication authentication) {
        AnalyticsService.DashboardAnalytics analytics = analyticsService.getDashboardAnalytics(authentication);
        return ResponseEntity.ok(analytics);
    }

    /**
     * Get application analytics
     */
    @GetMapping("/applications")
    @Operation(summary = "Get application analytics", description = "Get detailed analytics about job applications")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Application analytics retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid period parameter"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<AnalyticsService.ApplicationAnalytics> getApplicationAnalytics(
            Authentication authentication,
            @Parameter(description = "Time period (week, month, quarter, year)") 
            @RequestParam(defaultValue = "month") String period) {
        
        AnalyticsService.ApplicationAnalytics analytics = analyticsService.getApplicationAnalytics(authentication, period);
        return ResponseEntity.ok(analytics);
    }

    /**
     * Get skill analytics
     */
    @GetMapping("/skills")
    @Operation(summary = "Get skill analytics", description = "Get analytics about user's skills and market demand")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Skill analytics retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<AnalyticsService.SkillAnalytics> getSkillAnalytics(Authentication authentication) {
        AnalyticsService.SkillAnalytics analytics = analyticsService.getSkillAnalytics(authentication);
        return ResponseEntity.ok(analytics);
    }

    /**
     * Get AI-generated insights
     */
    @GetMapping("/insights")
    @Operation(summary = "Get AI insights", description = "Get personalized AI-generated insights and recommendations")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Insights generated successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, Object>> getInsights(Authentication authentication) {
        List<String> insights = analyticsService.generateInsights(authentication);
        
        return ResponseEntity.ok(Map.of(
                "insights", insights,
                "generatedAt", java.time.LocalDateTime.now(),
                "count", insights.size()
        ));
    }

    /**
     * Track user activity
     */
    @PostMapping("/track")
    @Operation(summary = "Track user activity", description = "Track user activity for analytics purposes")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Activity tracked successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid activity data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, String>> trackActivity(
            Authentication authentication,
            @RequestBody TrackActivityRequest request) {
        
        if (request.getActivityType() == null || request.getActivityType().trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        // Extract user ID from authentication
        String userId = extractUserId(authentication);
        
        analyticsService.trackActivity(userId, request.getActivityType(), request.getMetadata());
        
        return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Activity tracked successfully"
        ));
    }

    /**
     * Get progress tracking
     */
    @GetMapping("/progress")
    @Operation(summary = "Get progress tracking", description = "Get user's career progress and goal tracking")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Progress data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, Object>> getProgress(Authentication authentication) {
        // TODO: Implement progress tracking
        Map<String, Object> progress = Map.of(
                "careerGoals", Map.of(
                        "targetRole", "Software Engineer",
                        "targetSalary", 120000,
                        "targetLocation", "San Francisco, CA",
                        "timeline", "6 months"
                ),
                "milestones", List.of(
                        Map.of("name", "Complete Profile", "completed", true, "date", "2024-01-15"),
                        Map.of("name", "Upload Resume", "completed", true, "date", "2024-01-16"),
                        Map.of("name", "Apply to 10 Jobs", "completed", false, "progress", 7),
                        Map.of("name", "Get First Interview", "completed", false, "progress", 0)
                ),
                "weeklyGoals", Map.of(
                        "applications", Map.of("target", 5, "current", 3),
                        "networking", Map.of("target", 3, "current", 1),
                        "skillDevelopment", Map.of("target", 2, "current", 1)
                )
        );
        
        return ResponseEntity.ok(progress);
    }

    /**
     * Export analytics data
     */
    @PostMapping("/export")
    @Operation(summary = "Export analytics data", description = "Export user's analytics data in specified format")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Export initiated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid export parameters"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, Object>> exportAnalytics(
            Authentication authentication,
            @RequestBody ExportRequest request) {
        
        // TODO: Implement data export functionality
        return ResponseEntity.ok(Map.of(
                "status", "initiated",
                "message", "Export will be sent to your email when ready",
                "format", request.getFormat(),
                "estimatedTime", "5-10 minutes"
        ));
    }

    /**
     * Get market analytics
     */
    @GetMapping("/market")
    @Operation(summary = "Get market analytics", description = "Get job market analytics and trends")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Market analytics retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid parameters")
    })
    public ResponseEntity<Map<String, Object>> getMarketAnalytics(
            @Parameter(description = "Industry filter") @RequestParam(required = false) String industry,
            @Parameter(description = "Location filter") @RequestParam(required = false) String location,
            @Parameter(description = "Experience level") @RequestParam(required = false) String experienceLevel) {
        
        // TODO: Implement market analytics
        Map<String, Object> marketData = Map.of(
                "jobGrowth", Map.of(
                        "overall", 5.2,
                        "technology", 8.1,
                        "healthcare", 6.3,
                        "finance", 3.8
                ),
                "salaryTrends", Map.of(
                        "averageIncrease", 4.5,
                        "topPayingSkills", List.of("Machine Learning", "Cloud Architecture", "DevOps"),
                        "emergingRoles", List.of("AI Engineer", "Data Scientist", "Product Manager")
                ),
                "demandByLocation", Map.of(
                        "San Francisco", 95,
                        "New York", 88,
                        "Seattle", 82,
                        "Austin", 76
                ),
                "skillDemand", Map.of(
                        "increasing", List.of("Python", "React", "AWS", "Kubernetes"),
                        "stable", List.of("Java", "JavaScript", "SQL"),
                        "decreasing", List.of("jQuery", "Flash", "Perl")
                )
        );
        
        return ResponseEntity.ok(marketData);
    }

    // Helper methods
    
    private String extractUserId(Authentication authentication) {
        // This would extract user ID from JWT token
        // Implementation depends on your JWT structure
        return "user-id-from-jwt";
    }

    // Request DTOs
    
    public static class TrackActivityRequest {
        private String activityType;
        private Map<String, Object> metadata;

        // Getters and setters
        public String getActivityType() { return activityType; }
        public void setActivityType(String activityType) { this.activityType = activityType; }
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }

    public static class ExportRequest {
        private String format; // CSV, JSON, PDF
        private String period; // week, month, quarter, year
        private List<String> sections; // dashboard, applications, skills

        // Getters and setters
        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }
        public String getPeriod() { return period; }
        public void setPeriod(String period) { this.period = period; }
        public List<String> getSections() { return sections; }
        public void setSections(List<String> sections) { this.sections = sections; }
    }
}
