package com.careeralgo.service;

import com.careeralgo.model.User;
import com.careeralgo.model.ClerkMetadata;
import com.careeralgo.repository.UserRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HexFormat;
import java.util.Optional;

/**
 * Service for handling Clerk webhook events
 */
@Service
public class ClerkWebhookService {

    private static final Logger logger = LoggerFactory.getLogger(ClerkWebhookService.class);
    private static final String HMAC_SHA256 = "HmacSHA256";

    @Value("${careeralgo.clerk.webhook-secret}")
    private String webhookSecret;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Validate webhook signature
     */
    public boolean validateSignature(String payload, String signature, String timestamp) {
        try {
            // Parse signature header (format: "v1,signature1 v1,signature2")
            String[] signatures = signature.split(" ");
            
            for (String sig : signatures) {
                if (sig.startsWith("v1,")) {
                    String expectedSignature = sig.substring(3);
                    String computedSignature = computeSignature(payload, timestamp);
                    
                    if (expectedSignature.equals(computedSignature)) {
                        return true;
                    }
                }
            }
            
            return false;
        } catch (Exception e) {
            logger.error("Error validating webhook signature", e);
            return false;
        }
    }

    /**
     * Process Clerk webhook event
     */
    public void processEvent(String payload) {
        try {
            JsonNode event = objectMapper.readTree(payload);
            String eventType = event.get("type").asText();
            JsonNode data = event.get("data");

            logger.info("Processing Clerk webhook event: {}", eventType);

            switch (eventType) {
                case "user.created":
                    handleUserCreated(data);
                    break;
                case "user.updated":
                    handleUserUpdated(data);
                    break;
                case "user.deleted":
                    handleUserDeleted(data);
                    break;
                default:
                    logger.warn("Unhandled webhook event type: {}", eventType);
            }
        } catch (Exception e) {
            logger.error("Error processing webhook event", e);
            throw new RuntimeException("Failed to process webhook event", e);
        }
    }

    /**
     * Handle user.created webhook
     */
    private void handleUserCreated(JsonNode data) {
        try {
            String clerkUserId = data.get("id").asText();
            
            // Check if user already exists
            if (userRepository.existsByClerkUserId(clerkUserId)) {
                logger.warn("User already exists with Clerk ID: {}", clerkUserId);
                return;
            }

            // Extract user data
            String email = extractEmail(data);
            String firstName = data.has("first_name") ? data.get("first_name").asText() : "";
            String lastName = data.has("last_name") ? data.get("last_name").asText() : "";
            String profilePicture = data.has("image_url") ? data.get("image_url").asText() : null;

            // Create new user
            User user = new User(clerkUserId, email, firstName, lastName);
            user.setProfilePicture(profilePicture);

            // Set Clerk metadata
            ClerkMetadata clerkMetadata = new ClerkMetadata();
            clerkMetadata.setCreatedAt(extractTimestamp(data, "created_at"));
            clerkMetadata.setUpdatedAt(extractTimestamp(data, "updated_at"));
            clerkMetadata.setLastSignInAt(extractTimestamp(data, "last_sign_in_at"));
            clerkMetadata.setEmailVerified(isEmailVerified(data));
            user.setClerkMetadata(clerkMetadata);

            // Save user
            userRepository.save(user);
            logger.info("Created new user from Clerk webhook: {}", email);

        } catch (Exception e) {
            logger.error("Error handling user.created webhook", e);
            throw new RuntimeException("Failed to create user from webhook", e);
        }
    }

    /**
     * Handle user.updated webhook
     */
    private void handleUserUpdated(JsonNode data) {
        try {
            String clerkUserId = data.get("id").asText();
            
            Optional<User> userOpt = userRepository.findByClerkUserId(clerkUserId);
            if (userOpt.isEmpty()) {
                logger.warn("User not found for Clerk ID: {}", clerkUserId);
                return;
            }

            User user = userOpt.get();

            // Update user data
            String email = extractEmail(data);
            if (email != null) {
                user.setEmail(email);
            }

            if (data.has("first_name")) {
                user.setFirstName(data.get("first_name").asText());
            }

            if (data.has("last_name")) {
                user.setLastName(data.get("last_name").asText());
            }

            if (data.has("image_url")) {
                user.setProfilePicture(data.get("image_url").asText());
            }

            // Update Clerk metadata
            ClerkMetadata clerkMetadata = user.getClerkMetadata();
            if (clerkMetadata == null) {
                clerkMetadata = new ClerkMetadata();
                user.setClerkMetadata(clerkMetadata);
            }

            clerkMetadata.setUpdatedAt(extractTimestamp(data, "updated_at"));
            clerkMetadata.setEmailVerified(isEmailVerified(data));

            // Save updated user
            userRepository.save(user);
            logger.info("Updated user from Clerk webhook: {}", email);

        } catch (Exception e) {
            logger.error("Error handling user.updated webhook", e);
            throw new RuntimeException("Failed to update user from webhook", e);
        }
    }

    /**
     * Handle user.deleted webhook
     */
    private void handleUserDeleted(JsonNode data) {
        try {
            String clerkUserId = data.get("id").asText();
            
            Optional<User> userOpt = userRepository.findByClerkUserId(clerkUserId);
            if (userOpt.isEmpty()) {
                logger.warn("User not found for deletion, Clerk ID: {}", clerkUserId);
                return;
            }

            User user = userOpt.get();
            
            // Soft delete - deactivate user instead of hard delete
            user.setActive(false);
            userRepository.save(user);
            
            logger.info("Deactivated user from Clerk webhook: {}", user.getEmail());

            // TODO: Implement data cleanup based on GDPR requirements
            // - Anonymize PII data
            // - Clean up related data (resumes, applications, etc.)
            // - Cancel subscriptions

        } catch (Exception e) {
            logger.error("Error handling user.deleted webhook", e);
            throw new RuntimeException("Failed to delete user from webhook", e);
        }
    }

    /**
     * Compute HMAC signature for webhook validation
     */
    private String computeSignature(String payload, String timestamp) 
            throws NoSuchAlgorithmException, InvalidKeyException {
        String signedPayload = timestamp + "." + payload;
        
        Mac mac = Mac.getInstance(HMAC_SHA256);
        SecretKeySpec secretKeySpec = new SecretKeySpec(
                webhookSecret.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
        mac.init(secretKeySpec);
        
        byte[] hash = mac.doFinal(signedPayload.getBytes(StandardCharsets.UTF_8));
        return HexFormat.of().formatHex(hash);
    }

    /**
     * Extract email from Clerk user data
     */
    private String extractEmail(JsonNode data) {
        JsonNode emailAddresses = data.get("email_addresses");
        if (emailAddresses != null && emailAddresses.isArray() && emailAddresses.size() > 0) {
            JsonNode primaryEmail = emailAddresses.get(0);
            return primaryEmail.get("email_address").asText();
        }
        return null;
    }

    /**
     * Extract timestamp from Clerk data
     */
    private LocalDateTime extractTimestamp(JsonNode data, String field) {
        if (data.has(field) && !data.get(field).isNull()) {
            long timestamp = data.get(field).asLong();
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneOffset.UTC);
        }
        return null;
    }

    /**
     * Check if email is verified
     */
    private boolean isEmailVerified(JsonNode data) {
        JsonNode emailAddresses = data.get("email_addresses");
        if (emailAddresses != null && emailAddresses.isArray() && emailAddresses.size() > 0) {
            JsonNode primaryEmail = emailAddresses.get(0);
            JsonNode verification = primaryEmail.get("verification");
            if (verification != null) {
                return "verified".equals(verification.get("status").asText());
            }
        }
        return false;
    }
}
