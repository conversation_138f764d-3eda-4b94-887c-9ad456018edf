package com.careeralgo.model;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * AI analysis results for resume
 */
public class AIAnalysis {
    
    private Integer overallScore;
    private Integer atsCompatibility;
    private Integer readabilityScore;
    private List<String> strengths;
    private List<String> improvements;
    private KeywordAnalysis keywordAnalysis;
    private Map<String, Integer> sectionScores;
    private LocalDateTime lastAnalyzedAt;

    // Constructors
    public AIAnalysis() {}

    // Getters and Setters
    public Integer getOverallScore() {
        return overallScore;
    }

    public void setOverallScore(Integer overallScore) {
        this.overallScore = overallScore;
    }

    public Integer getAtsCompatibility() {
        return atsCompatibility;
    }

    public void setAtsCompatibility(Integer atsCompatibility) {
        this.atsCompatibility = atsCompatibility;
    }

    public Integer getReadabilityScore() {
        return readabilityScore;
    }

    public void setReadabilityScore(Integer readabilityScore) {
        this.readabilityScore = readabilityScore;
    }

    public List<String> getStrengths() {
        return strengths;
    }

    public void setStrengths(List<String> strengths) {
        this.strengths = strengths;
    }

    public List<String> getImprovements() {
        return improvements;
    }

    public void setImprovements(List<String> improvements) {
        this.improvements = improvements;
    }

    public KeywordAnalysis getKeywordAnalysis() {
        return keywordAnalysis;
    }

    public void setKeywordAnalysis(KeywordAnalysis keywordAnalysis) {
        this.keywordAnalysis = keywordAnalysis;
    }

    public Map<String, Integer> getSectionScores() {
        return sectionScores;
    }

    public void setSectionScores(Map<String, Integer> sectionScores) {
        this.sectionScores = sectionScores;
    }

    public LocalDateTime getLastAnalyzedAt() {
        return lastAnalyzedAt;
    }

    public void setLastAnalyzedAt(LocalDateTime lastAnalyzedAt) {
        this.lastAnalyzedAt = lastAnalyzedAt;
    }

    /**
     * Keyword analysis results
     */
    public static class KeywordAnalysis {
        private Integer totalKeywords;
        private Integer industryRelevant;
        private List<String> missing;
        private Map<String, Integer> keywordDensity;

        // Constructors
        public KeywordAnalysis() {}

        // Getters and Setters
        public Integer getTotalKeywords() {
            return totalKeywords;
        }

        public void setTotalKeywords(Integer totalKeywords) {
            this.totalKeywords = totalKeywords;
        }

        public Integer getIndustryRelevant() {
            return industryRelevant;
        }

        public void setIndustryRelevant(Integer industryRelevant) {
            this.industryRelevant = industryRelevant;
        }

        public List<String> getMissing() {
            return missing;
        }

        public void setMissing(List<String> missing) {
            this.missing = missing;
        }

        public Map<String, Integer> getKeywordDensity() {
            return keywordDensity;
        }

        public void setKeywordDensity(Map<String, Integer> keywordDensity) {
            this.keywordDensity = keywordDensity;
        }

        public double getRelevancePercentage() {
            if (totalKeywords == null || totalKeywords == 0) return 0.0;
            return (industryRelevant != null ? industryRelevant : 0) * 100.0 / totalKeywords;
        }
    }

    public String getScoreGrade() {
        if (overallScore == null) return "N/A";
        if (overallScore >= 90) return "A";
        if (overallScore >= 80) return "B";
        if (overallScore >= 70) return "C";
        if (overallScore >= 60) return "D";
        return "F";
    }

    public boolean needsImprovement() {
        return overallScore != null && overallScore < 75;
    }
}
