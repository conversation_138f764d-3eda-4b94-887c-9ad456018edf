package com.careeralgo.controller;

import com.careeralgo.constant.SkillCategory;
import com.careeralgo.model.Skill;
import com.careeralgo.model.UserSkill;
import com.careeralgo.service.SkillsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for skills management
 */
@RestController
@RequestMapping("/skills")
@Tag(name = "Skills Management", description = "APIs for skills management and analysis")
public class SkillsController {

    @Autowired
    private SkillsService skillsService;

    /**
     * Search skills
     */
    @GetMapping
    @Operation(summary = "Search skills", description = "Search skills by name or category with pagination")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Skills retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid search parameters")
    })
    public ResponseEntity<Page<Skill>> searchSkills(
            @Parameter(description = "Search query") @RequestParam(required = false) String q,
            @Parameter(description = "Skill category") @RequestParam(required = false) SkillCategory category,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Page<Skill> skills = skillsService.searchSkills(q, category, page, size);
        return ResponseEntity.ok(skills);
    }

    /**
     * Get trending skills
     */
    @GetMapping("/trending")
    @Operation(summary = "Get trending skills", description = "Get list of trending skills in the job market")
    @ApiResponse(responseCode = "200", description = "Trending skills retrieved successfully")
    public ResponseEntity<List<Skill>> getTrendingSkills(
            @Parameter(description = "Number of skills to return") @RequestParam(defaultValue = "20") int limit) {
        
        List<Skill> skills = skillsService.getTrendingSkills(limit);
        return ResponseEntity.ok(skills);
    }

    /**
     * Get skills by category
     */
    @GetMapping("/categories/{category}")
    @Operation(summary = "Get skills by category", description = "Get skills filtered by specific category")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Skills retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid category")
    })
    public ResponseEntity<List<Skill>> getSkillsByCategory(
            @Parameter(description = "Skill category") @PathVariable SkillCategory category,
            @Parameter(description = "Number of skills to return") @RequestParam(defaultValue = "50") int limit) {
        
        List<Skill> skills = skillsService.getSkillsByCategory(category, limit);
        return ResponseEntity.ok(skills);
    }

    /**
     * Get skill details
     */
    @GetMapping("/{skillId}")
    @Operation(summary = "Get skill details", description = "Get detailed information about a specific skill")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Skill details retrieved successfully"),
            @ApiResponse(responseCode = "404", description = "Skill not found")
    })
    public ResponseEntity<Skill> getSkillById(
            @Parameter(description = "Skill ID") @PathVariable String skillId) {
        
        Optional<Skill> skill = skillsService.getSkillById(skillId);
        return skill.map(ResponseEntity::ok)
                   .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get user's skills
     */
    @GetMapping("/user")
    @Operation(summary = "Get user skills", description = "Get all skills associated with the current user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User skills retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<List<UserSkill>> getUserSkills(Authentication authentication) {
        List<UserSkill> userSkills = skillsService.getUserSkills(authentication);
        return ResponseEntity.ok(userSkills);
    }

    /**
     * Add skill to user profile
     */
    @PostMapping("/user")
    @Operation(summary = "Add user skill", description = "Add a skill to the user's profile")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Skill added successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid skill data or skill already exists"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Skill not found")
    })
    public ResponseEntity<UserSkill> addUserSkill(
            Authentication authentication,
            @Valid @RequestBody AddUserSkillRequest request) {
        
        UserSkill userSkill = skillsService.addUserSkill(
                authentication, 
                request.getSkillId(), 
                request.getProficiencyLevel(), 
                request.getYearsOfExperience()
        );
        
        return ResponseEntity.status(201).body(userSkill);
    }

    /**
     * Update user skill
     */
    @PutMapping("/user/{skillId}")
    @Operation(summary = "Update user skill", description = "Update proficiency level and experience for a user skill")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Skill updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid skill data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "User skill not found")
    })
    public ResponseEntity<UserSkill> updateUserSkill(
            Authentication authentication,
            @Parameter(description = "Skill ID") @PathVariable String skillId,
            @Valid @RequestBody UpdateUserSkillRequest request) {
        
        UserSkill userSkill = skillsService.updateUserSkill(
                authentication, 
                skillId, 
                request.getProficiencyLevel(), 
                request.getYearsOfExperience()
        );
        
        return ResponseEntity.ok(userSkill);
    }

    /**
     * Remove skill from user profile
     */
    @DeleteMapping("/user/{skillId}")
    @Operation(summary = "Remove user skill", description = "Remove a skill from the user's profile")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Skill removed successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "User skill not found")
    })
    public ResponseEntity<Void> removeUserSkill(
            Authentication authentication,
            @Parameter(description = "Skill ID") @PathVariable String skillId) {
        
        skillsService.removeUserSkill(authentication, skillId);
        return ResponseEntity.noContent().build();
    }

    /**
     * Get skill recommendations
     */
    @GetMapping("/recommendations")
    @Operation(summary = "Get skill recommendations", description = "Get personalized skill recommendations for the user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Recommendations retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<List<Skill>> getSkillRecommendations(
            Authentication authentication,
            @Parameter(description = "Number of recommendations") @RequestParam(defaultValue = "10") int limit) {
        
        List<Skill> recommendations = skillsService.getSkillRecommendations(authentication, limit);
        return ResponseEntity.ok(recommendations);
    }

    /**
     * Analyze skill gaps
     */
    @PostMapping("/gap-analysis")
    @Operation(summary = "Analyze skill gaps", description = "Analyze skill gaps for a target role")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Skill gap analysis completed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid target role"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<SkillsService.SkillGapAnalysis> analyzeSkillGaps(
            Authentication authentication,
            @RequestBody Map<String, String> request) {
        
        String targetRole = request.get("targetRole");
        if (targetRole == null || targetRole.trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        SkillsService.SkillGapAnalysis analysis = skillsService.analyzeSkillGaps(authentication, targetRole);
        return ResponseEntity.ok(analysis);
    }

    /**
     * Get skill market data
     */
    @PostMapping("/market-data")
    @Operation(summary = "Get skill market data", description = "Get market data for specified skills")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Market data retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid skill IDs")
    })
    public ResponseEntity<List<SkillsService.SkillMarketData>> getSkillMarketData(
            @RequestBody Map<String, List<String>> request) {
        
        List<String> skillIds = request.get("skillIds");
        if (skillIds == null || skillIds.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        List<SkillsService.SkillMarketData> marketData = skillsService.getSkillMarketData(skillIds);
        return ResponseEntity.ok(marketData);
    }

    /**
     * Get all skill categories
     */
    @GetMapping("/categories")
    @Operation(summary = "Get skill categories", description = "Get all available skill categories")
    @ApiResponse(responseCode = "200", description = "Categories retrieved successfully")
    public ResponseEntity<SkillCategory[]> getSkillCategories() {
        return ResponseEntity.ok(SkillCategory.values());
    }

    // Request DTOs

    public static class AddUserSkillRequest {
        private String skillId;
        private UserSkill.ProficiencyLevel proficiencyLevel;
        private Double yearsOfExperience;

        // Getters and setters
        public String getSkillId() { return skillId; }
        public void setSkillId(String skillId) { this.skillId = skillId; }
        public UserSkill.ProficiencyLevel getProficiencyLevel() { return proficiencyLevel; }
        public void setProficiencyLevel(UserSkill.ProficiencyLevel proficiencyLevel) { this.proficiencyLevel = proficiencyLevel; }
        public Double getYearsOfExperience() { return yearsOfExperience; }
        public void setYearsOfExperience(Double yearsOfExperience) { this.yearsOfExperience = yearsOfExperience; }
    }

    public static class UpdateUserSkillRequest {
        private UserSkill.ProficiencyLevel proficiencyLevel;
        private Double yearsOfExperience;

        // Getters and setters
        public UserSkill.ProficiencyLevel getProficiencyLevel() { return proficiencyLevel; }
        public void setProficiencyLevel(UserSkill.ProficiencyLevel proficiencyLevel) { this.proficiencyLevel = proficiencyLevel; }
        public Double getYearsOfExperience() { return yearsOfExperience; }
        public void setYearsOfExperience(Double yearsOfExperience) { this.yearsOfExperience = yearsOfExperience; }
    }
}
