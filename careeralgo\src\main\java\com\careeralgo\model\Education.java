package com.careeralgo.model;

import java.time.LocalDate;
import java.util.List;

/**
 * Education entry from resume
 */
public class Education {
    
    private String institution;
    private String degree;
    private String field;
    private LocalDate startDate;
    private LocalDate endDate;
    private String gpa;
    private List<String> honors;
    private List<String> relevantCoursework;

    // Constructors
    public Education() {}

    public Education(String institution, String degree, String field) {
        this.institution = institution;
        this.degree = degree;
        this.field = field;
    }

    // Getters and Setters
    public String getInstitution() {
        return institution;
    }

    public void setInstitution(String institution) {
        this.institution = institution;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getGpa() {
        return gpa;
    }

    public void setGpa(String gpa) {
        this.gpa = gpa;
    }

    public List<String> getHonors() {
        return honors;
    }

    public void setHonors(List<String> honors) {
        this.honors = honors;
    }

    public List<String> getRelevantCoursework() {
        return relevantCoursework;
    }

    public void setRelevantCoursework(List<String> relevantCoursework) {
        this.relevantCoursework = relevantCoursework;
    }

    public String getFullDegree() {
        if (degree != null && field != null) {
            return degree + " in " + field;
        }
        return degree != null ? degree : field;
    }

    public String getDurationString() {
        if (startDate == null && endDate == null) return "Unknown duration";
        
        String start = startDate != null ? String.valueOf(startDate.getYear()) : "Unknown";
        String end = endDate != null ? String.valueOf(endDate.getYear()) : "Present";
        
        return start + " - " + end;
    }
}
