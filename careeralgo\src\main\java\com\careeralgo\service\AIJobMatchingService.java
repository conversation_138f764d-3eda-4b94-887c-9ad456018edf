package com.careeralgo.service;

import com.careeralgo.model.Job;
import com.careeralgo.model.User;
import com.careeralgo.repository.JobRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI-powered job matching and recommendation service
 */
@Service
public class AIJobMatchingService {

    private static final Logger logger = LoggerFactory.getLogger(AIJobMatchingService.class);

    @Autowired
    private JobRepository jobRepository;

    /**
     * Get personalized job recommendations for a user
     */
    public List<Job> getPersonalizedRecommendations(User user, int limit) {
        logger.info("Generating personalized recommendations for user: {}", user.getId());
        
        List<Job> recommendations = new ArrayList<>();
        
        try {
            // Basic recommendation logic based on user profile
            if (user.getProfile() != null) {
                
                // 1. Match by skills
                if (user.getProfile().getSkills() != null && !user.getProfile().getSkills().isEmpty()) {
                    Pageable pageable = PageRequest.of(0, limit / 2);
                    List<Job> skillBasedJobs = jobRepository.findBySkills(user.getProfile().getSkills(), pageable)
                            .getContent();
                    recommendations.addAll(skillBasedJobs);
                }
                
                // 2. Match by experience level
                if (user.getProfile().getExperienceLevel() != null) {
                    Pageable pageable = PageRequest.of(0, limit / 3);
                    List<Job> experienceBasedJobs = jobRepository
                            .findByExperienceLevelAndIsActiveTrueOrderByPostedDateDesc(
                                    user.getProfile().getExperienceLevel(), pageable)
                            .getContent();
                    recommendations.addAll(experienceBasedJobs);
                }
                
                // 3. Match by location preference
                if (user.getProfile().getLocation() != null && 
                    user.getProfile().getLocation().getCity() != null) {
                    Pageable pageable = PageRequest.of(0, limit / 3);
                    List<Job> locationBasedJobs = jobRepository
                            .findByLocation(user.getProfile().getLocation().getCity(), pageable)
                            .getContent();
                    recommendations.addAll(locationBasedJobs);
                }
                
                // 4. Include remote jobs if user prefers remote work
                if (user.getProfile().getRemotePreference() != null && 
                    user.getProfile().getRemotePreference().toString().contains("REMOTE")) {
                    Pageable pageable = PageRequest.of(0, limit / 4);
                    List<Job> remoteJobs = jobRepository.findRemoteJobs(pageable).getContent();
                    recommendations.addAll(remoteJobs);
                }
            }
            
            // Remove duplicates and limit results
            recommendations = recommendations.stream()
                    .distinct()
                    .limit(limit)
                    .collect(Collectors.toList());
            
            // If we don't have enough recommendations, fill with trending jobs
            if (recommendations.size() < limit) {
                int remaining = limit - recommendations.size();
                Pageable pageable = PageRequest.of(0, remaining);
                List<Job> trendingJobs = jobRepository.findTrendingJobs(pageable);
                
                // Add trending jobs that aren't already in recommendations
                for (Job trendingJob : trendingJobs) {
                    if (!recommendations.contains(trendingJob)) {
                        recommendations.add(trendingJob);
                        if (recommendations.size() >= limit) break;
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("Error generating personalized recommendations", e);
            
            // Fallback to trending jobs
            Pageable pageable = PageRequest.of(0, limit);
            recommendations = jobRepository.findTrendingJobs(pageable);
        }
        
        logger.info("Generated {} personalized recommendations for user: {}", 
                recommendations.size(), user.getId());
        
        return recommendations;
    }

    /**
     * Calculate job match score for a user
     */
    public int calculateJobMatchScore(User user, Job job) {
        int score = 0;
        int maxScore = 100;
        
        if (user.getProfile() == null) {
            return 0;
        }
        
        // Skills matching (40% weight)
        if (user.getProfile().getSkills() != null && job.getSkills() != null) {
            long matchingSkills = user.getProfile().getSkills().stream()
                    .filter(skill -> job.getSkills().contains(skill))
                    .count();
            
            if (user.getProfile().getSkills().size() > 0) {
                score += (int) ((matchingSkills * 40.0) / user.getProfile().getSkills().size());
            }
        }
        
        // Experience level matching (25% weight)
        if (user.getProfile().getExperienceLevel() != null && 
            user.getProfile().getExperienceLevel().equals(job.getExperienceLevel())) {
            score += 25;
        }
        
        // Location matching (20% weight)
        if (user.getProfile().getLocation() != null && job.getLocation() != null) {
            if (user.getProfile().getRemotePreference() != null && 
                user.getProfile().getRemotePreference().toString().contains("REMOTE") &&
                job.getLocation().isRemote()) {
                score += 20;
            } else if (user.getProfile().getLocation().getCity() != null &&
                      job.getLocation().getCity() != null &&
                      user.getProfile().getLocation().getCity().equalsIgnoreCase(job.getLocation().getCity())) {
                score += 20;
            }
        }
        
        // Job title similarity (15% weight)
        if (user.getProfile().getJobTitle() != null && job.getTitle() != null) {
            if (job.getTitle().toLowerCase().contains(user.getProfile().getJobTitle().toLowerCase()) ||
                user.getProfile().getJobTitle().toLowerCase().contains(job.getTitle().toLowerCase())) {
                score += 15;
            }
        }
        
        return Math.min(score, maxScore);
    }

    /**
     * Get job recommendations based on a specific job
     */
    public List<Job> getJobBasedRecommendations(Job baseJob, int limit) {
        logger.info("Generating job-based recommendations for job: {}", baseJob.getId());
        
        Pageable pageable = PageRequest.of(0, limit);
        List<Job> similarJobs = jobRepository.findSimilarJobs(
                baseJob.getTitle(), baseJob.getSkills(), baseJob.getId(), pageable);
        
        logger.info("Generated {} job-based recommendations", similarJobs.size());
        return similarJobs;
    }

    /**
     * Get trending skills from job postings
     */
    public List<String> getTrendingSkills(int limit) {
        // TODO: Implement trending skills analysis
        // This would analyze job postings to find most frequently mentioned skills
        
        // Placeholder implementation
        return List.of(
                "JavaScript", "Python", "Java", "React", "Node.js",
                "AWS", "Docker", "Kubernetes", "SQL", "Git"
        ).subList(0, Math.min(limit, 10));
    }

    /**
     * Analyze job market trends
     */
    public JobMarketAnalysis analyzeJobMarket(String location, String industry) {
        // TODO: Implement comprehensive job market analysis
        // This would analyze job postings, salary trends, demand patterns, etc.
        
        JobMarketAnalysis analysis = new JobMarketAnalysis();
        analysis.setLocation(location);
        analysis.setIndustry(industry);
        analysis.setTotalJobs(jobRepository.countByIsActiveTrue());
        analysis.setAverageSalary(75000); // Placeholder
        analysis.setGrowthRate(5.2); // Placeholder
        
        return analysis;
    }

    /**
     * Job market analysis result
     */
    public static class JobMarketAnalysis {
        private String location;
        private String industry;
        private Long totalJobs;
        private Integer averageSalary;
        private Double growthRate;
        private List<String> topSkills;
        private List<String> topCompanies;

        // Getters and Setters
        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getIndustry() {
            return industry;
        }

        public void setIndustry(String industry) {
            this.industry = industry;
        }

        public Long getTotalJobs() {
            return totalJobs;
        }

        public void setTotalJobs(Long totalJobs) {
            this.totalJobs = totalJobs;
        }

        public Integer getAverageSalary() {
            return averageSalary;
        }

        public void setAverageSalary(Integer averageSalary) {
            this.averageSalary = averageSalary;
        }

        public Double getGrowthRate() {
            return growthRate;
        }

        public void setGrowthRate(Double growthRate) {
            this.growthRate = growthRate;
        }

        public List<String> getTopSkills() {
            return topSkills;
        }

        public void setTopSkills(List<String> topSkills) {
            this.topSkills = topSkills;
        }

        public List<String> getTopCompanies() {
            return topCompanies;
        }

        public void setTopCompanies(List<String> topCompanies) {
            this.topCompanies = topCompanies;
        }
    }
}
