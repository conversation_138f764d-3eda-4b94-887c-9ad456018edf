package com.careeralgo.repository;

import com.careeralgo.constant.ExperienceLevel;
import com.careeralgo.model.Job;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Job document operations
 */
@Repository
public interface JobRepository extends MongoRepository<Job, String> {

    /**
     * Find job by external ID and source
     */
    Optional<Job> findByExternalIdAndSource(String externalId, Job.JobSource source);

    /**
     * Find job by slug
     */
    Optional<Job> findBySlug(String slug);

    /**
     * Find all active jobs
     */
    Page<Job> findByIsActiveTrueOrderByPostedDateDesc(Pageable pageable);

    /**
     * Find featured jobs
     */
    List<Job> findByIsFeaturedTrueAndIsActiveTrueOrderByPostedDateDesc();

    /**
     * Find jobs by company name
     */
    @Query("{ 'company.name': { $regex: ?0, $options: 'i' }, 'isActive': true }")
    Page<Job> findByCompanyName(String companyName, Pageable pageable);

    /**
     * Find jobs by location
     */
    @Query("{ $or: [ " +
           "{ 'location.city': { $regex: ?0, $options: 'i' } }, " +
           "{ 'location.state': { $regex: ?0, $options: 'i' } }, " +
           "{ 'location.country': { $regex: ?0, $options: 'i' } } " +
           "], 'isActive': true }")
    Page<Job> findByLocation(String location, Pageable pageable);

    /**
     * Find remote jobs
     */
    @Query("{ 'location.isRemote': true, 'isActive': true }")
    Page<Job> findRemoteJobs(Pageable pageable);

    /**
     * Find hybrid jobs
     */
    @Query("{ 'location.isHybrid': true, 'isActive': true }")
    Page<Job> findHybridJobs(Pageable pageable);

    /**
     * Find jobs by experience level
     */
    Page<Job> findByExperienceLevelAndIsActiveTrueOrderByPostedDateDesc(
            ExperienceLevel experienceLevel, Pageable pageable);

    /**
     * Find jobs by employment type
     */
    Page<Job> findByEmploymentTypeAndIsActiveTrueOrderByPostedDateDesc(
            Job.EmploymentType employmentType, Pageable pageable);

    /**
     * Find jobs by skills
     */
    @Query("{ 'skills': { $in: ?0 }, 'isActive': true }")
    Page<Job> findBySkills(List<String> skills, Pageable pageable);

    /**
     * Find jobs by salary range
     */
    @Query("{ 'salary.min': { $lte: ?1 }, 'salary.max': { $gte: ?0 }, 'isActive': true }")
    Page<Job> findBySalaryRange(Integer minSalary, Integer maxSalary, Pageable pageable);

    /**
     * Search jobs by title and description
     */
    @Query("{ $text: { $search: ?0 }, 'isActive': true }")
    Page<Job> searchJobs(String searchTerm, Pageable pageable);

    /**
     * Find jobs posted after a specific date
     */
    Page<Job> findByPostedDateAfterAndIsActiveTrueOrderByPostedDateDesc(
            LocalDateTime date, Pageable pageable);

    /**
     * Find jobs with application deadline before date
     */
    @Query("{ 'applicationDeadline': { $lt: ?0 }, 'isActive': true }")
    List<Job> findJobsWithUpcomingDeadlines(LocalDateTime date);

    /**
     * Find expired jobs
     */
    @Query("{ 'applicationDeadline': { $lt: ?0 }, 'isActive': true }")
    List<Job> findExpiredJobs(LocalDateTime currentDate);

    /**
     * Find jobs by source
     */
    Page<Job> findBySourceAndIsActiveTrueOrderByPostedDateDesc(
            Job.JobSource source, Pageable pageable);

    /**
     * Find jobs that need syncing
     */
    @Query("{ 'lastSyncedAt': { $lt: ?0 } }")
    List<Job> findJobsNeedingSyncing(LocalDateTime cutoffDate);

    /**
     * Find trending jobs (high view count)
     */
    @Query(value = "{ 'isActive': true }", sort = "{ 'viewCount': -1 }")
    List<Job> findTrendingJobs(Pageable pageable);

    /**
     * Find popular jobs (high application count)
     */
    @Query(value = "{ 'isActive': true }", sort = "{ 'applicationCount': -1 }")
    List<Job> findPopularJobs(Pageable pageable);

    /**
     * Find jobs by company size
     */
    @Query("{ 'company.size': ?0, 'isActive': true }")
    Page<Job> findByCompanySize(String companySize, Pageable pageable);

    /**
     * Find jobs by industry
     */
    @Query("{ 'company.industry': { $regex: ?0, $options: 'i' }, 'isActive': true }")
    Page<Job> findByIndustry(String industry, Pageable pageable);

    /**
     * Find jobs with specific tags
     */
    @Query("{ 'tags': { $in: ?0 }, 'isActive': true }")
    Page<Job> findByTags(List<String> tags, Pageable pageable);

    /**
     * Count jobs by source
     */
    long countBySource(Job.JobSource source);

    /**
     * Count active jobs
     */
    long countByIsActiveTrue();

    /**
     * Count jobs posted today
     */
    @Query(value = "{ 'postedDate': { $gte: ?0, $lt: ?1 } }", count = true)
    long countJobsPostedBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find similar jobs based on title and skills
     */
    @Query("{ $or: [ " +
           "{ 'title': { $regex: ?0, $options: 'i' } }, " +
           "{ 'skills': { $in: ?1 } } " +
           "], 'isActive': true, '_id': { $ne: ?2 } }")
    List<Job> findSimilarJobs(String title, List<String> skills, String excludeJobId, Pageable pageable);

    /**
     * Advanced job search with multiple filters
     */
    @Query("{ " +
           "$and: [ " +
           "{ 'isActive': true }, " +
           "{ $or: [ " +
           "  { ?0: null }, " +
           "  { 'title': { $regex: ?0, $options: 'i' } }, " +
           "  { 'description': { $regex: ?0, $options: 'i' } } " +
           "] }, " +
           "{ $or: [ { ?1: null }, { 'location.city': { $regex: ?1, $options: 'i' } } ] }, " +
           "{ $or: [ { ?2: null }, { 'experienceLevel': ?2 } ] }, " +
           "{ $or: [ { ?3: null }, { 'location.isRemote': ?3 } ] } " +
           "] }")
    Page<Job> advancedJobSearch(String keyword, String location, 
                               ExperienceLevel experienceLevel, Boolean isRemote, Pageable pageable);
}
