package com.careeralgo.constant;

/**
 * Enumeration of skill categories for organizing and classifying skills
 */
public enum SkillCategory {
    
    // Technical Skills
    PROGRAMMING_LANGUAGES("Programming Languages", "Languages used for software development"),
    FRAMEWORKS_LIBRARIES("Frameworks & Libraries", "Software frameworks and libraries"),
    DATABASES("Databases", "Database technologies and management systems"),
    CLOUD_PLATFORMS("Cloud Platforms", "Cloud computing platforms and services"),
    DEVOPS_TOOLS("DevOps Tools", "Development operations and automation tools"),
    MOBILE_DEVELOPMENT("Mobile Development", "Mobile application development technologies"),
    WEB_DEVELOPMENT("Web Development", "Web development technologies and tools"),
    DATA_SCIENCE("Data Science", "Data analysis, machine learning, and AI technologies"),
    CYBERSECURITY("Cybersecurity", "Information security and cybersecurity tools"),
    NETWORKING("Networking", "Network technologies and protocols"),
    OPERATING_SYSTEMS("Operating Systems", "Operating system platforms and administration"),
    SOFTWARE_TESTING("Software Testing", "Testing frameworks and methodologies"),
    
    // Design Skills
    UI_UX_DESIGN("UI/UX Design", "User interface and user experience design"),
    GRAPHIC_DESIGN("Graphic Design", "Visual design and graphics creation"),
    PRODUCT_DESIGN("Product Design", "Product design and prototyping"),
    WEB_DESIGN("Web Design", "Website design and layout"),
    MOTION_GRAPHICS("Motion Graphics", "Animation and motion design"),
    
    // Business Skills
    PROJECT_MANAGEMENT("Project Management", "Project planning and execution methodologies"),
    PRODUCT_MANAGEMENT("Product Management", "Product strategy and lifecycle management"),
    BUSINESS_ANALYSIS("Business Analysis", "Business process analysis and requirements gathering"),
    DIGITAL_MARKETING("Digital Marketing", "Online marketing strategies and tools"),
    SALES("Sales", "Sales techniques and customer relationship management"),
    FINANCE("Finance", "Financial analysis and accounting"),
    OPERATIONS("Operations", "Business operations and process optimization"),
    STRATEGY("Strategy", "Business strategy and planning"),
    
    // Communication Skills
    WRITTEN_COMMUNICATION("Written Communication", "Writing and documentation skills"),
    VERBAL_COMMUNICATION("Verbal Communication", "Speaking and presentation skills"),
    TECHNICAL_WRITING("Technical Writing", "Technical documentation and communication"),
    PUBLIC_SPEAKING("Public Speaking", "Presentation and public speaking abilities"),
    
    // Leadership Skills
    TEAM_LEADERSHIP("Team Leadership", "Leading and managing teams"),
    MENTORING("Mentoring", "Coaching and developing others"),
    CHANGE_MANAGEMENT("Change Management", "Managing organizational change"),
    DECISION_MAKING("Decision Making", "Strategic and operational decision making"),
    
    // Analytical Skills
    DATA_ANALYSIS("Data Analysis", "Data interpretation and statistical analysis"),
    RESEARCH("Research", "Research methodologies and information gathering"),
    PROBLEM_SOLVING("Problem Solving", "Analytical thinking and solution development"),
    CRITICAL_THINKING("Critical Thinking", "Logical reasoning and evaluation"),
    
    // Creative Skills
    CREATIVE_THINKING("Creative Thinking", "Innovation and creative problem solving"),
    CONTENT_CREATION("Content Creation", "Creating various types of content"),
    STORYTELLING("Storytelling", "Narrative development and communication"),
    BRAINSTORMING("Brainstorming", "Idea generation and creative collaboration"),
    
    // Industry-Specific Skills
    HEALTHCARE("Healthcare", "Medical and healthcare-related skills"),
    EDUCATION("Education", "Teaching and educational methodologies"),
    LEGAL("Legal", "Legal knowledge and compliance"),
    MANUFACTURING("Manufacturing", "Production and manufacturing processes"),
    RETAIL("Retail", "Retail operations and customer service"),
    HOSPITALITY("Hospitality", "Hotel, restaurant, and service industry skills"),
    REAL_ESTATE("Real Estate", "Property management and real estate transactions"),
    
    // Language Skills
    FOREIGN_LANGUAGES("Foreign Languages", "Non-native language proficiency"),
    TRANSLATION("Translation", "Language translation and interpretation"),
    
    // Soft Skills
    TEAMWORK("Teamwork", "Collaboration and team participation"),
    ADAPTABILITY("Adaptability", "Flexibility and change adaptation"),
    TIME_MANAGEMENT("Time Management", "Organizing and prioritizing tasks"),
    EMOTIONAL_INTELLIGENCE("Emotional Intelligence", "Understanding and managing emotions"),
    CONFLICT_RESOLUTION("Conflict Resolution", "Resolving disputes and disagreements"),
    CUSTOMER_SERVICE("Customer Service", "Customer interaction and support"),
    NEGOTIATION("Negotiation", "Negotiating agreements and deals"),
    
    // Compliance and Certifications
    COMPLIANCE("Compliance", "Regulatory compliance and standards"),
    CERTIFICATIONS("Certifications", "Professional certifications and credentials"),
    
    // Other
    OTHER("Other", "Skills that don't fit into other categories");

    private final String displayName;
    private final String description;

    SkillCategory(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Get category by display name (case insensitive)
     */
    public static SkillCategory fromDisplayName(String displayName) {
        for (SkillCategory category : values()) {
            if (category.displayName.equalsIgnoreCase(displayName)) {
                return category;
            }
        }
        return OTHER;
    }

    /**
     * Check if this is a technical category
     */
    public boolean isTechnical() {
        return this == PROGRAMMING_LANGUAGES || this == FRAMEWORKS_LIBRARIES || 
               this == DATABASES || this == CLOUD_PLATFORMS || this == DEVOPS_TOOLS ||
               this == MOBILE_DEVELOPMENT || this == WEB_DEVELOPMENT || this == DATA_SCIENCE ||
               this == CYBERSECURITY || this == NETWORKING || this == OPERATING_SYSTEMS ||
               this == SOFTWARE_TESTING;
    }

    /**
     * Check if this is a design category
     */
    public boolean isDesign() {
        return this == UI_UX_DESIGN || this == GRAPHIC_DESIGN || this == PRODUCT_DESIGN ||
               this == WEB_DESIGN || this == MOTION_GRAPHICS;
    }

    /**
     * Check if this is a business category
     */
    public boolean isBusiness() {
        return this == PROJECT_MANAGEMENT || this == PRODUCT_MANAGEMENT || 
               this == BUSINESS_ANALYSIS || this == DIGITAL_MARKETING || this == SALES ||
               this == FINANCE || this == OPERATIONS || this == STRATEGY;
    }

    /**
     * Check if this is a soft skill category
     */
    public boolean isSoftSkill() {
        return this == TEAMWORK || this == ADAPTABILITY || this == TIME_MANAGEMENT ||
               this == EMOTIONAL_INTELLIGENCE || this == CONFLICT_RESOLUTION ||
               this == CUSTOMER_SERVICE || this == NEGOTIATION || this == WRITTEN_COMMUNICATION ||
               this == VERBAL_COMMUNICATION || this == PUBLIC_SPEAKING;
    }

    /**
     * Get all technical categories
     */
    public static SkillCategory[] getTechnicalCategories() {
        return new SkillCategory[]{
            PROGRAMMING_LANGUAGES, FRAMEWORKS_LIBRARIES, DATABASES, CLOUD_PLATFORMS,
            DEVOPS_TOOLS, MOBILE_DEVELOPMENT, WEB_DEVELOPMENT, DATA_SCIENCE,
            CYBERSECURITY, NETWORKING, OPERATING_SYSTEMS, SOFTWARE_TESTING
        };
    }

    /**
     * Get all business categories
     */
    public static SkillCategory[] getBusinessCategories() {
        return new SkillCategory[]{
            PROJECT_MANAGEMENT, PRODUCT_MANAGEMENT, BUSINESS_ANALYSIS, DIGITAL_MARKETING,
            SALES, FINANCE, OPERATIONS, STRATEGY
        };
    }

    /**
     * Get all design categories
     */
    public static SkillCategory[] getDesignCategories() {
        return new SkillCategory[]{
            UI_UX_DESIGN, GRAPHIC_DESIGN, PRODUCT_DESIGN, WEB_DESIGN, MOTION_GRAPHICS
        };
    }

    @Override
    public String toString() {
        return displayName;
    }
}
