package com.careeralgo.repository;

import com.careeralgo.model.Analytics;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for Analytics entities
 */
@Repository
public interface AnalyticsRepository extends MongoRepository<Analytics, String> {

    /**
     * Find analytics by user ID
     */
    List<Analytics> findByUserId(String userId);

    /**
     * Find analytics by user ID with pagination
     */
    Page<Analytics> findByUserId(String userId, Pageable pageable);

    /**
     * Find analytics by event type
     */
    List<Analytics> findByEventType(String eventType);

    /**
     * Find analytics by user ID and event type
     */
    List<Analytics> findByUserIdAndEventType(String userId, String eventType);

    /**
     * Find analytics by user ID and event category
     */
    List<Analytics> findByUserIdAndEventCategory(String userId, String eventCategory);

    /**
     * Find analytics by timestamp range
     */
    List<Analytics> findByTimestampBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find analytics by user ID and timestamp range
     */
    List<Analytics> findByUserIdAndTimestampBetween(String userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find analytics by user ID and timestamp after
     */
    List<Analytics> findByUserIdAndTimestampAfter(String userId, LocalDateTime timestamp);

    /**
     * Find analytics by session ID
     */
    List<Analytics> findBySessionId(String sessionId);

    /**
     * Find analytics by event type and timestamp range
     */
    List<Analytics> findByEventTypeAndTimestampBetween(String eventType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find analytics by event category and timestamp range
     */
    List<Analytics> findByEventCategoryAndTimestampBetween(String eventCategory, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Count analytics by user ID
     */
    long countByUserId(String userId);

    /**
     * Count analytics by event type
     */
    long countByEventType(String eventType);

    /**
     * Count analytics by user ID and event type
     */
    long countByUserIdAndEventType(String userId, String eventType);

    /**
     * Count analytics by user ID and timestamp range
     */
    long countByUserIdAndTimestampBetween(String userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Count analytics by event type and timestamp range
     */
    long countByEventTypeAndTimestampBetween(String eventType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find analytics by multiple event types
     */
    List<Analytics> findByEventTypeIn(List<String> eventTypes);

    /**
     * Find analytics by user ID and multiple event types
     */
    List<Analytics> findByUserIdAndEventTypeIn(String userId, List<String> eventTypes);

    /**
     * Find analytics by platform
     */
    List<Analytics> findByPlatform(String platform);

    /**
     * Find analytics by source
     */
    List<Analytics> findBySource(String source);

    /**
     * Find analytics by medium
     */
    List<Analytics> findByMedium(String medium);

    /**
     * Find analytics by campaign
     */
    List<Analytics> findByCampaign(String campaign);

    /**
     * Find recent analytics (last N hours)
     */
    @Query("{'timestamp': {'$gte': ?0}}")
    List<Analytics> findRecentAnalytics(LocalDateTime since);

    /**
     * Find analytics by user ID ordered by timestamp descending
     */
    List<Analytics> findByUserIdOrderByTimestampDesc(String userId);

    /**
     * Find analytics by event type ordered by timestamp descending
     */
    List<Analytics> findByEventTypeOrderByTimestampDesc(String eventType);

    /**
     * Find top analytics by timestamp
     */
    @Query(value = "{}", sort = "{'timestamp': -1}")
    List<Analytics> findTopByTimestamp(Pageable pageable);

    /**
     * Find analytics with event data containing specific key
     */
    @Query("{'eventData.?0': {'$exists': true}}")
    List<Analytics> findByEventDataContaining(String key);

    /**
     * Find analytics by user ID with event data containing specific key
     */
    @Query("{'userId': ?0, 'eventData.?1': {'$exists': true}}")
    List<Analytics> findByUserIdAndEventDataContaining(String userId, String key);

    /**
     * Find analytics by event data value
     */
    @Query("{'eventData.?0': ?1}")
    List<Analytics> findByEventDataValue(String key, Object value);

    /**
     * Find unique event types
     */
    @Query(value = "{}", fields = "{'eventType': 1}")
    List<String> findDistinctEventTypes();

    /**
     * Find unique event categories
     */
    @Query(value = "{}", fields = "{'eventCategory': 1}")
    List<String> findDistinctEventCategories();

    /**
     * Find analytics by IP address
     */
    List<Analytics> findByIpAddress(String ipAddress);

    /**
     * Find analytics by user agent containing
     */
    @Query("{'userAgent': {'$regex': ?0, '$options': 'i'}}")
    List<Analytics> findByUserAgentContaining(String userAgent);

    /**
     * Delete analytics older than specified date
     */
    void deleteByTimestampBefore(LocalDateTime timestamp);

    /**
     * Delete analytics by user ID
     */
    void deleteByUserId(String userId);

    /**
     * Find analytics by multiple criteria
     */
    @Query("{'userId': ?0, 'eventType': {'$in': ?1}, 'timestamp': {'$gte': ?2, '$lte': ?3}}")
    List<Analytics> findByMultipleCriteria(String userId, List<String> eventTypes, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Count unique users by event type
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'eventType': ?0 } }",
        "{ '$group': { '_id': '$userId' } }",
        "{ '$count': 'uniqueUsers' }"
    })
    Long countUniqueUsersByEventType(String eventType);

    /**
     * Count events by user in time range
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'userId': ?0, 'timestamp': { '$gte': ?1, '$lte': ?2 } } }",
        "{ '$group': { '_id': '$eventType', 'count': { '$sum': 1 } } }"
    })
    List<Object> countEventsByUserInTimeRange(String userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find most active users
     */
    @Aggregation(pipeline = {
        "{ '$group': { '_id': '$userId', 'eventCount': { '$sum': 1 } } }",
        "{ '$sort': { 'eventCount': -1 } }",
        "{ '$limit': ?0 }"
    })
    List<Object> findMostActiveUsers(int limit);

    /**
     * Find popular event types
     */
    @Aggregation(pipeline = {
        "{ '$group': { '_id': '$eventType', 'count': { '$sum': 1 } } }",
        "{ '$sort': { 'count': -1 } }",
        "{ '$limit': ?0 }"
    })
    List<Object> findPopularEventTypes(int limit);

    /**
     * Find analytics by time range with pagination
     */
    Page<Analytics> findByTimestampBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Check if analytics exists for user and event type
     */
    boolean existsByUserIdAndEventType(String userId, String eventType);

    /**
     * Find first analytics by user ID ordered by timestamp
     */
    Analytics findFirstByUserIdOrderByTimestampAsc(String userId);

    /**
     * Find last analytics by user ID ordered by timestamp
     */
    Analytics findFirstByUserIdOrderByTimestampDesc(String userId);

    /**
     * Count analytics by user ID and event type in time range
     */
    long countByUserIdAndEventTypeAndTimestampBetween(String userId, String eventType, LocalDateTime startTime, LocalDateTime endTime);
}
