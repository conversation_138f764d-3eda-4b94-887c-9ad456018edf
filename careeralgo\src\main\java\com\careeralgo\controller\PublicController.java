package com.careeralgo.controller;

import com.careeralgo.dto.ResumeResponse;
import com.careeralgo.service.ResumeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Public controller for non-authenticated endpoints
 */
@RestController
@RequestMapping("/public")
@Tag(name = "Public Access", description = "Public endpoints that don't require authentication")
public class PublicController {

    @Autowired
    private ResumeService resumeService;

    /**
     * Get resume by shareable link (public access)
     */
    @GetMapping("/resume/share/{shareToken}")
    @Operation(summary = "Get shared resume", description = "Access a resume via its public shareable link")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Resume retrieved successfully"),
            @ApiResponse(responseCode = "404", description = "Resume not found or link expired"),
            @ApiResponse(responseCode = "400", description = "Invalid share token")
    })
    public ResponseEntity<ResumeResponse> getSharedResume(
            @Parameter(description = "Share token from the shareable link") @PathVariable String shareToken) {
        
        if (shareToken == null || shareToken.trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        try {
            String shareableLink = "https://careeralgo.com/resume/share/" + shareToken;
            ResumeResponse resume = resumeService.getResumeByShareableLink(shareableLink);
            return ResponseEntity.ok(resume);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Health check for public endpoints
     */
    @GetMapping("/health")
    @Operation(summary = "Public health check", description = "Check if public endpoints are accessible")
    @ApiResponse(responseCode = "200", description = "Public endpoints are healthy")
    public ResponseEntity<String> publicHealth() {
        return ResponseEntity.ok("Public endpoints are healthy");
    }
}
