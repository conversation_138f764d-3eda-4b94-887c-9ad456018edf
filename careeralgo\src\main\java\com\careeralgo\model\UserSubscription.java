package com.careeralgo.model;

import com.careeralgo.constant.SubscriptionPlan;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * User subscription model for premium features management
 */
@Document(collection = "user_subscriptions")
public class UserSubscription {

    @Id
    private String id;
    private String userId;
    private SubscriptionPlan plan;
    private SubscriptionStatus status;
    private String stripeCustomerId;
    private String stripeSubscriptionId;
    private String paymentMethodId;
    private BigDecimal amount;
    private String currency;
    private BillingCycle billingCycle;
    private LocalDateTime currentPeriodStart;
    private LocalDateTime currentPeriodEnd;
    private LocalDateTime trialStart;
    private LocalDateTime trialEnd;
    private boolean isTrialActive;
    private LocalDateTime canceledAt;
    private String cancellationReason;
    private boolean cancelAtPeriodEnd;
    private UsageLimits usageLimits;
    private UsageTracking usageTracking;
    private List<PaymentRecord> paymentHistory;
    private Map<String, Object> metadata;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public UserSubscription() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.status = SubscriptionStatus.PENDING;
        this.currency = "USD";
        this.isTrialActive = false;
        this.cancelAtPeriodEnd = false;
        this.usageTracking = new UsageTracking();
    }

    public UserSubscription(String userId, SubscriptionPlan plan) {
        this();
        this.userId = userId;
        this.plan = plan;
        this.usageLimits = getDefaultUsageLimits(plan);
    }

    // Enums
    public enum SubscriptionStatus {
        PENDING("Pending"),
        ACTIVE("Active"),
        TRIALING("Trialing"),
        PAST_DUE("Past Due"),
        CANCELED("Canceled"),
        UNPAID("Unpaid"),
        INCOMPLETE("Incomplete"),
        INCOMPLETE_EXPIRED("Incomplete Expired");

        private final String displayName;

        SubscriptionStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    public enum BillingCycle {
        MONTHLY("Monthly"),
        YEARLY("Yearly");

        private final String displayName;

        BillingCycle(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // Nested classes
    public static class UsageLimits {
        private Integer monthlyApplications;
        private Integer resumeVersions;
        private Integer interviewSessions;
        private Integer aiAnalyses;
        private boolean emailNotifications;
        private boolean linkedinIntegration;
        private boolean prioritySupport;
        private boolean advancedAnalytics;

        public UsageLimits() {}

        public UsageLimits(Integer monthlyApplications, Integer resumeVersions, Integer interviewSessions, 
                          Integer aiAnalyses, boolean emailNotifications, boolean linkedinIntegration,
                          boolean prioritySupport, boolean advancedAnalytics) {
            this.monthlyApplications = monthlyApplications;
            this.resumeVersions = resumeVersions;
            this.interviewSessions = interviewSessions;
            this.aiAnalyses = aiAnalyses;
            this.emailNotifications = emailNotifications;
            this.linkedinIntegration = linkedinIntegration;
            this.prioritySupport = prioritySupport;
            this.advancedAnalytics = advancedAnalytics;
        }

        // Getters and setters
        public Integer getMonthlyApplications() { return monthlyApplications; }
        public void setMonthlyApplications(Integer monthlyApplications) { this.monthlyApplications = monthlyApplications; }
        public Integer getResumeVersions() { return resumeVersions; }
        public void setResumeVersions(Integer resumeVersions) { this.resumeVersions = resumeVersions; }
        public Integer getInterviewSessions() { return interviewSessions; }
        public void setInterviewSessions(Integer interviewSessions) { this.interviewSessions = interviewSessions; }
        public Integer getAiAnalyses() { return aiAnalyses; }
        public void setAiAnalyses(Integer aiAnalyses) { this.aiAnalyses = aiAnalyses; }
        public boolean isEmailNotifications() { return emailNotifications; }
        public void setEmailNotifications(boolean emailNotifications) { this.emailNotifications = emailNotifications; }
        public boolean isLinkedinIntegration() { return linkedinIntegration; }
        public void setLinkedinIntegration(boolean linkedinIntegration) { this.linkedinIntegration = linkedinIntegration; }
        public boolean isPrioritySupport() { return prioritySupport; }
        public void setPrioritySupport(boolean prioritySupport) { this.prioritySupport = prioritySupport; }
        public boolean isAdvancedAnalytics() { return advancedAnalytics; }
        public void setAdvancedAnalytics(boolean advancedAnalytics) { this.advancedAnalytics = advancedAnalytics; }
    }

    public static class UsageTracking {
        private Integer currentMonthApplications;
        private Integer currentMonthResumeVersions;
        private Integer currentMonthInterviewSessions;
        private Integer currentMonthAiAnalyses;
        private LocalDateTime lastResetDate;

        public UsageTracking() {
            this.currentMonthApplications = 0;
            this.currentMonthResumeVersions = 0;
            this.currentMonthInterviewSessions = 0;
            this.currentMonthAiAnalyses = 0;
            this.lastResetDate = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        }

        public void resetMonthlyUsage() {
            this.currentMonthApplications = 0;
            this.currentMonthResumeVersions = 0;
            this.currentMonthInterviewSessions = 0;
            this.currentMonthAiAnalyses = 0;
            this.lastResetDate = LocalDateTime.now();
        }

        // Getters and setters
        public Integer getCurrentMonthApplications() { return currentMonthApplications; }
        public void setCurrentMonthApplications(Integer currentMonthApplications) { this.currentMonthApplications = currentMonthApplications; }
        public Integer getCurrentMonthResumeVersions() { return currentMonthResumeVersions; }
        public void setCurrentMonthResumeVersions(Integer currentMonthResumeVersions) { this.currentMonthResumeVersions = currentMonthResumeVersions; }
        public Integer getCurrentMonthInterviewSessions() { return currentMonthInterviewSessions; }
        public void setCurrentMonthInterviewSessions(Integer currentMonthInterviewSessions) { this.currentMonthInterviewSessions = currentMonthInterviewSessions; }
        public Integer getCurrentMonthAiAnalyses() { return currentMonthAiAnalyses; }
        public void setCurrentMonthAiAnalyses(Integer currentMonthAiAnalyses) { this.currentMonthAiAnalyses = currentMonthAiAnalyses; }
        public LocalDateTime getLastResetDate() { return lastResetDate; }
        public void setLastResetDate(LocalDateTime lastResetDate) { this.lastResetDate = lastResetDate; }
    }

    public static class PaymentRecord {
        private String paymentId;
        private BigDecimal amount;
        private String currency;
        private String status;
        private LocalDateTime paidAt;
        private String invoiceUrl;
        private String description;

        public PaymentRecord() {}

        public PaymentRecord(String paymentId, BigDecimal amount, String currency, String status, LocalDateTime paidAt) {
            this.paymentId = paymentId;
            this.amount = amount;
            this.currency = currency;
            this.status = status;
            this.paidAt = paidAt;
        }

        // Getters and setters
        public String getPaymentId() { return paymentId; }
        public void setPaymentId(String paymentId) { this.paymentId = paymentId; }
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public String getCurrency() { return currency; }
        public void setCurrency(String currency) { this.currency = currency; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public LocalDateTime getPaidAt() { return paidAt; }
        public void setPaidAt(LocalDateTime paidAt) { this.paidAt = paidAt; }
        public String getInvoiceUrl() { return invoiceUrl; }
        public void setInvoiceUrl(String invoiceUrl) { this.invoiceUrl = invoiceUrl; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }

    // Helper methods
    public boolean isActive() {
        return status == SubscriptionStatus.ACTIVE || status == SubscriptionStatus.TRIALING;
    }

    public boolean isTrialExpired() {
        return trialEnd != null && LocalDateTime.now().isAfter(trialEnd);
    }

    public boolean isPeriodExpired() {
        return currentPeriodEnd != null && LocalDateTime.now().isAfter(currentPeriodEnd);
    }

    public boolean canUseFeature(String featureType) {
        if (!isActive() && plan != SubscriptionPlan.FREE) {
            return false;
        }

        return switch (featureType.toLowerCase()) {
            case "linkedin" -> usageLimits.isLinkedinIntegration();
            case "email" -> usageLimits.isEmailNotifications();
            case "priority_support" -> usageLimits.isPrioritySupport();
            case "advanced_analytics" -> usageLimits.isAdvancedAnalytics();
            default -> true;
        };
    }

    public boolean hasUsageRemaining(String usageType) {
        if (!isActive() && plan != SubscriptionPlan.FREE) {
            return false;
        }

        return switch (usageType.toLowerCase()) {
            case "applications" -> usageLimits.getMonthlyApplications() == -1 || 
                    usageTracking.getCurrentMonthApplications() < usageLimits.getMonthlyApplications();
            case "resumes" -> usageLimits.getResumeVersions() == -1 || 
                    usageTracking.getCurrentMonthResumeVersions() < usageLimits.getResumeVersions();
            case "interviews" -> usageLimits.getInterviewSessions() == -1 || 
                    usageTracking.getCurrentMonthInterviewSessions() < usageLimits.getInterviewSessions();
            case "ai" -> usageLimits.getAiAnalyses() == -1 || 
                    usageTracking.getCurrentMonthAiAnalyses() < usageLimits.getAiAnalyses();
            default -> true;
        };
    }

    public void incrementUsage(String usageType) {
        switch (usageType.toLowerCase()) {
            case "applications" -> usageTracking.setCurrentMonthApplications(
                    usageTracking.getCurrentMonthApplications() + 1);
            case "resumes" -> usageTracking.setCurrentMonthResumeVersions(
                    usageTracking.getCurrentMonthResumeVersions() + 1);
            case "interviews" -> usageTracking.setCurrentMonthInterviewSessions(
                    usageTracking.getCurrentMonthInterviewSessions() + 1);
            case "ai" -> usageTracking.setCurrentMonthAiAnalyses(
                    usageTracking.getCurrentMonthAiAnalyses() + 1);
        }
        updateTimestamp();
    }

    public void updateTimestamp() {
        this.updatedAt = LocalDateTime.now();
    }

    private UsageLimits getDefaultUsageLimits(SubscriptionPlan plan) {
        return switch (plan) {
            case FREE -> new UsageLimits(5, 1, 0, 0, false, false, false, false);
            case BASIC -> new UsageLimits(50, 3, 10, 5, true, false, false, false);
            case PRO -> new UsageLimits(-1, -1, -1, -1, true, true, true, true); // -1 means unlimited
            case ENTERPRISE -> new UsageLimits(-1, -1, -1, -1, true, true, true, true);
        };
    }

    // Main getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    public SubscriptionPlan getPlan() { return plan; }
    public void setPlan(SubscriptionPlan plan) { this.plan = plan; }
    public SubscriptionStatus getStatus() { return status; }
    public void setStatus(SubscriptionStatus status) { this.status = status; }
    public String getStripeCustomerId() { return stripeCustomerId; }
    public void setStripeCustomerId(String stripeCustomerId) { this.stripeCustomerId = stripeCustomerId; }
    public String getStripeSubscriptionId() { return stripeSubscriptionId; }
    public void setStripeSubscriptionId(String stripeSubscriptionId) { this.stripeSubscriptionId = stripeSubscriptionId; }
    public String getPaymentMethodId() { return paymentMethodId; }
    public void setPaymentMethodId(String paymentMethodId) { this.paymentMethodId = paymentMethodId; }
    public BigDecimal getAmount() { return amount; }
    public void setAmount(BigDecimal amount) { this.amount = amount; }
    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }
    public BillingCycle getBillingCycle() { return billingCycle; }
    public void setBillingCycle(BillingCycle billingCycle) { this.billingCycle = billingCycle; }
    public LocalDateTime getCurrentPeriodStart() { return currentPeriodStart; }
    public void setCurrentPeriodStart(LocalDateTime currentPeriodStart) { this.currentPeriodStart = currentPeriodStart; }
    public LocalDateTime getCurrentPeriodEnd() { return currentPeriodEnd; }
    public void setCurrentPeriodEnd(LocalDateTime currentPeriodEnd) { this.currentPeriodEnd = currentPeriodEnd; }
    public LocalDateTime getTrialStart() { return trialStart; }
    public void setTrialStart(LocalDateTime trialStart) { this.trialStart = trialStart; }
    public LocalDateTime getTrialEnd() { return trialEnd; }
    public void setTrialEnd(LocalDateTime trialEnd) { this.trialEnd = trialEnd; }
    public boolean isTrialActive() { return isTrialActive; }
    public void setTrialActive(boolean trialActive) { isTrialActive = trialActive; }
    public LocalDateTime getCanceledAt() { return canceledAt; }
    public void setCanceledAt(LocalDateTime canceledAt) { this.canceledAt = canceledAt; }
    public String getCancellationReason() { return cancellationReason; }
    public void setCancellationReason(String cancellationReason) { this.cancellationReason = cancellationReason; }
    public boolean isCancelAtPeriodEnd() { return cancelAtPeriodEnd; }
    public void setCancelAtPeriodEnd(boolean cancelAtPeriodEnd) { this.cancelAtPeriodEnd = cancelAtPeriodEnd; }
    public UsageLimits getUsageLimits() { return usageLimits; }
    public void setUsageLimits(UsageLimits usageLimits) { this.usageLimits = usageLimits; }
    public UsageTracking getUsageTracking() { return usageTracking; }
    public void setUsageTracking(UsageTracking usageTracking) { this.usageTracking = usageTracking; }
    public List<PaymentRecord> getPaymentHistory() { return paymentHistory; }
    public void setPaymentHistory(List<PaymentRecord> paymentHistory) { this.paymentHistory = paymentHistory; }
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
