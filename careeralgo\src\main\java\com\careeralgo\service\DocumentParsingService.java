package com.careeralgo.service;

import com.careeralgo.model.*;
import com.careeralgo.repository.ResumeRepository;
import org.apache.tika.Tika;
import org.apache.tika.exception.TikaException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service for parsing resume documents and extracting structured content
 */
@Service
public class DocumentParsingService {

    private static final Logger logger = LoggerFactory.getLogger(DocumentParsingService.class);

    @Autowired
    private ResumeRepository resumeRepository;

    @Autowired
    private AIResumeAnalysisService aiResumeAnalysisService;

    private final Tika tika = new Tika();

    // Regex patterns for parsing
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b");
    private static final Pattern PHONE_PATTERN = Pattern.compile(
            "\\b(?:\\+?1[-.]?)?\\(?([0-9]{3})\\)?[-.]?([0-9]{3})[-.]?([0-9]{4})\\b");
    private static final Pattern LINKEDIN_PATTERN = Pattern.compile(
            "(?:https?://)?(?:www\\.)?linkedin\\.com/in/[\\w-]+/?", Pattern.CASE_INSENSITIVE);
    private static final Pattern GITHUB_PATTERN = Pattern.compile(
            "(?:https?://)?(?:www\\.)?github\\.com/[\\w-]+/?", Pattern.CASE_INSENSITIVE);
    private static final Pattern URL_PATTERN = Pattern.compile(
            "https?://[\\w.-]+(?:\\.[\\w.-]+)+[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=.]+");

    /**
     * Parse resume asynchronously
     */
    @Async
    public void parseResumeAsync(String resumeId) {
        try {
            Optional<Resume> resumeOpt = resumeRepository.findById(resumeId);
            if (resumeOpt.isEmpty()) {
                logger.warn("Resume not found for parsing: {}", resumeId);
                return;
            }

            Resume resume = resumeOpt.get();
            ParsedContent parsedContent = parseResumeContent(resume);

            resume.setParsedContent(parsedContent);
            resumeRepository.save(resume);

            // Trigger AI analysis after parsing
            aiResumeAnalysisService.analyzeResumeAsync(resumeId);

            logger.info("Successfully parsed resume: {}", resumeId);

        } catch (Exception e) {
            logger.error("Error parsing resume: {}", resumeId, e);
        }
    }

    /**
     * Parse resume content from Cloudinary URL
     */
    public ParsedContent parseResumeContent(Resume resume) throws IOException, TikaException {
        String cloudinaryUrl = resume.getCloudinaryUrl();
        if (cloudinaryUrl == null) {
            throw new IllegalArgumentException("Resume has no Cloudinary URL");
        }

        // Download and parse document
        try (InputStream inputStream = new URL(cloudinaryUrl).openStream()) {
            String rawText = tika.parseToString(inputStream);
            return parseTextContent(rawText);
        }
    }

    /**
     * Parse structured content from raw text
     */
    public ParsedContent parseTextContent(String rawText) {
        ParsedContent content = new ParsedContent();
        content.setRawText(rawText);

        // Parse personal information
        content.setPersonalInfo(extractPersonalInfo(rawText));

        // Parse sections
        content.setProfessionalSummary(extractProfessionalSummary(rawText));
        content.setWorkExperience(extractWorkExperience(rawText));
        content.setEducation(extractEducation(rawText));
        content.setSkills(extractSkills(rawText));
        content.setCertifications(extractCertifications(rawText));
        content.setProjects(extractProjects(rawText));
        content.setLanguages(extractLanguages(rawText));

        return content;
    }

    /**
     * Extract personal information from text
     */
    private ParsedContent.PersonalInfo extractPersonalInfo(String text) {
        ParsedContent.PersonalInfo personalInfo = new ParsedContent.PersonalInfo();

        // Extract email
        Matcher emailMatcher = EMAIL_PATTERN.matcher(text);
        if (emailMatcher.find()) {
            personalInfo.setEmail(emailMatcher.group());
        }

        // Extract phone
        Matcher phoneMatcher = PHONE_PATTERN.matcher(text);
        if (phoneMatcher.find()) {
            personalInfo.setPhone(phoneMatcher.group());
        }

        // Extract LinkedIn
        Matcher linkedinMatcher = LINKEDIN_PATTERN.matcher(text);
        if (linkedinMatcher.find()) {
            personalInfo.setLinkedinUrl(linkedinMatcher.group());
        }

        // Extract GitHub
        Matcher githubMatcher = GITHUB_PATTERN.matcher(text);
        if (githubMatcher.find()) {
            personalInfo.setGithubUrl(githubMatcher.group());
        }

        // Extract name (first few lines, excluding email/phone)
        String[] lines = text.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (!line.isEmpty() && !EMAIL_PATTERN.matcher(line).find() &&
                !PHONE_PATTERN.matcher(line).find() && line.length() > 2 && line.length() < 50) {
                personalInfo.setFullName(line);
                break;
            }
        }

        return personalInfo;
    }

    /**
     * Extract professional summary
     */
    private String extractProfessionalSummary(String text) {
        String[] summaryKeywords = {"summary", "profile", "objective", "about"};

        for (String keyword : summaryKeywords) {
            Pattern pattern = Pattern.compile(
                    "(?i)" + keyword + "\\s*:?\\s*\\n?([\\s\\S]{50,500}?)(?=\\n\\s*[A-Z]|$)",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);
            Matcher matcher = pattern.matcher(text);
            if (matcher.find()) {
                return matcher.group(1).trim();
            }
        }

        return null;
    }

    /**
     * Extract work experience
     */
    private List<WorkExperience> extractWorkExperience(String text) {
        List<WorkExperience> experiences = new ArrayList<>();

        // Look for experience section
        Pattern experiencePattern = Pattern.compile(
                "(?i)(experience|employment|work history)\\s*:?\\s*\\n([\\s\\S]+?)(?=\\n\\s*(?:education|skills|projects|certifications)|$)",
                Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);

        Matcher matcher = experiencePattern.matcher(text);
        if (matcher.find()) {
            String experienceSection = matcher.group(2);

            // Parse individual experiences
            String[] experienceBlocks = experienceSection.split("\\n\\s*\\n");
            for (String block : experienceBlocks) {
                WorkExperience experience = parseWorkExperience(block.trim());
                if (experience != null) {
                    experiences.add(experience);
                }
            }
        }

        return experiences;
    }

    /**
     * Parse individual work experience
     */
    private WorkExperience parseWorkExperience(String block) {
        if (block.length() < 20) return null;

        WorkExperience experience = new WorkExperience();
        String[] lines = block.split("\n");

        // First line usually contains position and company
        if (lines.length > 0) {
            String firstLine = lines[0].trim();
            if (firstLine.contains(" at ") || firstLine.contains(" - ")) {
                String[] parts = firstLine.split(" at | - ");
                if (parts.length >= 2) {
                    experience.setPosition(parts[0].trim());
                    experience.setCompany(parts[1].trim());
                }
            }
        }

        // Look for dates
        for (String line : lines) {
            LocalDate[] dates = extractDates(line);
            if (dates.length >= 1) {
                experience.setStartDate(dates[0]);
                if (dates.length >= 2) {
                    experience.setEndDate(dates[1]);
                }
                break;
            }
        }

        // Set description as the remaining text
        if (lines.length > 2) {
            StringBuilder description = new StringBuilder();
            for (int i = 2; i < lines.length; i++) {
                description.append(lines[i]).append("\n");
            }
            experience.setDescription(description.toString().trim());
        }

        return experience.getPosition() != null ? experience : null;
    }

    /**
     * Extract education information
     */
    private List<Education> extractEducation(String text) {
        List<Education> educationList = new ArrayList<>();

        Pattern educationPattern = Pattern.compile(
                "(?i)(education|academic)\\s*:?\\s*\\n([\\s\\S]+?)(?=\\n\\s*(?:experience|skills|projects|certifications)|$)",
                Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);

        Matcher matcher = educationPattern.matcher(text);
        if (matcher.find()) {
            String educationSection = matcher.group(2);

            String[] educationBlocks = educationSection.split("\\n\\s*\\n");
            for (String block : educationBlocks) {
                Education education = parseEducation(block.trim());
                if (education != null) {
                    educationList.add(education);
                }
            }
        }

        return educationList;
    }

    /**
     * Parse individual education entry
     */
    private Education parseEducation(String block) {
        if (block.length() < 10) return null;

        Education education = new Education();
        String[] lines = block.split("\n");

        // Look for degree and institution
        for (String line : lines) {
            if (line.toLowerCase().contains("university") ||
                line.toLowerCase().contains("college") ||
                line.toLowerCase().contains("institute")) {
                education.setInstitution(line.trim());
            } else if (line.toLowerCase().contains("bachelor") ||
                      line.toLowerCase().contains("master") ||
                      line.toLowerCase().contains("phd") ||
                      line.toLowerCase().contains("degree")) {
                education.setDegree(line.trim());
            }
        }

        // Extract dates
        for (String line : lines) {
            LocalDate[] dates = extractDates(line);
            if (dates.length >= 1) {
                education.setStartDate(dates[0]);
                if (dates.length >= 2) {
                    education.setEndDate(dates[1]);
                }
                break;
            }
        }

        return education.getInstitution() != null || education.getDegree() != null ? education : null;
    }

    /**
     * Extract skills
     */
    private ParsedContent.Skills extractSkills(String text) {
        ParsedContent.Skills skills = new ParsedContent.Skills();

        Pattern skillsPattern = Pattern.compile(
                "(?i)(skills|technologies|technical skills)\\s*:?\\s*\\n([\\s\\S]+?)(?=\\n\\s*[A-Z]|$)",
                Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);

        Matcher matcher = skillsPattern.matcher(text);
        if (matcher.find()) {
            String skillsSection = matcher.group(2);

            // Split by common delimiters
            String[] skillArray = skillsSection.split("[,;\\n•·]");
            List<String> technicalSkills = new ArrayList<>();

            for (String skill : skillArray) {
                skill = skill.trim();
                if (skill.length() > 1 && skill.length() < 50) {
                    technicalSkills.add(skill);
                }
            }

            skills.setTechnical(technicalSkills);
        }

        return skills;
    }

    /**
     * Extract certifications
     */
    private List<Certification> extractCertifications(String text) {
        List<Certification> certifications = new ArrayList<>();

        Pattern certPattern = Pattern.compile(
                "(?i)(certifications?|certificates?)\\s*:?\\s*\\n([\\s\\S]+?)(?=\\n\\s*[A-Z]|$)",
                Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);

        Matcher matcher = certPattern.matcher(text);
        if (matcher.find()) {
            String certSection = matcher.group(2);
            String[] certLines = certSection.split("\n");

            for (String line : certLines) {
                line = line.trim();
                if (line.length() > 5) {
                    Certification cert = new Certification();
                    cert.setName(line);
                    certifications.add(cert);
                }
            }
        }

        return certifications;
    }

    /**
     * Extract projects
     */
    private List<Project> extractProjects(String text) {
        List<Project> projects = new ArrayList<>();

        Pattern projectPattern = Pattern.compile(
                "(?i)(projects?)\\s*:?\\s*\\n([\\s\\S]+?)(?=\\n\\s*[A-Z]|$)",
                Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);

        Matcher matcher = projectPattern.matcher(text);
        if (matcher.find()) {
            String projectSection = matcher.group(2);
            String[] projectBlocks = projectSection.split("\\n\\s*\\n");

            for (String block : projectBlocks) {
                if (block.trim().length() > 10) {
                    Project project = new Project();
                    String[] lines = block.split("\n");
                    if (lines.length > 0) {
                        project.setName(lines[0].trim());
                        if (lines.length > 1) {
                            project.setDescription(String.join("\n",
                                    Arrays.copyOfRange(lines, 1, lines.length)).trim());
                        }
                    }
                    projects.add(project);
                }
            }
        }

        return projects;
    }

    /**
     * Extract languages
     */
    private List<Language> extractLanguages(String text) {
        List<Language> languages = new ArrayList<>();

        Pattern langPattern = Pattern.compile(
                "(?i)(languages?)\\s*:?\\s*\\n([\\s\\S]+?)(?=\\n\\s*[A-Z]|$)",
                Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);

        Matcher matcher = langPattern.matcher(text);
        if (matcher.find()) {
            String langSection = matcher.group(2);
            String[] langLines = langSection.split("\n");

            for (String line : langLines) {
                line = line.trim();
                if (line.length() > 2) {
                    Language language = new Language();
                    language.setLanguage(line);
                    language.setProficiency(Language.Proficiency.CONVERSATIONAL); // Default
                    languages.add(language);
                }
            }
        }

        return languages;
    }

    /**
     * Extract dates from text
     */
    private LocalDate[] extractDates(String text) {
        List<LocalDate> dates = new ArrayList<>();

        // Common date patterns
        String[] patterns = {
                "\\b(\\d{1,2})/(\\d{1,2})/(\\d{4})\\b",
                "\\b(\\d{4})-(\\d{1,2})-(\\d{1,2})\\b",
                "\\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s+(\\d{4})\\b",
                "\\b(\\d{4})\\b"
        };

        for (String patternStr : patterns) {
            Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(text);

            while (matcher.find() && dates.size() < 2) {
                try {
                    LocalDate date = parseDate(matcher.group());
                    if (date != null) {
                        dates.add(date);
                    }
                } catch (Exception e) {
                    // Ignore parsing errors
                }
            }
        }

        return dates.toArray(new LocalDate[0]);
    }

    /**
     * Parse date string to LocalDate
     */
    private LocalDate parseDate(String dateStr) {
        try {
            // Try different date formats
            DateTimeFormatter[] formatters = {
                    DateTimeFormatter.ofPattern("M/d/yyyy"),
                    DateTimeFormatter.ofPattern("yyyy-M-d"),
                    DateTimeFormatter.ofPattern("MMM yyyy"),
                    DateTimeFormatter.ofPattern("yyyy")
            };

            for (DateTimeFormatter formatter : formatters) {
                try {
                    return LocalDate.parse(dateStr, formatter);
                } catch (DateTimeParseException e) {
                    // Try next formatter
                }
            }

            // If only year is provided
            if (dateStr.matches("\\d{4}")) {
                return LocalDate.of(Integer.parseInt(dateStr), 1, 1);
            }

        } catch (Exception e) {
            logger.debug("Failed to parse date: {}", dateStr);
        }

        return null;
    }
}


