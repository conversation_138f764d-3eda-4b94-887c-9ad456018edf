package com.careeralgo.model;

/**
 * Salary information for jobs
 */
public class Salary {
    
    private Integer min;
    private Integer max;
    private String currency = "USD";
    private SalaryPeriod period = SalaryPeriod.ANNUAL;
    private String equity;
    private String bonus;

    public enum SalaryPeriod {
        HOURLY, DAILY, WEEKLY, MONTHLY, ANNUAL
    }

    // Constructors
    public Salary() {}

    public Salary(Integer min, Integer max, String currency, SalaryPeriod period) {
        this.min = min;
        this.max = max;
        this.currency = currency;
        this.period = period;
    }

    // Getters and Setters
    public Integer getMin() {
        return min;
    }

    public void setMin(Integer min) {
        this.min = min;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public SalaryPeriod getPeriod() {
        return period;
    }

    public void setPeriod(SalaryPeriod period) {
        this.period = period;
    }

    public String getEquity() {
        return equity;
    }

    public void setEquity(String equity) {
        this.equity = equity;
    }

    public String getBonus() {
        return bonus;
    }

    public void setBonus(String bonus) {
        this.bonus = bonus;
    }

    public String getFormattedRange() {
        if (min == null && max == null) {
            return "Salary not disclosed";
        }
        
        String periodSuffix = getPeriodSuffix();
        
        if (min == null) {
            return "Up to " + formatAmount(max) + " " + currency + periodSuffix;
        }
        if (max == null) {
            return "From " + formatAmount(min) + " " + currency + periodSuffix;
        }
        return formatAmount(min) + " - " + formatAmount(max) + " " + currency + periodSuffix;
    }

    public String getFullCompensation() {
        StringBuilder sb = new StringBuilder(getFormattedRange());
        
        if (equity != null && !equity.isEmpty()) {
            sb.append(" + ").append(equity).append(" equity");
        }
        
        if (bonus != null && !bonus.isEmpty()) {
            sb.append(" + ").append(bonus);
        }
        
        return sb.toString();
    }

    private String formatAmount(Integer amount) {
        if (amount == null) return "0";
        return String.format("%,d", amount);
    }

    private String getPeriodSuffix() {
        switch (period) {
            case HOURLY: return "/hr";
            case DAILY: return "/day";
            case WEEKLY: return "/week";
            case MONTHLY: return "/month";
            case ANNUAL: return "/year";
            default: return "";
        }
    }

    public Integer getMidpoint() {
        if (min == null && max == null) return null;
        if (min == null) return max;
        if (max == null) return min;
        return (min + max) / 2;
    }

    @Override
    public String toString() {
        return getFormattedRange();
    }
}
