package com.careeralgo.model;

import java.time.LocalDateTime;

/**
 * User metadata embedded in User document
 */
public class Metadata {
    
    private LocalDateTime lastLoginAt;
    private Integer loginCount = 0;
    private Integer profileCompleteness = 0;
    private boolean onboardingCompleted = false;
    private String referralCode;
    private String referredBy;

    // Constructors
    public Metadata() {}

    // Getters and Setters
    public LocalDateTime getLastLoginAt() {
        return lastLoginAt;
    }

    public void setLastLoginAt(LocalDateTime lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
    }

    public Integer getLoginCount() {
        return loginCount;
    }

    public void setLoginCount(Integer loginCount) {
        this.loginCount = loginCount;
    }

    public Integer getProfileCompleteness() {
        return profileCompleteness;
    }

    public void setProfileCompleteness(Integer profileCompleteness) {
        this.profileCompleteness = profileCompleteness;
    }

    public boolean isOnboardingCompleted() {
        return onboardingCompleted;
    }

    public void setOnboardingCompleted(boolean onboardingCompleted) {
        this.onboardingCompleted = onboardingCompleted;
    }

    public String getReferralCode() {
        return referralCode;
    }

    public void setReferralCode(String referralCode) {
        this.referralCode = referralCode;
    }

    public String getReferredBy() {
        return referredBy;
    }

    public void setReferredBy(String referredBy) {
        this.referredBy = referredBy;
    }

    public void incrementLoginCount() {
        this.loginCount++;
        this.lastLoginAt = LocalDateTime.now();
    }
}
