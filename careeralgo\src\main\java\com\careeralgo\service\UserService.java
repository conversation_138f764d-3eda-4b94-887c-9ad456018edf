package com.careeralgo.service;

import com.careeralgo.dto.UserProfileUpdateRequest;
import com.careeralgo.dto.UserResponse;
import com.careeralgo.exception.ResourceNotFoundException;
import com.careeralgo.model.*;
import com.careeralgo.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service for user management operations
 */
@Service
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserRepository userRepository;

    /**
     * Get current user from authentication context
     */
    public UserResponse getCurrentUser(Authentication authentication) {
        String clerkUserId = extractClerkUserId(authentication);
        User user = findUserByClerkId(clerkUserId);
        return new UserResponse(user);
    }

    /**
     * Update user profile
     */
    public UserResponse updateUserProfile(Authentication authentication, UserProfileUpdateRequest request) {
        String clerkUserId = extractClerkUserId(authentication);
        User user = findUserByClerkId(clerkUserId);

        // Update profile information
        Profile profile = user.getProfile();
        if (profile == null) {
            profile = new Profile();
            user.setProfile(profile);
        }

        updateProfileFromRequest(profile, request);

        // Calculate and update profile completeness
        updateProfileCompleteness(user);

        // Save updated user
        User savedUser = userRepository.save(user);
        logger.info("Updated profile for user: {}", user.getEmail());

        return new UserResponse(savedUser);
    }

    /**
     * Update user preferences
     */
    public UserResponse updateUserPreferences(Authentication authentication, Preferences preferences) {
        String clerkUserId = extractClerkUserId(authentication);
        User user = findUserByClerkId(clerkUserId);

        user.setPreferences(preferences);
        User savedUser = userRepository.save(user);
        logger.info("Updated preferences for user: {}", user.getEmail());

        return new UserResponse(savedUser);
    }

    /**
     * Get user statistics
     */
    public UserStatsResponse getUserStats(Authentication authentication) {
        String clerkUserId = extractClerkUserId(authentication);
        User user = findUserByClerkId(clerkUserId);

        // TODO: Implement user statistics calculation
        // This would involve aggregating data from resumes, applications, etc.
        UserStatsResponse stats = new UserStatsResponse();
        stats.setUserId(user.getId());
        stats.setProfileCompleteness(user.getMetadata().getProfileCompleteness());
        stats.setLoginCount(user.getMetadata().getLoginCount());
        
        return stats;
    }

    /**
     * Deactivate user account
     */
    public void deactivateUser(Authentication authentication) {
        String clerkUserId = extractClerkUserId(authentication);
        User user = findUserByClerkId(clerkUserId);

        user.setActive(false);
        userRepository.save(user);
        logger.info("Deactivated user account: {}", user.getEmail());
    }

    /**
     * Update profile picture
     */
    public UserResponse updateProfilePicture(Authentication authentication, String profilePictureUrl) {
        String clerkUserId = extractClerkUserId(authentication);
        User user = findUserByClerkId(clerkUserId);

        user.setProfilePicture(profilePictureUrl);
        User savedUser = userRepository.save(user);
        logger.info("Updated profile picture for user: {}", user.getEmail());

        return new UserResponse(savedUser);
    }

    /**
     * Remove profile picture
     */
    public UserResponse removeProfilePicture(Authentication authentication) {
        String clerkUserId = extractClerkUserId(authentication);
        User user = findUserByClerkId(clerkUserId);

        user.setProfilePicture(null);
        User savedUser = userRepository.save(user);
        logger.info("Removed profile picture for user: {}", user.getEmail());

        return new UserResponse(savedUser);
    }

    /**
     * Find user by Clerk user ID
     */
    private User findUserByClerkId(String clerkUserId) {
        return userRepository.findByClerkUserId(clerkUserId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with Clerk ID: " + clerkUserId));
    }

    /**
     * Extract Clerk user ID from JWT token
     */
    private String extractClerkUserId(Authentication authentication) {
        if (authentication.getPrincipal() instanceof Jwt jwt) {
            return jwt.getSubject(); // Clerk user ID is typically in the 'sub' claim
        }
        throw new IllegalArgumentException("Invalid authentication type");
    }

    /**
     * Update profile from request DTO
     */
    private void updateProfileFromRequest(Profile profile, UserProfileUpdateRequest request) {
        if (request.getJobTitle() != null) {
            profile.setJobTitle(request.getJobTitle());
        }
        if (request.getIndustry() != null) {
            profile.setIndustry(request.getIndustry());
        }
        if (request.getExperienceLevel() != null) {
            profile.setExperienceLevel(request.getExperienceLevel());
        }
        if (request.getLocation() != null) {
            Location location = new Location(
                    request.getLocation().getCity(),
                    request.getLocation().getState(),
                    request.getLocation().getCountry()
            );
            profile.setLocation(location);
        }
        if (request.getRemotePreference() != null) {
            profile.setRemotePreference(request.getRemotePreference());
        }
        if (request.getSalaryExpectation() != null) {
            SalaryExpectation salary = new SalaryExpectation(
                    request.getSalaryExpectation().getMin(),
                    request.getSalaryExpectation().getMax(),
                    request.getSalaryExpectation().getCurrency()
            );
            profile.setSalaryExpectation(salary);
        }
        if (request.getSkills() != null) {
            profile.setSkills(request.getSkills());
        }
        if (request.getLinkedinProfile() != null) {
            profile.setLinkedinProfile(request.getLinkedinProfile());
        }
        if (request.getGithubProfile() != null) {
            profile.setGithubProfile(request.getGithubProfile());
        }
        if (request.getPersonalWebsite() != null) {
            profile.setPersonalWebsite(request.getPersonalWebsite());
        }
        if (request.getBio() != null) {
            profile.setBio(request.getBio());
        }
        if (request.getPhoneNumber() != null) {
            profile.setPhoneNumber(request.getPhoneNumber());
        }
    }

    /**
     * Calculate and update profile completeness percentage
     */
    private void updateProfileCompleteness(User user) {
        Profile profile = user.getProfile();
        if (profile == null) {
            user.getMetadata().setProfileCompleteness(0);
            return;
        }

        int totalFields = 12; // Total number of profile fields
        int completedFields = 0;

        if (profile.getJobTitle() != null && !profile.getJobTitle().isEmpty()) completedFields++;
        if (profile.getIndustry() != null && !profile.getIndustry().isEmpty()) completedFields++;
        if (profile.getExperienceLevel() != null) completedFields++;
        if (profile.getLocation() != null && profile.getLocation().getCity() != null) completedFields++;
        if (profile.getRemotePreference() != null) completedFields++;
        if (profile.getSalaryExpectation() != null) completedFields++;
        if (profile.getSkills() != null && !profile.getSkills().isEmpty()) completedFields++;
        if (profile.getLinkedinProfile() != null && !profile.getLinkedinProfile().isEmpty()) completedFields++;
        if (profile.getGithubProfile() != null && !profile.getGithubProfile().isEmpty()) completedFields++;
        if (profile.getPersonalWebsite() != null && !profile.getPersonalWebsite().isEmpty()) completedFields++;
        if (profile.getBio() != null && !profile.getBio().isEmpty()) completedFields++;
        if (profile.getPhoneNumber() != null && !profile.getPhoneNumber().isEmpty()) completedFields++;

        int completeness = (completedFields * 100) / totalFields;
        user.getMetadata().setProfileCompleteness(completeness);
    }

    /**
     * User statistics response DTO
     */
    public static class UserStatsResponse {
        private String userId;
        private Integer profileCompleteness;
        private Integer loginCount;
        private Integer resumeCount;
        private Integer applicationCount;
        private Integer interviewCount;

        // Getters and Setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public Integer getProfileCompleteness() {
            return profileCompleteness;
        }

        public void setProfileCompleteness(Integer profileCompleteness) {
            this.profileCompleteness = profileCompleteness;
        }

        public Integer getLoginCount() {
            return loginCount;
        }

        public void setLoginCount(Integer loginCount) {
            this.loginCount = loginCount;
        }

        public Integer getResumeCount() {
            return resumeCount;
        }

        public void setResumeCount(Integer resumeCount) {
            this.resumeCount = resumeCount;
        }

        public Integer getApplicationCount() {
            return applicationCount;
        }

        public void setApplicationCount(Integer applicationCount) {
            this.applicationCount = applicationCount;
        }

        public Integer getInterviewCount() {
            return interviewCount;
        }

        public void setInterviewCount(Integer interviewCount) {
            this.interviewCount = interviewCount;
        }
    }
}
