package com.careeralgo.ai.orchestrator;

import java.util.List;
import java.util.Map;

/**
 * Request DTO for career assistance operations
 */
public class CareerAssistanceRequest {

    private RequestType requestType;
    private String userId;
    private String sessionId;

    // Resume Analysis fields
    private String resumeContent;
    private String targetRole;
    private String experienceLevel;

    // Job Matching fields
    private String candidateProfile;
    private String jobDescription;
    private String jobRequirements;

    // Interview Prep fields
    private String interviewAnswer;
    private String interviewQuestion;
    private String questionType;

    // Career Advice fields
    private String careerSituation;
    private String currentRole;
    private String careerGoals;
    private String timeframe;

    // Additional context
    private Map<String, Object> additionalContext;
    private List<String> preferences;

    public enum RequestType {
        RESUME_ANALYSIS,
        JOB_MATCHING,
        INTERVIEW_PREP,
        CAREER_ADVICE,
        COMPREHENSIVE_ANALYSIS
    }

    // Constructors
    public CareerAssistanceRequest() {}

    public CareerAssistanceRequest(RequestType requestType, String userId) {
        this.requestType = requestType;
        this.userId = userId;
    }

    // Static factory methods for different request types
    public static CareerAssistanceRequest resumeAnalysis(String userId, String resumeContent, String targetRole, String experienceLevel) {
        CareerAssistanceRequest request = new CareerAssistanceRequest(RequestType.RESUME_ANALYSIS, userId);
        request.setResumeContent(resumeContent);
        request.setTargetRole(targetRole);
        request.setExperienceLevel(experienceLevel);
        return request;
    }

    public static CareerAssistanceRequest jobMatching(String userId, String candidateProfile, String jobDescription, String jobRequirements) {
        CareerAssistanceRequest request = new CareerAssistanceRequest(RequestType.JOB_MATCHING, userId);
        request.setCandidateProfile(candidateProfile);
        request.setJobDescription(jobDescription);
        request.setJobRequirements(jobRequirements);
        return request;
    }

    public static CareerAssistanceRequest interviewPrep(String userId, String answer, String question, String questionType, String targetRole) {
        CareerAssistanceRequest request = new CareerAssistanceRequest(RequestType.INTERVIEW_PREP, userId);
        request.setInterviewAnswer(answer);
        request.setInterviewQuestion(question);
        request.setQuestionType(questionType);
        request.setTargetRole(targetRole);
        return request;
    }

    public static CareerAssistanceRequest careerAdvice(String userId, String careerSituation, String currentRole, String careerGoals, String timeframe) {
        CareerAssistanceRequest request = new CareerAssistanceRequest(RequestType.CAREER_ADVICE, userId);
        request.setCareerSituation(careerSituation);
        request.setCurrentRole(currentRole);
        request.setCareerGoals(careerGoals);
        request.setTimeframe(timeframe);
        return request;
    }

    public static CareerAssistanceRequest comprehensive(String userId, String resumeContent, String jobDescription, String careerGoals) {
        CareerAssistanceRequest request = new CareerAssistanceRequest(RequestType.COMPREHENSIVE_ANALYSIS, userId);
        request.setResumeContent(resumeContent);
        request.setJobDescription(jobDescription);
        request.setCareerGoals(careerGoals);
        // Set derived fields
        request.setCandidateProfile(resumeContent);
        request.setJobRequirements(jobDescription);
        request.setCareerSituation("Seeking comprehensive career analysis");
        return request;
    }

    // Getters and setters
    public RequestType getRequestType() {
        return requestType;
    }

    public void setRequestType(RequestType requestType) {
        this.requestType = requestType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getResumeContent() {
        return resumeContent;
    }

    public void setResumeContent(String resumeContent) {
        this.resumeContent = resumeContent;
    }

    public String getTargetRole() {
        return targetRole;
    }

    public void setTargetRole(String targetRole) {
        this.targetRole = targetRole;
    }

    public String getExperienceLevel() {
        return experienceLevel;
    }

    public void setExperienceLevel(String experienceLevel) {
        this.experienceLevel = experienceLevel;
    }

    public String getCandidateProfile() {
        return candidateProfile;
    }

    public void setCandidateProfile(String candidateProfile) {
        this.candidateProfile = candidateProfile;
    }

    public String getJobDescription() {
        return jobDescription;
    }

    public void setJobDescription(String jobDescription) {
        this.jobDescription = jobDescription;
    }

    public String getJobRequirements() {
        return jobRequirements;
    }

    public void setJobRequirements(String jobRequirements) {
        this.jobRequirements = jobRequirements;
    }

    public String getInterviewAnswer() {
        return interviewAnswer;
    }

    public void setInterviewAnswer(String interviewAnswer) {
        this.interviewAnswer = interviewAnswer;
    }

    public String getInterviewQuestion() {
        return interviewQuestion;
    }

    public void setInterviewQuestion(String interviewQuestion) {
        this.interviewQuestion = interviewQuestion;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public String getCareerSituation() {
        return careerSituation;
    }

    public void setCareerSituation(String careerSituation) {
        this.careerSituation = careerSituation;
    }

    public String getCurrentRole() {
        return currentRole;
    }

    public void setCurrentRole(String currentRole) {
        this.currentRole = currentRole;
    }

    public String getCareerGoals() {
        return careerGoals;
    }

    public void setCareerGoals(String careerGoals) {
        this.careerGoals = careerGoals;
    }

    public String getTimeframe() {
        return timeframe;
    }

    public void setTimeframe(String timeframe) {
        this.timeframe = timeframe;
    }

    public Map<String, Object> getAdditionalContext() {
        return additionalContext;
    }

    public void setAdditionalContext(Map<String, Object> additionalContext) {
        this.additionalContext = additionalContext;
    }

    public List<String> getPreferences() {
        return preferences;
    }

    public void setPreferences(List<String> preferences) {
        this.preferences = preferences;
    }
}
