package com.careeralgo.model;

import java.time.LocalDateTime;

/**
 * Clerk-specific metadata embedded in User document
 */
public class ClerkMetadata {
    
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime lastSignInAt;
    private boolean emailVerified = false;
    private boolean phoneVerified = false;

    // Constructors
    public ClerkMetadata() {}

    public ClerkMetadata(LocalDateTime createdAt, LocalDateTime updatedAt, LocalDateTime lastSignInAt) {
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.lastSignInAt = lastSignInAt;
    }

    // Getters and Setters
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public LocalDateTime getLastSignInAt() {
        return lastSignInAt;
    }

    public void setLastSignInAt(LocalDateTime lastSignInAt) {
        this.lastSignInAt = lastSignInAt;
    }

    public boolean isEmailVerified() {
        return emailVerified;
    }

    public void setEmailVerified(boolean emailVerified) {
        this.emailVerified = emailVerified;
    }

    public boolean isPhoneVerified() {
        return phoneVerified;
    }

    public void setPhoneVerified(boolean phoneVerified) {
        this.phoneVerified = phoneVerified;
    }
}
