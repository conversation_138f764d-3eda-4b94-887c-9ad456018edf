package com.careeralgo.controller;

import com.careeralgo.dto.ResumeResponse;
import com.careeralgo.dto.ResumeUploadRequest;
import com.careeralgo.service.ResumeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for resume management operations
 */
@RestController
@RequestMapping("/resumes")
@Tag(name = "Resume Management", description = "APIs for resume upload, parsing, and management")
public class ResumeController {

    @Autowired
    private ResumeService resumeService;

    /**
     * Get user's resumes with pagination
     */
    @GetMapping
    @Operation(summary = "Get user resumes", description = "Retrieve paginated list of user's resumes")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Resumes retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "400", description = "Invalid pagination parameters")
    })
    public ResponseEntity<Page<ResumeResponse>> getUserResumes(
            Authentication authentication,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size) {
        
        Page<ResumeResponse> resumes = resumeService.getUserResumes(authentication, page, size);
        return ResponseEntity.ok(resumes);
    }

    /**
     * Get specific resume by ID
     */
    @GetMapping("/{resumeId}")
    @Operation(summary = "Get resume by ID", description = "Retrieve a specific resume by its ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Resume retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    public ResponseEntity<ResumeResponse> getResumeById(
            Authentication authentication,
            @Parameter(description = "Resume ID") @PathVariable String resumeId) {
        
        ResumeResponse resume = resumeService.getResumeById(authentication, resumeId);
        return ResponseEntity.ok(resume);
    }

    /**
     * Upload new resume
     */
    @PostMapping
    @Operation(summary = "Upload resume", description = "Upload a new resume file")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Resume uploaded successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid file or request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "413", description = "File too large"),
            @ApiResponse(responseCode = "415", description = "Unsupported file type")
    })
    public ResponseEntity<ResumeResponse> uploadResume(
            Authentication authentication,
            @Parameter(description = "Resume file (PDF, DOC, DOCX)") @RequestParam("file") MultipartFile file,
            @Parameter(description = "Upload options") @Valid @ModelAttribute ResumeUploadRequest request) {
        
        ResumeResponse resume = resumeService.uploadResume(authentication, file, request);
        return ResponseEntity.status(201).body(resume);
    }

    /**
     * Update resume information
     */
    @PutMapping("/{resumeId}")
    @Operation(summary = "Update resume", description = "Update resume metadata and settings")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Resume updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    public ResponseEntity<ResumeResponse> updateResume(
            Authentication authentication,
            @Parameter(description = "Resume ID") @PathVariable String resumeId,
            @Valid @RequestBody ResumeUploadRequest request) {
        
        ResumeResponse resume = resumeService.updateResume(authentication, resumeId, request);
        return ResponseEntity.ok(resume);
    }

    /**
     * Delete resume
     */
    @DeleteMapping("/{resumeId}")
    @Operation(summary = "Delete resume", description = "Delete a resume (soft delete)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Resume deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    public ResponseEntity<Void> deleteResume(
            Authentication authentication,
            @Parameter(description = "Resume ID") @PathVariable String resumeId) {
        
        resumeService.deleteResume(authentication, resumeId);
        return ResponseEntity.noContent().build();
    }

    /**
     * Set resume as primary
     */
    @PostMapping("/{resumeId}/set-primary")
    @Operation(summary = "Set primary resume", description = "Set a resume as the primary/default resume")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Primary resume set successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    public ResponseEntity<ResumeResponse> setPrimaryResume(
            Authentication authentication,
            @Parameter(description = "Resume ID") @PathVariable String resumeId) {
        
        ResumeResponse resume = resumeService.setPrimaryResume(authentication, resumeId);
        return ResponseEntity.ok(resume);
    }

    /**
     * Get primary resume
     */
    @GetMapping("/primary")
    @Operation(summary = "Get primary resume", description = "Retrieve the user's primary resume")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Primary resume retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "No primary resume found")
    })
    public ResponseEntity<ResumeResponse> getPrimaryResume(Authentication authentication) {
        Optional<ResumeResponse> primaryResume = resumeService.getPrimaryResume(authentication);
        
        if (primaryResume.isPresent()) {
            return ResponseEntity.ok(primaryResume.get());
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Duplicate resume
     */
    @PostMapping("/{resumeId}/duplicate")
    @Operation(summary = "Duplicate resume", description = "Create a copy of an existing resume")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Resume duplicated successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    public ResponseEntity<ResumeResponse> duplicateResume(
            Authentication authentication,
            @Parameter(description = "Resume ID") @PathVariable String resumeId) {
        
        ResumeResponse duplicatedResume = resumeService.duplicateResume(authentication, resumeId);
        return ResponseEntity.status(201).body(duplicatedResume);
    }

    /**
     * Generate shareable link
     */
    @PostMapping("/{resumeId}/share")
    @Operation(summary = "Generate shareable link", description = "Generate a public shareable link for the resume")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Shareable link generated successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    public ResponseEntity<Map<String, String>> generateShareableLink(
            Authentication authentication,
            @Parameter(description = "Resume ID") @PathVariable String resumeId) {
        
        String shareableLink = resumeService.generateShareableLink(authentication, resumeId);
        return ResponseEntity.ok(Map.of("shareableLink", shareableLink));
    }

    /**
     * Get resume download URL
     */
    @GetMapping("/{resumeId}/download")
    @Operation(summary = "Get download URL", description = "Get the download URL for a resume")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Download URL retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    public ResponseEntity<Map<String, String>> getDownloadUrl(
            Authentication authentication,
            @Parameter(description = "Resume ID") @PathVariable String resumeId) {
        
        String downloadUrl = resumeService.getResumeDownloadUrl(authentication, resumeId);
        return ResponseEntity.ok(Map.of("downloadUrl", downloadUrl));
    }

    /**
     * Search user's resumes
     */
    @GetMapping("/search")
    @Operation(summary = "Search resumes", description = "Search through user's resumes by content")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search completed successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "400", description = "Invalid search parameters")
    })
    public ResponseEntity<List<ResumeResponse>> searchResumes(
            Authentication authentication,
            @Parameter(description = "Search term") @RequestParam String q) {
        
        if (q == null || q.trim().length() < 2) {
            return ResponseEntity.badRequest().build();
        }
        
        List<ResumeResponse> resumes = resumeService.searchResumes(authentication, q.trim());
        return ResponseEntity.ok(resumes);
    }
}
