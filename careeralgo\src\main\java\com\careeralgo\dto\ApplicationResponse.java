package com.careeralgo.dto;

import com.careeralgo.constant.ApplicationStatus;
import com.careeralgo.model.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO for application response data
 */
public class ApplicationResponse {

    private String id;
    private String userId;
    private String jobId;
    private String resumeId;
    private ApplicationStatus status;
    private LocalDateTime appliedDate;
    private Application.ApplicationMethod applicationMethod;
    private String coverLetter;
    private boolean customizedResume;
    private String notes;
    private Application.Priority priority;
    private LocalDateTime followUpReminder;
    private List<StatusHistory> statusHistory;
    private List<Interview> interviews;
    private Offer offer;
    private Integer matchScore;
    private Integer applicationScore;
    private List<ApplicationDocument> documents;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Job information (populated from job lookup)
    private JobSummary job;

    // Resume information (populated from resume lookup)
    private ResumeSummary resume;

    // Constructors
    public ApplicationResponse() {}

    public ApplicationResponse(Application application) {
        this.id = application.getId();
        this.userId = application.getUserId();
        this.jobId = application.getJobId();
        this.resumeId = application.getResumeId();
        this.status = application.getStatus();
        this.appliedDate = application.getAppliedDate();
        this.applicationMethod = application.getApplicationMethod();
        this.coverLetter = application.getCoverLetter();
        this.customizedResume = application.isCustomizedResume();
        this.notes = application.getNotes();
        this.priority = application.getPriority();
        this.followUpReminder = application.getFollowUpReminder();
        this.statusHistory = application.getStatusHistory();
        this.interviews = application.getInterviews();
        this.offer = application.getOffer();
        this.matchScore = application.getMatchScore();
        this.applicationScore = application.getApplicationScore();
        this.documents = application.getDocuments();
        this.createdAt = application.getCreatedAt();
        this.updatedAt = application.getUpdatedAt();
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getResumeId() {
        return resumeId;
    }

    public void setResumeId(String resumeId) {
        this.resumeId = resumeId;
    }

    public ApplicationStatus getStatus() {
        return status;
    }

    public void setStatus(ApplicationStatus status) {
        this.status = status;
    }

    public LocalDateTime getAppliedDate() {
        return appliedDate;
    }

    public void setAppliedDate(LocalDateTime appliedDate) {
        this.appliedDate = appliedDate;
    }

    public Application.ApplicationMethod getApplicationMethod() {
        return applicationMethod;
    }

    public void setApplicationMethod(Application.ApplicationMethod applicationMethod) {
        this.applicationMethod = applicationMethod;
    }

    public String getCoverLetter() {
        return coverLetter;
    }

    public void setCoverLetter(String coverLetter) {
        this.coverLetter = coverLetter;
    }

    public boolean isCustomizedResume() {
        return customizedResume;
    }

    public void setCustomizedResume(boolean customizedResume) {
        this.customizedResume = customizedResume;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Application.Priority getPriority() {
        return priority;
    }

    public void setPriority(Application.Priority priority) {
        this.priority = priority;
    }

    public LocalDateTime getFollowUpReminder() {
        return followUpReminder;
    }

    public void setFollowUpReminder(LocalDateTime followUpReminder) {
        this.followUpReminder = followUpReminder;
    }

    public List<StatusHistory> getStatusHistory() {
        return statusHistory;
    }

    public void setStatusHistory(List<StatusHistory> statusHistory) {
        this.statusHistory = statusHistory;
    }

    public List<Interview> getInterviews() {
        return interviews;
    }

    public void setInterviews(List<Interview> interviews) {
        this.interviews = interviews;
    }

    public Offer getOffer() {
        return offer;
    }

    public void setOffer(Offer offer) {
        this.offer = offer;
    }

    public Integer getMatchScore() {
        return matchScore;
    }

    public void setMatchScore(Integer matchScore) {
        this.matchScore = matchScore;
    }

    public Integer getApplicationScore() {
        return applicationScore;
    }

    public void setApplicationScore(Integer applicationScore) {
        this.applicationScore = applicationScore;
    }

    public List<ApplicationDocument> getDocuments() {
        return documents;
    }

    public void setDocuments(List<ApplicationDocument> documents) {
        this.documents = documents;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public JobSummary getJob() {
        return job;
    }

    public void setJob(JobSummary job) {
        this.job = job;
    }

    public ResumeSummary getResume() {
        return resume;
    }

    public void setResume(ResumeSummary resume) {
        this.resume = resume;
    }

    // Helper methods
    public boolean isActive() {
        return status != ApplicationStatus.REJECTED && 
               status != ApplicationStatus.WITHDRAWN && 
               status != ApplicationStatus.HIRED;
    }

    public boolean needsFollowUp() {
        return followUpReminder != null && 
               followUpReminder.isBefore(LocalDateTime.now()) && 
               isActive();
    }

    public String getStatusDisplayName() {
        return status.getValue().replace("_", " ");
    }

    public boolean hasOffer() {
        return offer != null;
    }

    public boolean hasInterviews() {
        return interviews != null && !interviews.isEmpty();
    }

    public int getInterviewCount() {
        return interviews != null ? interviews.size() : 0;
    }

    /**
     * Job summary for application response
     */
    public static class JobSummary {
        private String id;
        private String title;
        private String companyName;
        private String location;
        private boolean isActive;

        // Constructors, getters and setters
        public JobSummary() {}

        public JobSummary(String id, String title, String companyName, String location, boolean isActive) {
            this.id = id;
            this.title = title;
            this.companyName = companyName;
            this.location = location;
            this.isActive = isActive;
        }

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
        public boolean isActive() { return isActive; }
        public void setActive(boolean active) { isActive = active; }
    }

    /**
     * Resume summary for application response
     */
    public static class ResumeSummary {
        private String id;
        private String fileName;
        private boolean isPrimary;

        // Constructors, getters and setters
        public ResumeSummary() {}

        public ResumeSummary(String id, String fileName, boolean isPrimary) {
            this.id = id;
            this.fileName = fileName;
            this.isPrimary = isPrimary;
        }

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public boolean isPrimary() { return isPrimary; }
        public void setPrimary(boolean primary) { isPrimary = primary; }
    }
}
