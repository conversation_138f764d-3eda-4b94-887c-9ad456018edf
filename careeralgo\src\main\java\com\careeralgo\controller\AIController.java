package com.careeralgo.controller;

import com.careeralgo.ai.orchestrator.ModernAIAgentOrchestrator;
import com.careeralgo.ai.orchestrator.CareerAssistanceRequest;
import com.careeralgo.ai.orchestrator.CareerAssistanceResponse;
import com.careeralgo.ai.agent.*;
import com.careeralgo.service.OpenAIService;
import com.careeralgo.service.AIResumeAnalysisService;
import com.careeralgo.service.AIJobMatchingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * REST controller for AI-powered features
 */
@RestController
@RequestMapping("/ai")
@Tag(name = "AI Features", description = "AI-powered career assistance and optimization tools")
public class AIController {

    @Autowired
    private ModernAIAgentOrchestrator agentOrchestrator;

    @Autowired
    private OpenAIService openAIService;

    @Autowired
    private AIResumeAnalysisService aiResumeAnalysisService;

    @Autowired
    private AIJobMatchingService aiJobMatchingService;

    /**
     * Enhance resume content for specific job
     */
    @PostMapping("/resume/enhance")
    @Operation(summary = "Enhance resume content", description = "Use AI to enhance resume content for a specific job")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Resume content enhanced successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "500", description = "AI service error")
    })
    public ResponseEntity<Map<String, String>> enhanceResumeContent(
            Authentication authentication,
            @RequestBody Map<String, String> request) {

        String originalContent = request.get("originalContent");
        String jobDescription = request.get("jobDescription");

        if (originalContent == null || jobDescription == null) {
            return ResponseEntity.badRequest().build();
        }

        String enhancedContent = openAIService.enhanceResumeContent(originalContent, jobDescription);

        return ResponseEntity.ok(Map.of(
                "originalContent", originalContent,
                "enhancedContent", enhancedContent
        ));
    }

    /**
     * Generate cover letter using AI
     */
    @PostMapping("/cover-letter/generate")
    @Operation(summary = "Generate cover letter", description = "Generate a personalized cover letter using AI")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Cover letter generated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "500", description = "AI service error")
    })
    public ResponseEntity<Map<String, String>> generateCoverLetter(
            Authentication authentication,
            @RequestBody Map<String, String> request) {

        String resumeContent = request.get("resumeContent");
        String jobDescription = request.get("jobDescription");
        String companyName = request.get("companyName");

        if (resumeContent == null || jobDescription == null || companyName == null) {
            return ResponseEntity.badRequest().build();
        }

        String coverLetter = openAIService.generateCoverLetter(resumeContent, jobDescription, companyName);

        return ResponseEntity.ok(Map.of(
                "coverLetter", coverLetter,
                "companyName", companyName
        ));
    }

    /**
     * Generate interview questions for job preparation
     */
    @PostMapping("/interview/questions")
    @Operation(summary = "Generate interview questions", description = "Generate relevant interview questions for job preparation")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview questions generated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "500", description = "AI service error")
    })
    public ResponseEntity<Map<String, Object>> generateInterviewQuestions(
            Authentication authentication,
            @RequestBody Map<String, String> request) {

        String jobDescription = request.get("jobDescription");
        String experienceLevel = request.get("experienceLevel");

        if (jobDescription == null) {
            return ResponseEntity.badRequest().build();
        }

        List<String> questions = openAIService.generateInterviewQuestions(
                jobDescription, experienceLevel != null ? experienceLevel : "mid-level");

        return ResponseEntity.ok(Map.of(
                "questions", questions,
                "jobDescription", jobDescription,
                "experienceLevel", experienceLevel != null ? experienceLevel : "mid-level"
        ));
    }

    /**
     * Analyze job description using AI
     */
    @PostMapping("/job/analyze")
    @Operation(summary = "Analyze job description", description = "Extract key requirements and insights from job description")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job description analyzed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "500", description = "AI service error")
    })
    public ResponseEntity<OpenAIService.JobAnalysisResult> analyzeJobDescription(
            Authentication authentication,
            @RequestBody Map<String, String> request) {

        String jobDescription = request.get("jobDescription");

        if (jobDescription == null || jobDescription.trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        OpenAIService.JobAnalysisResult analysis = openAIService.analyzeJobDescription(jobDescription);

        return ResponseEntity.ok(analysis);
    }

    /**
     * Get personalized job search tips
     */
    @PostMapping("/job-search/tips")
    @Operation(summary = "Get job search tips", description = "Get personalized job search advice based on user profile")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job search tips generated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "500", description = "AI service error")
    })
    public ResponseEntity<Map<String, String>> getJobSearchTips(
            Authentication authentication,
            @RequestBody Map<String, String> request) {

        String userProfile = request.get("userProfile");
        String targetRole = request.get("targetRole");

        if (userProfile == null || targetRole == null) {
            return ResponseEntity.badRequest().build();
        }

        String tips = openAIService.generateJobSearchTips(userProfile, targetRole);

        return ResponseEntity.ok(Map.of(
                "tips", tips,
                "targetRole", targetRole
        ));
    }

    /**
     * Get trending skills in the job market
     */
    @GetMapping("/skills/trending")
    @Operation(summary = "Get trending skills", description = "Get list of trending skills in the job market")
    @ApiResponse(responseCode = "200", description = "Trending skills retrieved successfully")
    public ResponseEntity<Map<String, Object>> getTrendingSkills(
            @Parameter(description = "Number of skills to return") @RequestParam(defaultValue = "20") int limit) {

        List<String> trendingSkills = aiJobMatchingService.getTrendingSkills(limit);

        return ResponseEntity.ok(Map.of(
                "skills", trendingSkills,
                "limit", limit,
                "lastUpdated", java.time.LocalDateTime.now()
        ));
    }

    /**
     * Analyze job market trends
     */
    @GetMapping("/market/analysis")
    @Operation(summary = "Analyze job market", description = "Get job market analysis for specific location and industry")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Market analysis completed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid parameters")
    })
    public ResponseEntity<AIJobMatchingService.JobMarketAnalysis> analyzeJobMarket(
            @Parameter(description = "Location to analyze") @RequestParam(required = false) String location,
            @Parameter(description = "Industry to analyze") @RequestParam(required = false) String industry) {

        AIJobMatchingService.JobMarketAnalysis analysis = aiJobMatchingService.analyzeJobMarket(location, industry);

        return ResponseEntity.ok(analysis);
    }

    /**
     * Calculate job match score for user
     */
    @PostMapping("/job/match-score")
    @Operation(summary = "Calculate job match score", description = "Calculate how well a job matches the user's profile")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Match score calculated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    public ResponseEntity<Map<String, Object>> calculateJobMatchScore(
            Authentication authentication,
            @RequestBody Map<String, String> request) {

        String jobId = request.get("jobId");

        if (jobId == null) {
            return ResponseEntity.badRequest().build();
        }

        // TODO: Implement job match score calculation
        // This would require getting user and job data, then calculating match

        return ResponseEntity.ok(Map.of(
                "jobId", jobId,
                "matchScore", 85,
                "explanation", "Strong match based on skills and experience"
        ));
    }

    /**
     * Get AI-powered career advice
     */
    @PostMapping("/career/advice")
    @Operation(summary = "Get career advice", description = "Get personalized career advice based on user's goals and background")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Career advice generated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, String>> getCareerAdvice(
            Authentication authentication,
            @RequestBody Map<String, String> request) {

        String currentRole = request.get("currentRole");
        String targetRole = request.get("targetRole");
        String experience = request.get("experience");

        if (currentRole == null || targetRole == null) {
            return ResponseEntity.badRequest().build();
        }

        // TODO: Implement AI-powered career advice generation
        String advice = "Focus on developing skills in " + targetRole +
                       " and consider taking relevant courses or certifications.";

        return ResponseEntity.ok(Map.of(
                "advice", advice,
                "currentRole", currentRole,
                "targetRole", targetRole
        ));
    }

    // ========== MODERN AI AGENT ENDPOINTS ==========

    /**
     * Modern resume analysis using AI agents
     */
    @PostMapping("/agents/resume/analyze")
    @Operation(summary = "Modern AI resume analysis", description = "Analyze resume using advanced AI agents with comprehensive insights")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Resume analysis completed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public CompletableFuture<ResponseEntity<CareerAssistanceResponse>> analyzeResumeWithAgents(
            Authentication authentication,
            @RequestBody ModernResumeAnalysisRequest request) {

        String userId = getUserId(authentication);

        CareerAssistanceRequest agentRequest = CareerAssistanceRequest.resumeAnalysis(
            userId,
            request.getResumeContent(),
            request.getTargetRole(),
            request.getExperienceLevel()
        );

        return agentOrchestrator.processCareerRequest(agentRequest)
                .thenApply(ResponseEntity::ok)
                .exceptionally(ex -> ResponseEntity.internalServerError().build());
    }

    /**
     * Modern job matching using AI agents
     */
    @PostMapping("/agents/job/match")
    @Operation(summary = "Modern AI job matching", description = "Calculate job compatibility using advanced AI agents")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job matching completed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public CompletableFuture<ResponseEntity<CareerAssistanceResponse>> matchJobWithAgents(
            Authentication authentication,
            @RequestBody ModernJobMatchingRequest request) {

        String userId = getUserId(authentication);

        CareerAssistanceRequest agentRequest = CareerAssistanceRequest.jobMatching(
            userId,
            request.getCandidateProfile(),
            request.getJobDescription(),
            request.getJobRequirements()
        );

        return agentOrchestrator.processCareerRequest(agentRequest)
                .thenApply(ResponseEntity::ok)
                .exceptionally(ex -> ResponseEntity.internalServerError().build());
    }

    /**
     * Modern interview preparation using AI agents
     */
    @PostMapping("/agents/interview/evaluate")
    @Operation(summary = "Modern AI interview evaluation", description = "Evaluate interview answers using advanced AI agents")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview evaluation completed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public CompletableFuture<ResponseEntity<CareerAssistanceResponse>> evaluateInterviewWithAgents(
            Authentication authentication,
            @RequestBody ModernInterviewEvaluationRequest request) {

        String userId = getUserId(authentication);

        CareerAssistanceRequest agentRequest = CareerAssistanceRequest.interviewPrep(
            userId,
            request.getAnswer(),
            request.getQuestion(),
            request.getQuestionType(),
            request.getTargetRole()
        );

        return agentOrchestrator.processCareerRequest(agentRequest)
                .thenApply(ResponseEntity::ok)
                .exceptionally(ex -> ResponseEntity.internalServerError().build());
    }

    /**
     * Modern career advice using AI agents
     */
    @PostMapping("/agents/career/advice")
    @Operation(summary = "Modern AI career advice", description = "Get comprehensive career advice using advanced AI agents")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Career advice generated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public CompletableFuture<ResponseEntity<CareerAssistanceResponse>> getCareerAdviceWithAgents(
            Authentication authentication,
            @RequestBody ModernCareerAdviceRequest request) {

        String userId = getUserId(authentication);

        CareerAssistanceRequest agentRequest = CareerAssistanceRequest.careerAdvice(
            userId,
            request.getCareerSituation(),
            request.getCurrentRole(),
            request.getCareerGoals(),
            request.getTimeframe()
        );

        return agentOrchestrator.processCareerRequest(agentRequest)
                .thenApply(ResponseEntity::ok)
                .exceptionally(ex -> ResponseEntity.internalServerError().build());
    }

    /**
     * Comprehensive career analysis using multiple AI agents
     */
    @PostMapping("/agents/comprehensive/analyze")
    @Operation(summary = "Comprehensive AI career analysis", description = "Get comprehensive career analysis using multiple coordinated AI agents")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Comprehensive analysis completed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public CompletableFuture<ResponseEntity<CareerAssistanceResponse>> comprehensiveAnalysisWithAgents(
            Authentication authentication,
            @RequestBody ComprehensiveAnalysisRequest request) {

        String userId = getUserId(authentication);

        CareerAssistanceRequest agentRequest = CareerAssistanceRequest.comprehensive(
            userId,
            request.getResumeContent(),
            request.getJobDescription(),
            request.getCareerGoals()
        );

        return agentOrchestrator.processCareerRequest(agentRequest)
                .thenApply(ResponseEntity::ok)
                .exceptionally(ex -> ResponseEntity.internalServerError().build());
    }

    /**
     * Stream real-time resume analysis
     */
    @GetMapping(value = "/agents/resume/stream/{sessionId}", produces = "text/event-stream")
    @Operation(summary = "Stream resume analysis", description = "Get real-time streaming resume analysis updates")
    public Flux<ResumeAnalysisAgent.AnalysisUpdate> streamResumeAnalysis(
            Authentication authentication,
            @PathVariable String sessionId,
            @RequestParam String resumeContent) {

        return agentOrchestrator.getResumeAnalysisAgent()
                .streamResumeAnalysis(resumeContent, sessionId);
    }

    // Helper method to extract user ID
    private String getUserId(Authentication authentication) {
        if (authentication.getPrincipal() instanceof Jwt jwt) {
            return jwt.getSubject();
        }
        return "anonymous";
    }

    // Request DTOs for modern AI endpoints
    public static class ModernResumeAnalysisRequest {
        private String resumeContent;
        private String targetRole;
        private String experienceLevel;

        // Getters and setters
        public String getResumeContent() { return resumeContent; }
        public void setResumeContent(String resumeContent) { this.resumeContent = resumeContent; }
        public String getTargetRole() { return targetRole; }
        public void setTargetRole(String targetRole) { this.targetRole = targetRole; }
        public String getExperienceLevel() { return experienceLevel; }
        public void setExperienceLevel(String experienceLevel) { this.experienceLevel = experienceLevel; }
    }

    public static class ModernJobMatchingRequest {
        private String candidateProfile;
        private String jobDescription;
        private String jobRequirements;

        // Getters and setters
        public String getCandidateProfile() { return candidateProfile; }
        public void setCandidateProfile(String candidateProfile) { this.candidateProfile = candidateProfile; }
        public String getJobDescription() { return jobDescription; }
        public void setJobDescription(String jobDescription) { this.jobDescription = jobDescription; }
        public String getJobRequirements() { return jobRequirements; }
        public void setJobRequirements(String jobRequirements) { this.jobRequirements = jobRequirements; }
    }

    public static class ModernInterviewEvaluationRequest {
        private String answer;
        private String question;
        private String questionType;
        private String targetRole;

        // Getters and setters
        public String getAnswer() { return answer; }
        public void setAnswer(String answer) { this.answer = answer; }
        public String getQuestion() { return question; }
        public void setQuestion(String question) { this.question = question; }
        public String getQuestionType() { return questionType; }
        public void setQuestionType(String questionType) { this.questionType = questionType; }
        public String getTargetRole() { return targetRole; }
        public void setTargetRole(String targetRole) { this.targetRole = targetRole; }
    }

    public static class ModernCareerAdviceRequest {
        private String careerSituation;
        private String currentRole;
        private String careerGoals;
        private String timeframe;

        // Getters and setters
        public String getCareerSituation() { return careerSituation; }
        public void setCareerSituation(String careerSituation) { this.careerSituation = careerSituation; }
        public String getCurrentRole() { return currentRole; }
        public void setCurrentRole(String currentRole) { this.currentRole = currentRole; }
        public String getCareerGoals() { return careerGoals; }
        public void setCareerGoals(String careerGoals) { this.careerGoals = careerGoals; }
        public String getTimeframe() { return timeframe; }
        public void setTimeframe(String timeframe) { this.timeframe = timeframe; }
    }

    public static class ComprehensiveAnalysisRequest {
        private String resumeContent;
        private String jobDescription;
        private String careerGoals;

        // Getters and setters
        public String getResumeContent() { return resumeContent; }
        public void setResumeContent(String resumeContent) { this.resumeContent = resumeContent; }
        public String getJobDescription() { return jobDescription; }
        public void setJobDescription(String jobDescription) { this.jobDescription = jobDescription; }
        public String getCareerGoals() { return careerGoals; }
        public void setCareerGoals(String careerGoals) { this.careerGoals = careerGoals; }
    }
}
