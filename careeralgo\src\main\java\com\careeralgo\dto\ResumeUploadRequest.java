package com.careeralgo.dto;

import jakarta.validation.constraints.Size;

/**
 * DTO for resume upload request
 */
public class ResumeUploadRequest {

    @Size(max = 100, message = "Template ID must not exceed 100 characters")
    private String templateId;

    private boolean isPrimary = false;

    // Constructors
    public ResumeUploadRequest() {}

    public ResumeUploadRequest(String templateId, boolean isPrimary) {
        this.templateId = templateId;
        this.isPrimary = isPrimary;
    }

    // Getters and Setters
    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public boolean isPrimary() {
        return isPrimary;
    }

    public void setPrimary(boolean primary) {
        isPrimary = primary;
    }
}
