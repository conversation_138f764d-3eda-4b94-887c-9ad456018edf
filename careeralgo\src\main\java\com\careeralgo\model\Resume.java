package com.careeralgo.model;

import com.careeralgo.constant.FileType;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Resume document model for MongoDB
 */
@Document(collection = "resumes")
public class Resume {
    
    @Id
    private String id;
    
    @Indexed
    private String userId;
    
    private String fileName;
    private String originalFileName;
    private String cloudinaryUrl;
    private String cloudinaryPublicId;
    private FileType fileType;
    private Long fileSize;
    private boolean isActive = true;
    private boolean isPrimary = false;
    private Integer version = 1;
    private String templateId;
    
    private ParsedContent parsedContent;
    private AIAnalysis aiAnalysis;
    private List<ResumeCustomization> customizations;
    
    private Integer downloadCount = 0;
    private String shareableLink;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;

    // Constructors
    public Resume() {}

    public Resume(String userId, String fileName, String originalFileName, FileType fileType) {
        this.userId = userId;
        this.fileName = fileName;
        this.originalFileName = originalFileName;
        this.fileType = fileType;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOriginalFileName() {
        return originalFileName;
    }

    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }

    public String getCloudinaryUrl() {
        return cloudinaryUrl;
    }

    public void setCloudinaryUrl(String cloudinaryUrl) {
        this.cloudinaryUrl = cloudinaryUrl;
    }

    public String getCloudinaryPublicId() {
        return cloudinaryPublicId;
    }

    public void setCloudinaryPublicId(String cloudinaryPublicId) {
        this.cloudinaryPublicId = cloudinaryPublicId;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public boolean isPrimary() {
        return isPrimary;
    }

    public void setPrimary(boolean primary) {
        isPrimary = primary;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public ParsedContent getParsedContent() {
        return parsedContent;
    }

    public void setParsedContent(ParsedContent parsedContent) {
        this.parsedContent = parsedContent;
    }

    public AIAnalysis getAiAnalysis() {
        return aiAnalysis;
    }

    public void setAiAnalysis(AIAnalysis aiAnalysis) {
        this.aiAnalysis = aiAnalysis;
    }

    public List<ResumeCustomization> getCustomizations() {
        return customizations;
    }

    public void setCustomizations(List<ResumeCustomization> customizations) {
        this.customizations = customizations;
    }

    public Integer getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(Integer downloadCount) {
        this.downloadCount = downloadCount;
    }

    public String getShareableLink() {
        return shareableLink;
    }

    public void setShareableLink(String shareableLink) {
        this.shareableLink = shareableLink;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public void incrementDownloadCount() {
        this.downloadCount++;
    }
}
