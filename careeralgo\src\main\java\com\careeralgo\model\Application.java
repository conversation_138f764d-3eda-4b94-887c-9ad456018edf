package com.careeralgo.model;

import com.careeralgo.constant.ApplicationStatus;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Application document model for MongoDB
 */
@Document(collection = "applications")
public class Application {
    
    @Id
    private String id;
    
    @Indexed
    private String userId;
    
    @Indexed
    private String jobId;
    
    @Indexed
    private String resumeId;
    
    @Indexed
    private ApplicationStatus status = ApplicationStatus.SAVED;
    
    private LocalDateTime appliedDate;
    private ApplicationMethod applicationMethod;
    private String coverLetter;
    private boolean customizedResume = false;
    private String notes;
    private Priority priority = Priority.MEDIUM;
    private LocalDateTime followUpReminder;
    
    private List<StatusHistory> statusHistory;
    private List<Interview> interviews;
    private Offer offer;
    
    private Integer matchScore;
    private Integer applicationScore;
    private List<ApplicationDocument> documents;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;

    public enum ApplicationMethod {
        PLATFORM, EXTERNAL, EMAIL
    }

    public enum Priority {
        LOW, MEDIUM, HIGH
    }

    // Constructors
    public Application() {}

    public Application(String userId, String jobId, String resumeId) {
        this.userId = userId;
        this.jobId = jobId;
        this.resumeId = resumeId;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getResumeId() {
        return resumeId;
    }

    public void setResumeId(String resumeId) {
        this.resumeId = resumeId;
    }

    public ApplicationStatus getStatus() {
        return status;
    }

    public void setStatus(ApplicationStatus status) {
        this.status = status;
    }

    public LocalDateTime getAppliedDate() {
        return appliedDate;
    }

    public void setAppliedDate(LocalDateTime appliedDate) {
        this.appliedDate = appliedDate;
    }

    public ApplicationMethod getApplicationMethod() {
        return applicationMethod;
    }

    public void setApplicationMethod(ApplicationMethod applicationMethod) {
        this.applicationMethod = applicationMethod;
    }

    public String getCoverLetter() {
        return coverLetter;
    }

    public void setCoverLetter(String coverLetter) {
        this.coverLetter = coverLetter;
    }

    public boolean isCustomizedResume() {
        return customizedResume;
    }

    public void setCustomizedResume(boolean customizedResume) {
        this.customizedResume = customizedResume;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Priority getPriority() {
        return priority;
    }

    public void setPriority(Priority priority) {
        this.priority = priority;
    }

    public LocalDateTime getFollowUpReminder() {
        return followUpReminder;
    }

    public void setFollowUpReminder(LocalDateTime followUpReminder) {
        this.followUpReminder = followUpReminder;
    }

    public List<StatusHistory> getStatusHistory() {
        return statusHistory;
    }

    public void setStatusHistory(List<StatusHistory> statusHistory) {
        this.statusHistory = statusHistory;
    }

    public List<Interview> getInterviews() {
        return interviews;
    }

    public void setInterviews(List<Interview> interviews) {
        this.interviews = interviews;
    }

    public Offer getOffer() {
        return offer;
    }

    public void setOffer(Offer offer) {
        this.offer = offer;
    }

    public Integer getMatchScore() {
        return matchScore;
    }

    public void setMatchScore(Integer matchScore) {
        this.matchScore = matchScore;
    }

    public Integer getApplicationScore() {
        return applicationScore;
    }

    public void setApplicationScore(Integer applicationScore) {
        this.applicationScore = applicationScore;
    }

    public List<ApplicationDocument> getDocuments() {
        return documents;
    }

    public void setDocuments(List<ApplicationDocument> documents) {
        this.documents = documents;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public boolean isActive() {
        return status != ApplicationStatus.REJECTED && 
               status != ApplicationStatus.WITHDRAWN && 
               status != ApplicationStatus.HIRED;
    }

    public boolean needsFollowUp() {
        return followUpReminder != null && 
               followUpReminder.isBefore(LocalDateTime.now()) && 
               isActive();
    }

    public String getStatusDisplayName() {
        return status.getValue().replace("_", " ");
    }
}
