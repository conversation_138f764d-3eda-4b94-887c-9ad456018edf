package com.careeralgo.dto;

import com.careeralgo.model.Preferences;

/**
 * DTO for user preferences
 */
public class PreferencesDto {

    private boolean emailNotifications;
    private boolean jobAlerts;
    private boolean weeklyReports;
    private boolean marketingEmails;
    private Preferences.Theme theme;
    private String language;
    private String timezone;

    // Constructors
    public PreferencesDto() {}

    public PreferencesDto(Preferences preferences) {
        this.emailNotifications = preferences.isEmailNotifications();
        this.jobAlerts = preferences.isJobAlerts();
        this.weeklyReports = preferences.isWeeklyReports();
        this.marketingEmails = preferences.isMarketingEmails();
        this.theme = preferences.getTheme();
        this.language = preferences.getLanguage();
        this.timezone = preferences.getTimezone();
    }

    // Getters and Setters
    public boolean isEmailNotifications() {
        return emailNotifications;
    }

    public void setEmailNotifications(boolean emailNotifications) {
        this.emailNotifications = emailNotifications;
    }

    public boolean isJobAlerts() {
        return jobAlerts;
    }

    public void setJobAlerts(boolean jobAlerts) {
        this.jobAlerts = jobAlerts;
    }

    public boolean isWeeklyReports() {
        return weeklyReports;
    }

    public void setWeeklyReports(boolean weeklyReports) {
        this.weeklyReports = weeklyReports;
    }

    public boolean isMarketingEmails() {
        return marketingEmails;
    }

    public void setMarketingEmails(boolean marketingEmails) {
        this.marketingEmails = marketingEmails;
    }

    public Preferences.Theme getTheme() {
        return theme;
    }

    public void setTheme(Preferences.Theme theme) {
        this.theme = theme;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }
}
