package com.careeralgo.constant;

/**
 * Supported file types for document upload
 */
public enum FileType {
    PDF("PDF", "application/pdf"),
    DOCX("DOCX", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
    DOC("DOC", "application/msword"),
    TXT("TXT", "text/plain");

    private final String value;
    private final String mimeType;

    FileType(String value, String mimeType) {
        this.value = value;
        this.mimeType = mimeType;
    }

    public String getValue() {
        return value;
    }

    public String getMimeType() {
        return mimeType;
    }

    public static FileType fromMimeType(String mimeType) {
        for (FileType fileType : values()) {
            if (fileType.getMimeType().equals(mimeType)) {
                return fileType;
            }
        }
        throw new IllegalArgumentException("Unsupported file type: " + mimeType);
    }

    @Override
    public String toString() {
        return value;
    }
}
