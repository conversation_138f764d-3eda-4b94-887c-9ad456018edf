package com.careeralgo.model;

import java.time.LocalDate;
import java.util.List;

/**
 * Work experience entry from resume
 */
public class WorkExperience {
    
    private String company;
    private String position;
    private LocalDate startDate;
    private LocalDate endDate;
    private String location;
    private String description;
    private List<String> achievements;
    private List<String> technologies;

    // Constructors
    public WorkExperience() {}

    public WorkExperience(String company, String position, LocalDate startDate, LocalDate endDate) {
        this.company = company;
        this.position = position;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    // Getters and Setters
    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getAchievements() {
        return achievements;
    }

    public void setAchievements(List<String> achievements) {
        this.achievements = achievements;
    }

    public List<String> getTechnologies() {
        return technologies;
    }

    public void setTechnologies(List<String> technologies) {
        this.technologies = technologies;
    }

    public boolean isCurrent() {
        return endDate == null;
    }

    public String getDurationString() {
        if (startDate == null) return "Unknown duration";
        
        String start = startDate.getMonthValue() + "/" + startDate.getYear();
        String end = endDate != null ? (endDate.getMonthValue() + "/" + endDate.getYear()) : "Present";
        
        return start + " - " + end;
    }
}
