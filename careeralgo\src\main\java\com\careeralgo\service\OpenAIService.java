package com.careeralgo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for OpenAI API integration
 */
@Service
public class OpenAIService {

    private static final Logger logger = LoggerFactory.getLogger(OpenAIService.class);

    @Value("${careeralgo.ai.openai.api-key}")
    private String apiKey;

    @Value("${careeralgo.ai.openai.base-url:https://api.openai.com/v1}")
    private String baseUrl;

    @Value("${careeralgo.ai.openai.model:gpt-3.5-turbo}")
    private String defaultModel;

    @Value("${careeralgo.ai.openai.enabled:false}")
    private boolean enabled;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Enhance resume content using AI
     */
    public String enhanceResumeContent(String originalContent, String jobDescription) {
        if (!enabled) {
            logger.debug("OpenAI integration disabled, returning original content");
            return originalContent;
        }

        try {
            String prompt = buildResumeEnhancementPrompt(originalContent, jobDescription);
            return callChatCompletion(prompt, 1000);
        } catch (Exception e) {
            logger.error("Error enhancing resume content with OpenAI", e);
            return originalContent;
        }
    }

    /**
     * Generate cover letter using AI
     */
    public String generateCoverLetter(String resumeContent, String jobDescription, String companyName) {
        if (!enabled) {
            logger.debug("OpenAI integration disabled, returning placeholder cover letter");
            return "Please write a personalized cover letter for this position.";
        }

        try {
            String prompt = buildCoverLetterPrompt(resumeContent, jobDescription, companyName);
            return callChatCompletion(prompt, 800);
        } catch (Exception e) {
            logger.error("Error generating cover letter with OpenAI", e);
            return "Error generating cover letter. Please write manually.";
        }
    }

    /**
     * Generate interview questions based on job description
     */
    public List<String> generateInterviewQuestions(String jobDescription, String experienceLevel) {
        if (!enabled) {
            logger.debug("OpenAI integration disabled, returning default questions");
            return getDefaultInterviewQuestions();
        }

        try {
            String prompt = buildInterviewQuestionsPrompt(jobDescription, experienceLevel);
            String response = callChatCompletion(prompt, 600);
            
            // Parse response into list of questions
            return parseInterviewQuestions(response);
        } catch (Exception e) {
            logger.error("Error generating interview questions with OpenAI", e);
            return getDefaultInterviewQuestions();
        }
    }

    /**
     * Analyze job description and extract key requirements
     */
    public JobAnalysisResult analyzeJobDescription(String jobDescription) {
        if (!enabled) {
            logger.debug("OpenAI integration disabled, returning basic analysis");
            return new JobAnalysisResult();
        }

        try {
            String prompt = buildJobAnalysisPrompt(jobDescription);
            String response = callChatCompletion(prompt, 500);
            
            return parseJobAnalysis(response);
        } catch (Exception e) {
            logger.error("Error analyzing job description with OpenAI", e);
            return new JobAnalysisResult();
        }
    }

    /**
     * Generate personalized job search tips
     */
    public String generateJobSearchTips(String userProfile, String targetRole) {
        if (!enabled) {
            logger.debug("OpenAI integration disabled, returning generic tips");
            return "Focus on tailoring your resume and cover letter for each application.";
        }

        try {
            String prompt = buildJobSearchTipsPrompt(userProfile, targetRole);
            return callChatCompletion(prompt, 600);
        } catch (Exception e) {
            logger.error("Error generating job search tips with OpenAI", e);
            return "Focus on networking and continuous skill development.";
        }
    }

    // Private helper methods

    private String callChatCompletion(String prompt, int maxTokens) throws Exception {
        String url = baseUrl + "/chat/completions";
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(apiKey);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", defaultModel);
        requestBody.put("messages", List.of(
                Map.of("role", "system", "content", "You are a professional career advisor and resume expert."),
                Map.of("role", "user", "content", prompt)
        ));
        requestBody.put("max_tokens", maxTokens);
        requestBody.put("temperature", 0.7);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
        
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        
        if (response.getStatusCode() == HttpStatus.OK) {
            JsonNode jsonResponse = objectMapper.readTree(response.getBody());
            return jsonResponse.path("choices").get(0).path("message").path("content").asText();
        } else {
            throw new RuntimeException("OpenAI API call failed with status: " + response.getStatusCode());
        }
    }

    private String buildResumeEnhancementPrompt(String originalContent, String jobDescription) {
        return String.format("""
            Please enhance the following resume content to better match this job description.
            Focus on:
            1. Highlighting relevant skills and experience
            2. Using industry-specific keywords
            3. Improving action verbs and quantifiable achievements
            4. Maintaining professional tone
            
            Job Description:
            %s
            
            Original Resume Content:
            %s
            
            Please provide the enhanced version:
            """, jobDescription, originalContent);
    }

    private String buildCoverLetterPrompt(String resumeContent, String jobDescription, String companyName) {
        return String.format("""
            Write a professional cover letter for the following job application.
            
            Company: %s
            
            Job Description:
            %s
            
            Candidate's Resume Summary:
            %s
            
            The cover letter should:
            1. Be 3-4 paragraphs long
            2. Show enthusiasm for the role and company
            3. Highlight relevant experience and skills
            4. Include a strong call to action
            5. Be professional yet personable
            
            Cover Letter:
            """, companyName, jobDescription, resumeContent);
    }

    private String buildInterviewQuestionsPrompt(String jobDescription, String experienceLevel) {
        return String.format("""
            Generate 8-10 relevant interview questions for this job position.
            
            Job Description:
            %s
            
            Experience Level: %s
            
            Include a mix of:
            1. Technical questions related to the role
            2. Behavioral questions
            3. Company culture fit questions
            4. Questions about specific skills mentioned in the job description
            
            Format as a numbered list:
            """, jobDescription, experienceLevel);
    }

    private String buildJobAnalysisPrompt(String jobDescription) {
        return String.format("""
            Analyze this job description and extract key information:
            
            %s
            
            Please provide:
            1. Required skills (technical and soft skills)
            2. Experience level required
            3. Key responsibilities
            4. Company culture indicators
            5. Growth opportunities mentioned
            
            Format as structured text with clear sections.
            """, jobDescription);
    }

    private String buildJobSearchTipsPrompt(String userProfile, String targetRole) {
        return String.format("""
            Provide personalized job search advice for this candidate:
            
            User Profile:
            %s
            
            Target Role: %s
            
            Please provide 5-7 specific, actionable tips for:
            1. Resume optimization
            2. Networking strategies
            3. Skill development
            4. Interview preparation
            5. Job search tactics
            
            Make the advice specific to their background and target role.
            """, userProfile, targetRole);
    }

    private List<String> parseInterviewQuestions(String response) {
        // Simple parsing - split by numbers and clean up
        return List.of(response.split("\\d+\\."))
                .stream()
                .map(String::trim)
                .filter(q -> !q.isEmpty() && q.length() > 10)
                .limit(10)
                .toList();
    }

    private JobAnalysisResult parseJobAnalysis(String response) {
        JobAnalysisResult result = new JobAnalysisResult();
        result.setAnalysis(response);
        // TODO: Parse structured data from response
        return result;
    }

    private List<String> getDefaultInterviewQuestions() {
        return List.of(
                "Tell me about yourself and your background.",
                "Why are you interested in this position?",
                "What are your greatest strengths?",
                "Describe a challenging project you worked on.",
                "How do you handle tight deadlines?",
                "Where do you see yourself in 5 years?",
                "Why do you want to work for our company?",
                "Do you have any questions for us?"
        );
    }

    /**
     * Job analysis result
     */
    public static class JobAnalysisResult {
        private String analysis;
        private List<String> requiredSkills;
        private List<String> responsibilities;
        private String experienceLevel;

        // Getters and Setters
        public String getAnalysis() {
            return analysis;
        }

        public void setAnalysis(String analysis) {
            this.analysis = analysis;
        }

        public List<String> getRequiredSkills() {
            return requiredSkills;
        }

        public void setRequiredSkills(List<String> requiredSkills) {
            this.requiredSkills = requiredSkills;
        }

        public List<String> getResponsibilities() {
            return responsibilities;
        }

        public void setResponsibilities(List<String> responsibilities) {
            this.responsibilities = responsibilities;
        }

        public String getExperienceLevel() {
            return experienceLevel;
        }

        public void setExperienceLevel(String experienceLevel) {
            this.experienceLevel = experienceLevel;
        }
    }
}
