package com.careeralgo.controller;

import com.careeralgo.model.Notification;
import com.careeralgo.service.NotificationService;
import com.careeralgo.service.RealTimeNotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for notification management
 */
@RestController
@RequestMapping("/notifications")
@Tag(name = "Notifications", description = "APIs for notification management and real-time updates")
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private RealTimeNotificationService realTimeNotificationService;

    /**
     * Get user's notifications
     */
    @GetMapping
    @Operation(summary = "Get notifications", description = "Get paginated list of user's notifications")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notifications retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "400", description = "Invalid pagination parameters")
    })
    public ResponseEntity<Page<Notification>> getUserNotifications(
            Authentication authentication,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Show only unread notifications") @RequestParam(required = false) Boolean unreadOnly,
            @Parameter(description = "Filter by notification type") @RequestParam(required = false) Notification.NotificationType type) {
        
        Page<Notification> notifications = notificationService.getUserNotifications(authentication, page, size, unreadOnly, type);
        return ResponseEntity.ok(notifications);
    }

    /**
     * Get notification by ID
     */
    @GetMapping("/{notificationId}")
    @Operation(summary = "Get notification by ID", description = "Get detailed information about a specific notification")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notification retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Notification not found")
    })
    public ResponseEntity<Notification> getNotificationById(
            Authentication authentication,
            @Parameter(description = "Notification ID") @PathVariable String notificationId) {
        
        Notification notification = notificationService.getNotificationById(authentication, notificationId);
        return ResponseEntity.ok(notification);
    }

    /**
     * Mark notification as read
     */
    @PutMapping("/{notificationId}/read")
    @Operation(summary = "Mark notification as read", description = "Mark a specific notification as read")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notification marked as read successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Notification not found")
    })
    public ResponseEntity<Notification> markAsRead(
            Authentication authentication,
            @Parameter(description = "Notification ID") @PathVariable String notificationId) {
        
        Notification notification = notificationService.markAsRead(authentication, notificationId);
        return ResponseEntity.ok(notification);
    }

    /**
     * Mark multiple notifications as read
     */
    @PutMapping("/read-multiple")
    @Operation(summary = "Mark multiple notifications as read", description = "Mark multiple notifications as read")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notifications marked as read successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid notification IDs"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, String>> markMultipleAsRead(
            Authentication authentication,
            @RequestBody Map<String, List<String>> request) {
        
        List<String> notificationIds = request.get("notificationIds");
        if (notificationIds == null || notificationIds.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        notificationService.markMultipleAsRead(authentication, notificationIds);
        
        return ResponseEntity.ok(Map.of(
                "message", "Notifications marked as read successfully",
                "count", String.valueOf(notificationIds.size())
        ));
    }

    /**
     * Mark all notifications as read
     */
    @PutMapping("/read-all")
    @Operation(summary = "Mark all notifications as read", description = "Mark all user's notifications as read")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "All notifications marked as read successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, String>> markAllAsRead(Authentication authentication) {
        notificationService.markAllAsRead(authentication);
        
        return ResponseEntity.ok(Map.of(
                "message", "All notifications marked as read successfully"
        ));
    }

    /**
     * Delete notification
     */
    @DeleteMapping("/{notificationId}")
    @Operation(summary = "Delete notification", description = "Delete a specific notification")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Notification deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Notification not found")
    })
    public ResponseEntity<Void> deleteNotification(
            Authentication authentication,
            @Parameter(description = "Notification ID") @PathVariable String notificationId) {
        
        notificationService.deleteNotification(authentication, notificationId);
        return ResponseEntity.noContent().build();
    }

    /**
     * Delete multiple notifications
     */
    @DeleteMapping("/delete-multiple")
    @Operation(summary = "Delete multiple notifications", description = "Delete multiple notifications")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notifications deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid notification IDs"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, String>> deleteMultipleNotifications(
            Authentication authentication,
            @RequestBody Map<String, List<String>> request) {
        
        List<String> notificationIds = request.get("notificationIds");
        if (notificationIds == null || notificationIds.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        notificationService.deleteMultipleNotifications(authentication, notificationIds);
        
        return ResponseEntity.ok(Map.of(
                "message", "Notifications deleted successfully",
                "count", String.valueOf(notificationIds.size())
        ));
    }

    /**
     * Get notification statistics
     */
    @GetMapping("/stats")
    @Operation(summary = "Get notification statistics", description = "Get notification statistics and metrics for the user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<NotificationService.NotificationStats> getNotificationStats(Authentication authentication) {
        NotificationService.NotificationStats stats = notificationService.getNotificationStats(authentication);
        return ResponseEntity.ok(stats);
    }

    /**
     * Search notifications
     */
    @GetMapping("/search")
    @Operation(summary = "Search notifications", description = "Search through user's notifications")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search completed successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "400", description = "Invalid search parameters")
    })
    public ResponseEntity<Page<Notification>> searchNotifications(
            Authentication authentication,
            @Parameter(description = "Search term") @RequestParam String q,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        if (q == null || q.trim().length() < 2) {
            return ResponseEntity.badRequest().build();
        }
        
        Page<Notification> notifications = notificationService.searchNotifications(authentication, q.trim(), page, size);
        return ResponseEntity.ok(notifications);
    }

    /**
     * Get notification preferences
     */
    @GetMapping("/preferences")
    @Operation(summary = "Get notification preferences", description = "Get user's notification preferences")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Preferences retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<NotificationService.NotificationPreferences> getNotificationPreferences(Authentication authentication) {
        NotificationService.NotificationPreferences preferences = notificationService.getNotificationPreferences(authentication);
        return ResponseEntity.ok(preferences);
    }

    /**
     * Update notification preferences
     */
    @PutMapping("/preferences")
    @Operation(summary = "Update notification preferences", description = "Update user's notification preferences")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Preferences updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid preference data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<NotificationService.NotificationPreferences> updateNotificationPreferences(
            Authentication authentication,
            @Valid @RequestBody NotificationService.NotificationPreferences preferences) {
        
        NotificationService.NotificationPreferences updatedPreferences = 
                notificationService.updateNotificationPreferences(authentication, preferences);
        
        return ResponseEntity.ok(updatedPreferences);
    }

    /**
     * Get notification types
     */
    @GetMapping("/types")
    @Operation(summary = "Get notification types", description = "Get all available notification types")
    @ApiResponse(responseCode = "200", description = "Notification types retrieved successfully")
    public ResponseEntity<Notification.NotificationType[]> getNotificationTypes() {
        return ResponseEntity.ok(Notification.NotificationType.values());
    }

    /**
     * Get notification priorities
     */
    @GetMapping("/priorities")
    @Operation(summary = "Get notification priorities", description = "Get all available notification priorities")
    @ApiResponse(responseCode = "200", description = "Notification priorities retrieved successfully")
    public ResponseEntity<Notification.NotificationPriority[]> getNotificationPriorities() {
        return ResponseEntity.ok(Notification.NotificationPriority.values());
    }

    /**
     * Test real-time notification
     */
    @PostMapping("/test-realtime")
    @Operation(summary = "Test real-time notification", description = "Send a test real-time notification (development only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Test notification sent successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, String>> testRealTimeNotification(
            Authentication authentication,
            @RequestBody(required = false) Map<String, String> request) {
        
        String title = request != null ? request.getOrDefault("title", "Test Notification") : "Test Notification";
        String message = request != null ? request.getOrDefault("message", "This is a test real-time notification") : "This is a test real-time notification";
        
        // Create and send test notification
        // String userId = getUserId(authentication);
        // notificationService.createNotification(userId, Notification.NotificationType.SYSTEM_ANNOUNCEMENT, title, message, null, null);
        
        return ResponseEntity.ok(Map.of(
                "message", "Test notification sent successfully",
                "title", title
        ));
    }

    /**
     * Get real-time statistics
     */
    @GetMapping("/realtime-stats")
    @Operation(summary = "Get real-time statistics", description = "Get real-time notification system statistics")
    @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully")
    public ResponseEntity<Map<String, Object>> getRealTimeStats() {
        Map<String, Object> stats = realTimeNotificationService.getRealTimeStats();
        return ResponseEntity.ok(stats);
    }
}
