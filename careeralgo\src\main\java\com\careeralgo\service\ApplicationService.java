package com.careeralgo.service;

import com.careeralgo.constant.ApplicationStatus;
import com.careeralgo.dto.ApplicationRequest;
import com.careeralgo.dto.ApplicationResponse;
import com.careeralgo.dto.ApplicationUpdateRequest;
import com.careeralgo.exception.ResourceNotFoundException;
import com.careeralgo.model.*;
import com.careeralgo.repository.ApplicationRepository;
import com.careeralgo.repository.JobRepository;
import com.careeralgo.repository.ResumeRepository;
import com.careeralgo.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for job application management operations
 */
@Service
public class ApplicationService {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationService.class);

    @Autowired
    private ApplicationRepository applicationRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private ResumeRepository resumeRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmailService emailService;

    @Autowired
    private AIJobMatchingService aiJobMatchingService;

    /**
     * Get user's applications with pagination
     */
    public Page<ApplicationResponse> getUserApplications(Authentication authentication, int page, int size, String status) {
        String userId = getUserId(authentication);
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        
        Page<Application> applications;
        if (status != null && !status.isEmpty()) {
            ApplicationStatus applicationStatus = ApplicationStatus.valueOf(status.toUpperCase());
            applications = applicationRepository.findByUserIdAndStatusOrderByCreatedAtDesc(userId, applicationStatus, pageable);
        } else {
            applications = applicationRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        }
        
        return applications.map(ApplicationResponse::new);
    }

    /**
     * Get application by ID
     */
    public ApplicationResponse getApplicationById(Authentication authentication, String applicationId) {
        String userId = getUserId(authentication);
        Application application = findApplicationByIdAndUser(applicationId, userId);
        return new ApplicationResponse(application);
    }

    /**
     * Create new job application
     */
    public ApplicationResponse createApplication(Authentication authentication, ApplicationRequest request) {
        String userId = getUserId(authentication);
        
        // Verify user hasn't already applied to this job
        if (applicationRepository.existsByUserIdAndJobId(userId, request.getJobId())) {
            throw new IllegalArgumentException("You have already applied to this job");
        }
        
        // Verify job exists and is active
        Job job = jobRepository.findById(request.getJobId())
                .orElseThrow(() -> new ResourceNotFoundException("Job not found: " + request.getJobId()));
        
        if (!job.isActive()) {
            throw new IllegalArgumentException("This job is no longer active");
        }
        
        // Verify resume exists if provided
        Resume resume = null;
        if (request.getResumeId() != null) {
            resume = resumeRepository.findById(request.getResumeId())
                    .orElseThrow(() -> new ResourceNotFoundException("Resume not found: " + request.getResumeId()));
            
            if (!resume.getUserId().equals(userId)) {
                throw new IllegalArgumentException("Resume does not belong to user");
            }
        } else {
            // Use primary resume if no specific resume provided
            Optional<Resume> primaryResume = resumeRepository.findByUserIdAndIsPrimaryTrue(userId);
            if (primaryResume.isPresent()) {
                resume = primaryResume.get();
            }
        }
        
        // Create application
        Application application = new Application(userId, request.getJobId(), resume != null ? resume.getId() : null);
        application.setStatus(ApplicationStatus.SAVED);
        application.setCoverLetter(request.getCoverLetter());
        application.setNotes(request.getNotes());
        application.setPriority(request.getPriority() != null ? request.getPriority() : Application.Priority.MEDIUM);
        application.setApplicationMethod(request.getApplicationMethod());
        
        // Calculate match score
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        int matchScore = aiJobMatchingService.calculateJobMatchScore(user, job);
        application.setMatchScore(matchScore);
        
        // Initialize status history
        List<StatusHistory> statusHistory = new ArrayList<>();
        statusHistory.add(new StatusHistory(ApplicationStatus.SAVED, "Application created", StatusHistory.HistorySource.USER));
        application.setStatusHistory(statusHistory);
        
        Application savedApplication = applicationRepository.save(application);
        
        // Increment job application count
        job.incrementApplicationCount();
        jobRepository.save(job);
        
        logger.info("Created application for user: {} to job: {}", userId, request.getJobId());
        return new ApplicationResponse(savedApplication);
    }

    /**
     * Update application
     */
    public ApplicationResponse updateApplication(Authentication authentication, String applicationId, 
                                               ApplicationUpdateRequest request) {
        String userId = getUserId(authentication);
        Application application = findApplicationByIdAndUser(applicationId, userId);
        
        boolean statusChanged = false;
        ApplicationStatus oldStatus = application.getStatus();
        
        // Update fields
        if (request.getStatus() != null && request.getStatus() != application.getStatus()) {
            application.setStatus(request.getStatus());
            statusChanged = true;
            
            // Set applied date when status changes to APPLIED
            if (request.getStatus() == ApplicationStatus.APPLIED && application.getAppliedDate() == null) {
                application.setAppliedDate(LocalDateTime.now());
            }
        }
        
        if (request.getCoverLetter() != null) {
            application.setCoverLetter(request.getCoverLetter());
        }
        
        if (request.getNotes() != null) {
            application.setNotes(request.getNotes());
        }
        
        if (request.getPriority() != null) {
            application.setPriority(request.getPriority());
        }
        
        if (request.getFollowUpReminder() != null) {
            application.setFollowUpReminder(request.getFollowUpReminder());
        }
        
        // Add status history entry if status changed
        if (statusChanged) {
            List<StatusHistory> statusHistory = application.getStatusHistory();
            if (statusHistory == null) {
                statusHistory = new ArrayList<>();
                application.setStatusHistory(statusHistory);
            }
            
            String notes = request.getStatusNotes() != null ? request.getStatusNotes() : 
                          "Status updated from " + oldStatus + " to " + request.getStatus();
            statusHistory.add(new StatusHistory(request.getStatus(), notes, StatusHistory.HistorySource.USER));
        }
        
        Application savedApplication = applicationRepository.save(application);
        logger.info("Updated application: {} for user: {}", applicationId, userId);
        
        return new ApplicationResponse(savedApplication);
    }

    /**
     * Delete application
     */
    public void deleteApplication(Authentication authentication, String applicationId) {
        String userId = getUserId(authentication);
        Application application = findApplicationByIdAndUser(applicationId, userId);
        
        applicationRepository.delete(application);
        logger.info("Deleted application: {} for user: {}", applicationId, userId);
    }

    /**
     * Get application statistics for user
     */
    public ApplicationStatsResponse getApplicationStats(Authentication authentication) {
        String userId = getUserId(authentication);
        
        ApplicationStatsResponse stats = new ApplicationStatsResponse();
        stats.setUserId(userId);
        stats.setTotalApplications(applicationRepository.countByUserId(userId));
        
        // Count by status
        for (ApplicationStatus status : ApplicationStatus.values()) {
            long count = applicationRepository.countByUserIdAndStatus(userId, status);
            stats.getApplicationsByStatus().put(status.getValue(), count);
        }
        
        // Get active applications
        List<Application> activeApplications = applicationRepository.findActiveApplicationsByUserId(userId);
        stats.setActiveApplications((long) activeApplications.size());
        
        // Calculate response rate (interviews / applications)
        long interviewCount = activeApplications.stream()
                .mapToLong(app -> app.getInterviews() != null ? app.getInterviews().size() : 0)
                .sum();
        stats.setInterviewCount(interviewCount);
        
        if (stats.getTotalApplications() > 0) {
            stats.setResponseRate((double) interviewCount / stats.getTotalApplications() * 100);
        }
        
        return stats;
    }

    /**
     * Get applications needing follow-up
     */
    public List<ApplicationResponse> getApplicationsNeedingFollowUp(Authentication authentication) {
        String userId = getUserId(authentication);
        
        List<Application> applications = applicationRepository.findApplicationsNeedingFollowUp(LocalDateTime.now())
                .stream()
                .filter(app -> app.getUserId().equals(userId))
                .collect(Collectors.toList());
        
        return applications.stream()
                .map(ApplicationResponse::new)
                .collect(Collectors.toList());
    }

    /**
     * Add interview to application
     */
    public ApplicationResponse addInterview(Authentication authentication, String applicationId, Interview interview) {
        String userId = getUserId(authentication);
        Application application = findApplicationByIdAndUser(applicationId, userId);
        
        List<Interview> interviews = application.getInterviews();
        if (interviews == null) {
            interviews = new ArrayList<>();
            application.setInterviews(interviews);
        }
        
        interviews.add(interview);
        
        // Update status to INTERVIEW if not already
        if (application.getStatus() != ApplicationStatus.INTERVIEW) {
            application.setStatus(ApplicationStatus.INTERVIEW);
            
            // Add status history
            List<StatusHistory> statusHistory = application.getStatusHistory();
            if (statusHistory == null) {
                statusHistory = new ArrayList<>();
                application.setStatusHistory(statusHistory);
            }
            statusHistory.add(new StatusHistory(ApplicationStatus.INTERVIEW, 
                    "Interview scheduled", StatusHistory.HistorySource.USER));
        }
        
        Application savedApplication = applicationRepository.save(application);
        logger.info("Added interview to application: {} for user: {}", applicationId, userId);
        
        return new ApplicationResponse(savedApplication);
    }

    /**
     * Update interview feedback
     */
    public ApplicationResponse updateInterviewFeedback(Authentication authentication, String applicationId, 
                                                     int interviewIndex, Interview.InterviewFeedback feedback) {
        String userId = getUserId(authentication);
        Application application = findApplicationByIdAndUser(applicationId, userId);
        
        List<Interview> interviews = application.getInterviews();
        if (interviews == null || interviewIndex >= interviews.size()) {
            throw new IllegalArgumentException("Interview not found");
        }
        
        interviews.get(interviewIndex).setFeedback(feedback);
        
        Application savedApplication = applicationRepository.save(application);
        logger.info("Updated interview feedback for application: {} for user: {}", applicationId, userId);
        
        return new ApplicationResponse(savedApplication);
    }

    /**
     * Add offer to application
     */
    public ApplicationResponse addOffer(Authentication authentication, String applicationId, Offer offer) {
        String userId = getUserId(authentication);
        Application application = findApplicationByIdAndUser(applicationId, userId);
        
        application.setOffer(offer);
        
        // Update status to OFFER
        application.setStatus(ApplicationStatus.OFFER);
        
        // Add status history
        List<StatusHistory> statusHistory = application.getStatusHistory();
        if (statusHistory == null) {
            statusHistory = new ArrayList<>();
            application.setStatusHistory(statusHistory);
        }
        statusHistory.add(new StatusHistory(ApplicationStatus.OFFER, 
                "Job offer received", StatusHistory.HistorySource.USER));
        
        Application savedApplication = applicationRepository.save(application);
        logger.info("Added offer to application: {} for user: {}", applicationId, userId);
        
        // Send notification email
        try {
            emailService.sendOfferReceivedNotification(userId, savedApplication);
        } catch (Exception e) {
            logger.warn("Failed to send offer notification email", e);
        }
        
        return new ApplicationResponse(savedApplication);
    }

    /**
     * Search user's applications
     */
    public List<ApplicationResponse> searchApplications(Authentication authentication, String searchTerm) {
        String userId = getUserId(authentication);
        
        Pageable pageable = PageRequest.of(0, 100); // Limit search results
        Page<Application> applications = applicationRepository.searchUserApplications(userId, searchTerm, pageable);
        
        return applications.getContent().stream()
                .map(ApplicationResponse::new)
                .collect(Collectors.toList());
    }

    // Helper methods
    
    private String getUserId(Authentication authentication) {
        if (authentication.getPrincipal() instanceof Jwt jwt) {
            String clerkUserId = jwt.getSubject();
            User user = userRepository.findByClerkUserId(clerkUserId)
                    .orElseThrow(() -> new ResourceNotFoundException("User not found"));
            return user.getId();
        }
        throw new IllegalArgumentException("Invalid authentication type");
    }

    private Application findApplicationByIdAndUser(String applicationId, String userId) {
        Application application = applicationRepository.findById(applicationId)
                .orElseThrow(() -> new ResourceNotFoundException("Application not found: " + applicationId));
        
        if (!application.getUserId().equals(userId)) {
            throw new ResourceNotFoundException("Application not found: " + applicationId);
        }
        
        return application;
    }

    /**
     * Application statistics response DTO
     */
    public static class ApplicationStatsResponse {
        private String userId;
        private Long totalApplications;
        private Long activeApplications;
        private Long interviewCount;
        private Double responseRate;
        private java.util.Map<String, Long> applicationsByStatus = new java.util.HashMap<>();

        // Getters and Setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public Long getTotalApplications() {
            return totalApplications;
        }

        public void setTotalApplications(Long totalApplications) {
            this.totalApplications = totalApplications;
        }

        public Long getActiveApplications() {
            return activeApplications;
        }

        public void setActiveApplications(Long activeApplications) {
            this.activeApplications = activeApplications;
        }

        public Long getInterviewCount() {
            return interviewCount;
        }

        public void setInterviewCount(Long interviewCount) {
            this.interviewCount = interviewCount;
        }

        public Double getResponseRate() {
            return responseRate;
        }

        public void setResponseRate(Double responseRate) {
            this.responseRate = responseRate;
        }

        public java.util.Map<String, Long> getApplicationsByStatus() {
            return applicationsByStatus;
        }

        public void setApplicationsByStatus(java.util.Map<String, Long> applicationsByStatus) {
            this.applicationsByStatus = applicationsByStatus;
        }
    }
}
