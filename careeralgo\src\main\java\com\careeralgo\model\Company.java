package com.careeralgo.model;

/**
 * Company information embedded in Job document
 */
public class Company {

    private String name;
    private String slug;
    private String logo;
    private String website;
    private String size;
    private String industry;
    private String description;
    private Integer foundedYear;
    private String headquarters;

    // Constructors
    public Company() {}

    public Company(String name) {
        this.name = name;
        this.generateSlug();
    }

    public Company(String name, String industry, String size) {
        this.name = name;
        this.industry = industry;
        this.size = size;
        this.generateSlug();
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        this.generateSlug();
    }

    public String getSlug() {
        return slug;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public void setLogoUrl(String logoUrl) {
        this.logo = logoUrl;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getFoundedYear() {
        return foundedYear;
    }

    public void setFoundedYear(Integer foundedYear) {
        this.foundedYear = foundedYear;
    }

    public String getHeadquarters() {
        return headquarters;
    }

    public void setHeadquarters(String headquarters) {
        this.headquarters = headquarters;
    }

    private void generateSlug() {
        if (name != null) {
            this.slug = name.toLowerCase()
                    .replaceAll("[^a-z0-9\\s-]", "")
                    .replaceAll("\\s+", "-")
                    .replaceAll("-+", "-")
                    .trim();
        }
    }

    public String getSizeCategory() {
        if (size == null) return "Unknown";

        // Parse size ranges like "1-10", "11-50", "201-500", etc.
        if (size.contains("-")) {
            String[] parts = size.split("-");
            try {
                int maxSize = Integer.parseInt(parts[1]);
                if (maxSize <= 10) return "Startup";
                if (maxSize <= 50) return "Small";
                if (maxSize <= 200) return "Medium";
                if (maxSize <= 1000) return "Large";
                return "Enterprise";
            } catch (NumberFormatException e) {
                return "Unknown";
            }
        }

        return size;
    }

    @Override
    public String toString() {
        return name;
    }
}
