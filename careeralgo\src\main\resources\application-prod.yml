# Production Environment Configuration
spring:
  # MongoDB Configuration for Production
  data:
    mongodb:
      uri: ${MONGODB_URI}
  
  # Redis Configuration for Production
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD}
    ssl: true
  
  # Production Security Configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${CLERK_ISSUER_URI}

# Production Server Configuration
server:
  port: ${PORT:8080}
  # Production SSL Configuration
  ssl:
    enabled: ${SSL_ENABLED:false}
    key-store: ${SSL_KEY_STORE:}
    key-store-password: ${SSL_KEY_STORE_PASSWORD:}
    key-store-type: ${SSL_KEY_STORE_TYPE:PKCS12}

# Production Logging (Less Verbose)
logging:
  level:
    com.careeralgo: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.springframework.data.mongodb: WARN
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/careeralgo/application.log
    max-size: 100MB
    max-history: 30

# Production Specific Configuration
careeralgo:
  # Production CORS (Restrictive)
  cors:
    allowed-origins: ${ALLOWED_ORIGINS}
    
  # Production Rate Limiting (Strict)
  rate-limit:
    enabled: true
    requests-per-minute: 60
    burst-capacity: 100
    
  # Production AI Configuration
  ai:
    openai:
      model: ${OPENAI_MODEL:gpt-4}
      max-tokens: ${OPENAI_MAX_TOKENS:2000}
      temperature: ${OPENAI_TEMPERATURE:0.7}

# Production Management Endpoints (Restricted)
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: never
  security:
    enabled: true

# Swagger UI Configuration for Production (Disabled)
springdoc:
  swagger-ui:
    enabled: false
  api-docs:
    enabled: false

# Production Performance Tuning
server:
  tomcat:
    threads:
      max: 200
      min-spare: 10
    connection-timeout: 20000
    max-connections: 8192
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
