package com.careeralgo.model;

/**
 * Language proficiency entry from resume
 */
public class Language {
    
    private String language;
    private Proficiency proficiency;

    public enum Proficiency {
        NATIVE("Native"),
        FLUENT("Fluent"),
        CONVERSATIONAL("Conversational"),
        BASIC("Basic"),
        BEGINNER("Beginner");

        private final String displayName;

        Proficiency(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }

        @Override
        public String toString() {
            return displayName;
        }
    }

    // Constructors
    public Language() {}

    public Language(String language, Proficiency proficiency) {
        this.language = language;
        this.proficiency = proficiency;
    }

    // Getters and Setters
    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Proficiency getProficiency() {
        return proficiency;
    }

    public void setProficiency(Proficiency proficiency) {
        this.proficiency = proficiency;
    }

    public String getDisplayString() {
        return language + " (" + (proficiency != null ? proficiency.getDisplayName() : "Unknown") + ")";
    }

    @Override
    public String toString() {
        return getDisplayString();
    }
}
