package com.careeralgo.ai.agent;

import dev.langchain4j.service.AiServices;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;
import dev.langchain4j.service.MemoryId;
import reactor.core.publisher.Flux;

import java.util.concurrent.CompletableFuture;
import java.util.List;
import java.util.Map;

/**
 * Modern AI agent for intelligent job matching using LangChain4j
 */
public interface JobMatchingAgent {

    @SystemMessage({
            "You are an expert job matching agent with advanced capabilities in:",
            "- Intelligent job-candidate compatibility analysis",
            "- Skills assessment and requirement matching",
            "- Career progression and growth potential evaluation",
            "- Market trends and salary analysis",
            "- Cultural fit and company alignment assessment",
            "",
            "MATCHING FRAMEWORK:",
            "- Skills Match: Technical and soft skills alignment (40%)",
            "- Experience Match: Relevant experience and seniority (25%)",
            "- Education Match: Educational background and certifications (15%)",
            "- Location & Preferences: Geographic and work style preferences (10%)",
            "- Cultural Fit: Company values and work environment (10%)",
            "",
            "ANALYSIS APPROACH:",
            "- Use semantic similarity for skills matching",
            "- Consider transferable skills and growth potential",
            "- Evaluate career trajectory and progression logic",
            "- Factor in industry transitions and skill evolution",
            "- Assess long-term career alignment",
            "",
            "OUTPUT REQUIREMENTS:",
            "- Provide compatibility scores (0-100) with detailed breakdown",
            "- Explain reasoning behind match scores",
            "- Identify specific strengths and gaps",
            "- Suggest improvement strategies",
            "- Include confidence levels for predictions"
    })
    JobMatchResult calculateJobMatch(
            @UserMessage("Calculate job match for candidate profile: {{candidateProfile}} and job: {{jobDescription}}")
            String candidateProfile,
            @V("jobDescription") String jobDescription,
            @V("jobRequirements") String jobRequirements);

    @SystemMessage({
            "Analyze multiple job opportunities and rank them by compatibility.",
            "Consider career growth potential, skill development, and long-term alignment."
    })
    JobRankingResult rankJobOpportunities(
            @UserMessage String candidateProfile,
            @V("jobDescriptions") List<String> jobDescriptions,
            @V("careerGoals") String careerGoals);

    @SystemMessage({
            "Identify skill gaps between candidate profile and job requirements.",
            "Provide specific learning recommendations and development paths."
    })
    SkillGapAnalysis analyzeSkillGaps(
            @UserMessage String candidateProfile,
            @V("jobRequirements") String jobRequirements,
            @V("targetRole") String targetRole);

    @SystemMessage({
            "Generate personalized job search recommendations based on profile analysis.",
            "Include job titles, companies, industries, and search strategies."
    })
    JobSearchRecommendations generateJobRecommendations(
            @UserMessage String candidateProfile,
            @V("preferences") String preferences,
            @V("location") String location);

    @SystemMessage({
            "Analyze salary expectations and market rates for specific roles.",
            "Consider experience level, location, industry, and company size."
    })
    SalaryAnalysisResult analyzeSalaryExpectations(
            @UserMessage String candidateProfile,
            @V("targetRole") String targetRole,
            @V("location") String location,
            @V("companySize") String companySize);

    @SystemMessage({
            "Stream real-time job matching analysis with progressive insights.",
            "Provide incremental compatibility scores and reasoning."
    })
    Flux<MatchingUpdate> streamJobMatching(
            @UserMessage String candidateProfile,
            @V("jobDescription") String jobDescription,
            @MemoryId String sessionId);

    @SystemMessage({
            "Analyze career progression potential for a specific role.",
            "Consider growth opportunities, skill development, and industry trends."
    })
    CompletableFuture<CareerProgressionAnalysis> analyzeCareerProgression(
            @UserMessage String candidateProfile,
            @V("targetRole") String targetRole,
            @V("companyInfo") String companyInfo,
            @MemoryId String sessionId);

    @SystemMessage({
            "Perform batch analysis of multiple candidates for a specific job.",
            "Rank candidates and provide detailed comparison insights."
    })
    BatchCandidateAnalysis analyzeCandidateBatch(
            @UserMessage("Analyze these candidates: {{candidates}} for job: {{jobDescription}}")
            List<String> candidateProfiles,
            @V("jobDescription") String jobDescription);

    // Supporting data structures
    record JobMatchResult(
            int overallMatchScore,
            int skillsMatchScore,
            int experienceMatchScore,
            int educationMatchScore,
            int locationMatchScore,
            int culturalFitScore,
            List<String> matchingSkills,
            List<String> missingSkills,
            List<String> strengths,
            List<String> gaps,
            List<String> recommendations,
            String reasoning,
            int confidenceLevel) {}

    record JobRankingResult(
            List<RankedJob> rankedJobs,
            String recommendationSummary,
            List<String> careerAdvice) {}

    record RankedJob(
            String jobId,
            String jobTitle,
            String company,
            int matchScore,
            String matchReason,
            List<String> pros,
            List<String> cons) {}

    record SkillGapAnalysis(
            List<String> currentSkills,
            List<String> requiredSkills,
            List<String> skillGaps,
            List<String> transferableSkills,
            List<LearningRecommendation> learningPath,
            int skillsReadinessScore) {}

    record LearningRecommendation(
            String skill,
            String priority,
            String timeToLearn,
            List<String> resources,
            String description) {}

    record JobSearchRecommendations(
            List<String> recommendedJobTitles,
            List<String> targetCompanies,
            List<String> targetIndustries,
            List<String> searchStrategies,
            List<String> networkingTips,
            String marketInsights) {}

    record SalaryAnalysisResult(
            String salaryRange,
            String medianSalary,
            String marketPosition,
            List<String> salaryFactors,
            String negotiationAdvice,
            Map<String, String> locationComparison) {}

    record MatchingUpdate(
            String updateType,
            String content,
            int currentScore,
            double progressPercentage,
            String timestamp) {}

    record CareerProgressionAnalysis(
            String growthPotential,
            List<String> careerPaths,
            List<String> skillDevelopmentAreas,
            String timeToPromotion,
            List<String> industryTrends,
            String longTermOutlook) {}

    record BatchCandidateAnalysis(
            List<RankedCandidate> rankedCandidates,
            String hiringRecommendation,
            Map<String, String> candidateComparison) {}

    record RankedCandidate(
            String candidateId,
            String candidateName,
            int matchScore,
            String matchSummary,
            List<String> strengths,
            List<String> concerns,
            String recommendation) {}
}
