package com.careeralgo.model;

import java.time.LocalDateTime;

/**
 * Document attached to job application
 */
public class ApplicationDocument {
    
    private DocumentType type;
    private String cloudinaryUrl;
    private LocalDateTime uploadedAt;

    public enum DocumentType {
        RESUME, COVER_LETTER, PORTFOLIO, TRANSCRIPT, CERTIFICATE, OTHER
    }

    // Constructors
    public ApplicationDocument() {}

    public ApplicationDocument(DocumentType type, String cloudinaryUrl) {
        this.type = type;
        this.cloudinaryUrl = cloudinaryUrl;
        this.uploadedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public DocumentType getType() {
        return type;
    }

    public void setType(DocumentType type) {
        this.type = type;
    }

    public String getCloudinaryUrl() {
        return cloudinaryUrl;
    }

    public void setCloudinaryUrl(String cloudinaryUrl) {
        this.cloudinaryUrl = cloudinaryUrl;
    }

    public LocalDateTime getUploadedAt() {
        return uploadedAt;
    }

    public void setUploadedAt(LocalDateTime uploadedAt) {
        this.uploadedAt = uploadedAt;
    }

    public String getDisplayName() {
        return type.toString().replace("_", " ");
    }
}
