package com.careeralgo.model;

/**
 * Salary expectation information
 */
public class SalaryExpectation {
    
    private Integer min;
    private Integer max;
    private String currency = "USD";

    // Constructors
    public SalaryExpectation() {}

    public SalaryExpectation(Integer min, Integer max, String currency) {
        this.min = min;
        this.max = max;
        this.currency = currency;
    }

    // Getters and Setters
    public Integer getMin() {
        return min;
    }

    public void setMin(Integer min) {
        this.min = min;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getFormattedRange() {
        if (min == null && max == null) {
            return "Not specified";
        }
        if (min == null) {
            return "Up to " + formatAmount(max) + " " + currency;
        }
        if (max == null) {
            return "From " + formatAmount(min) + " " + currency;
        }
        return formatAmount(min) + " - " + formatAmount(max) + " " + currency;
    }

    private String formatAmount(Integer amount) {
        if (amount == null) return "0";
        return String.format("%,d", amount);
    }

    @Override
    public String toString() {
        return getFormattedRange();
    }
}
