package com.careeralgo.controller;

import com.careeralgo.config.WebSocketConfig;
import com.careeralgo.service.RealTimeNotificationService;
import com.careeralgo.service.WebSocketAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.annotation.SendToUser;
import org.springframework.stereotype.Controller;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * WebSocket controller for real-time messaging
 */
@Controller
public class WebSocketController {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketController.class);

    @Autowired
    private RealTimeNotificationService realTimeNotificationService;

    @Autowired
    private WebSocketAuthService webSocketAuthService;

    /**
     * Handle user connection
     */
    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        try {
            SimpMessageHeaderAccessor headerAccessor = SimpMessageHeaderAccessor.wrap(event.getMessage());
            Principal user = headerAccessor.getUser();
            
            if (user instanceof WebSocketConfig.WebSocketPrincipal webSocketPrincipal) {
                String userId = webSocketPrincipal.getUserId();
                logger.info("User connected via WebSocket: {}", userId);
                
                // Send connection confirmation
                realTimeNotificationService.sendConnectionStatus(userId, true);
                
                // Update session activity
                webSocketAuthService.updateSessionActivity(userId);
            }
            
        } catch (Exception e) {
            logger.error("Error handling WebSocket connection", e);
        }
    }

    /**
     * Handle user disconnection
     */
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        try {
            SimpMessageHeaderAccessor headerAccessor = SimpMessageHeaderAccessor.wrap(event.getMessage());
            Principal user = headerAccessor.getUser();
            
            if (user instanceof WebSocketConfig.WebSocketPrincipal webSocketPrincipal) {
                String userId = webSocketPrincipal.getUserId();
                logger.info("User disconnected from WebSocket: {}", userId);
                
                // Remove session
                webSocketAuthService.removeSession(userId);
                
                // Send disconnection status
                realTimeNotificationService.sendConnectionStatus(userId, false);
            }
            
        } catch (Exception e) {
            logger.error("Error handling WebSocket disconnection", e);
        }
    }

    /**
     * Handle ping messages for connection keep-alive
     */
    @MessageMapping("/ping")
    @SendToUser("/queue/pong")
    public Map<String, Object> handlePing(Principal principal) {
        if (principal instanceof WebSocketConfig.WebSocketPrincipal webSocketPrincipal) {
            String userId = webSocketPrincipal.getUserId();
            
            // Update session activity
            webSocketAuthService.updateSessionActivity(userId);
            
            return Map.of(
                    "type", "PONG",
                    "timestamp", LocalDateTime.now().toString(),
                    "userId", userId
            );
        }
        
        return Map.of(
                "type", "PONG",
                "timestamp", LocalDateTime.now().toString()
        );
    }

    /**
     * Handle user activity updates
     */
    @MessageMapping("/activity")
    public void handleUserActivity(@Payload Map<String, Object> activity, Principal principal) {
        if (principal instanceof WebSocketConfig.WebSocketPrincipal webSocketPrincipal) {
            String userId = webSocketPrincipal.getUserId();
            String activityType = (String) activity.get("activity");
            
            // Update session activity
            webSocketAuthService.updateSessionActivity(userId);
            
            // Send activity update
            realTimeNotificationService.sendUserActivityUpdate(userId, activityType);
            
            logger.debug("User activity update: {} - {}", userId, activityType);
        }
    }

    /**
     * Handle typing indicators
     */
    @MessageMapping("/typing")
    public void handleTypingIndicator(@Payload Map<String, Object> typingData, Principal principal) {
        if (principal instanceof WebSocketConfig.WebSocketPrincipal webSocketPrincipal) {
            String userId = webSocketPrincipal.getUserId();
            String targetUserId = (String) typingData.get("targetUserId");
            Boolean isTyping = (Boolean) typingData.get("isTyping");
            
            if (targetUserId != null && isTyping != null) {
                realTimeNotificationService.sendTypingIndicator(userId, targetUserId, isTyping);
            }
        }
    }

    /**
     * Handle subscription to notification channels
     */
    @MessageMapping("/subscribe")
    @SendToUser("/queue/subscription-status")
    public Map<String, Object> handleSubscription(@Payload Map<String, Object> subscriptionData, Principal principal) {
        if (principal instanceof WebSocketConfig.WebSocketPrincipal webSocketPrincipal) {
            String userId = webSocketPrincipal.getUserId();
            String channel = (String) subscriptionData.get("channel");
            
            logger.info("User {} subscribed to channel: {}", userId, channel);
            
            return Map.of(
                    "type", "SUBSCRIPTION_CONFIRMED",
                    "channel", channel,
                    "userId", userId,
                    "timestamp", LocalDateTime.now().toString()
            );
        }
        
        return Map.of(
                "type", "SUBSCRIPTION_FAILED",
                "reason", "Authentication required"
        );
    }

    /**
     * Handle unsubscription from notification channels
     */
    @MessageMapping("/unsubscribe")
    @SendToUser("/queue/subscription-status")
    public Map<String, Object> handleUnsubscription(@Payload Map<String, Object> unsubscriptionData, Principal principal) {
        if (principal instanceof WebSocketConfig.WebSocketPrincipal webSocketPrincipal) {
            String userId = webSocketPrincipal.getUserId();
            String channel = (String) unsubscriptionData.get("channel");
            
            logger.info("User {} unsubscribed from channel: {}", userId, channel);
            
            return Map.of(
                    "type", "UNSUBSCRIPTION_CONFIRMED",
                    "channel", channel,
                    "userId", userId,
                    "timestamp", LocalDateTime.now().toString()
            );
        }
        
        return Map.of(
                "type", "UNSUBSCRIPTION_FAILED",
                "reason", "Authentication required"
        );
    }

    /**
     * Handle client status requests
     */
    @MessageMapping("/status")
    @SendToUser("/queue/status")
    public Map<String, Object> handleStatusRequest(Principal principal) {
        if (principal instanceof WebSocketConfig.WebSocketPrincipal webSocketPrincipal) {
            String userId = webSocketPrincipal.getUserId();
            WebSocketAuthService.WebSocketSession session = webSocketAuthService.getActiveSession(userId);
            
            if (session != null) {
                return Map.of(
                        "type", "STATUS_RESPONSE",
                        "userId", userId,
                        "connected", true,
                        "connectedAt", session.getConnectedAt(),
                        "connectionDuration", session.getConnectionDuration(),
                        "lastActivity", session.getLastActivity(),
                        "timestamp", LocalDateTime.now().toString()
                );
            }
        }
        
        return Map.of(
                "type", "STATUS_RESPONSE",
                "connected", false,
                "timestamp", LocalDateTime.now().toString()
        );
    }

    /**
     * Handle broadcast messages (admin only)
     */
    @MessageMapping("/broadcast")
    @SendTo("/topic/broadcast")
    public Map<String, Object> handleBroadcast(@Payload Map<String, Object> broadcastData, Principal principal) {
        // TODO: Add admin role check
        
        String title = (String) broadcastData.get("title");
        String message = (String) broadcastData.get("message");
        String type = (String) broadcastData.get("type");
        
        if (title != null && message != null) {
            return Map.of(
                    "type", "BROADCAST",
                    "title", title,
                    "message", message,
                    "broadcastType", type != null ? type : "GENERAL",
                    "timestamp", LocalDateTime.now().toString()
            );
        }
        
        return Map.of(
                "type", "BROADCAST_ERROR",
                "message", "Invalid broadcast data"
        );
    }

    /**
     * Handle system status updates
     */
    @MessageMapping("/system-status")
    @SendTo("/topic/system-status")
    public Map<String, Object> handleSystemStatus(@Payload Map<String, Object> statusData, Principal principal) {
        // TODO: Add admin role check
        
        String status = (String) statusData.get("status");
        String message = (String) statusData.get("message");
        
        if (status != null && message != null) {
            return Map.of(
                    "type", "SYSTEM_STATUS",
                    "status", status,
                    "message", message,
                    "timestamp", LocalDateTime.now().toString()
            );
        }
        
        return Map.of(
                "type", "SYSTEM_STATUS_ERROR",
                "message", "Invalid status data"
        );
    }

    /**
     * Handle error messages
     */
    @MessageMapping("/error")
    @SendToUser("/queue/errors")
    public Map<String, Object> handleError(@Payload Map<String, Object> errorData, Principal principal) {
        if (principal instanceof WebSocketConfig.WebSocketPrincipal webSocketPrincipal) {
            String userId = webSocketPrincipal.getUserId();
            String errorType = (String) errorData.get("errorType");
            String errorMessage = (String) errorData.get("message");
            
            logger.warn("WebSocket error from user {}: {} - {}", userId, errorType, errorMessage);
            
            return Map.of(
                    "type", "ERROR_ACKNOWLEDGED",
                    "errorType", errorType,
                    "message", "Error received and logged",
                    "timestamp", LocalDateTime.now().toString()
            );
        }
        
        return Map.of(
                "type", "ERROR_FAILED",
                "message", "Authentication required"
        );
    }
}
