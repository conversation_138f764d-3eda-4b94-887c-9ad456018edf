package com.careeralgo.constant;

/**
 * Experience levels for users and jobs
 */
public enum ExperienceLevel {
    ENTRY_LEVEL("ENTRY_LEVEL"),
    MID_LEVEL("MID_LEVEL"),
    SENIOR_LEVEL("SENIOR_LEVEL"),
    LEAD_LEVEL("LEAD_LEVEL"),
    EXECUTIVE_LEVEL("EXECUTIVE_LEVEL");

    private final String value;

    ExperienceLevel(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
