package com.careeralgo.dto;

import com.careeralgo.constant.FileType;
import com.careeralgo.model.AIAnalysis;
import com.careeralgo.model.ParsedContent;
import com.careeralgo.model.Resume;
import com.careeralgo.model.ResumeCustomization;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO for resume response data
 */
public class ResumeResponse {

    private String id;
    private String userId;
    private String fileName;
    private String originalFileName;
    private String cloudinaryUrl;
    private FileType fileType;
    private Long fileSize;
    private boolean isActive;
    private boolean isPrimary;
    private Integer version;
    private String templateId;
    private ParsedContent parsedContent;
    private AIAnalysis aiAnalysis;
    private List<ResumeCustomization> customizations;
    private Integer downloadCount;
    private String shareableLink;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public ResumeResponse() {}

    public ResumeResponse(Resume resume) {
        this.id = resume.getId();
        this.userId = resume.getUserId();
        this.fileName = resume.getFileName();
        this.originalFileName = resume.getOriginalFileName();
        this.cloudinaryUrl = resume.getCloudinaryUrl();
        this.fileType = resume.getFileType();
        this.fileSize = resume.getFileSize();
        this.isActive = resume.isActive();
        this.isPrimary = resume.isPrimary();
        this.version = resume.getVersion();
        this.templateId = resume.getTemplateId();
        this.parsedContent = resume.getParsedContent();
        this.aiAnalysis = resume.getAiAnalysis();
        this.customizations = resume.getCustomizations();
        this.downloadCount = resume.getDownloadCount();
        this.shareableLink = resume.getShareableLink();
        this.createdAt = resume.getCreatedAt();
        this.updatedAt = resume.getUpdatedAt();
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOriginalFileName() {
        return originalFileName;
    }

    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }

    public String getCloudinaryUrl() {
        return cloudinaryUrl;
    }

    public void setCloudinaryUrl(String cloudinaryUrl) {
        this.cloudinaryUrl = cloudinaryUrl;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public boolean isPrimary() {
        return isPrimary;
    }

    public void setPrimary(boolean primary) {
        isPrimary = primary;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public ParsedContent getParsedContent() {
        return parsedContent;
    }

    public void setParsedContent(ParsedContent parsedContent) {
        this.parsedContent = parsedContent;
    }

    public AIAnalysis getAiAnalysis() {
        return aiAnalysis;
    }

    public void setAiAnalysis(AIAnalysis aiAnalysis) {
        this.aiAnalysis = aiAnalysis;
    }

    public List<ResumeCustomization> getCustomizations() {
        return customizations;
    }

    public void setCustomizations(List<ResumeCustomization> customizations) {
        this.customizations = customizations;
    }

    public Integer getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(Integer downloadCount) {
        this.downloadCount = downloadCount;
    }

    public String getShareableLink() {
        return shareableLink;
    }

    public void setShareableLink(String shareableLink) {
        this.shareableLink = shareableLink;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getFormattedFileSize() {
        if (fileSize == null) return "Unknown";
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }

    public boolean hasAIAnalysis() {
        return aiAnalysis != null;
    }

    public boolean hasParsedContent() {
        return parsedContent != null;
    }
}
