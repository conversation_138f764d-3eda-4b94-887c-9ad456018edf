package com.careeralgo.model;

import com.careeralgo.constant.ApplicationStatus;

import java.time.LocalDateTime;

/**
 * Status history entry for application tracking
 */
public class StatusHistory {
    
    private ApplicationStatus status;
    private LocalDateTime date;
    private String notes;
    private HistorySource source;

    public enum HistorySource {
        USER, SYSTEM, EXTERNAL
    }

    // Constructors
    public StatusHistory() {}

    public StatusHistory(ApplicationStatus status, String notes, HistorySource source) {
        this.status = status;
        this.notes = notes;
        this.source = source;
        this.date = LocalDateTime.now();
    }

    // Getters and Setters
    public ApplicationStatus getStatus() {
        return status;
    }

    public void setStatus(ApplicationStatus status) {
        this.status = status;
    }

    public LocalDateTime getDate() {
        return date;
    }

    public void setDate(LocalDateTime date) {
        this.date = date;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public HistorySource getSource() {
        return source;
    }

    public void setSource(HistorySource source) {
        this.source = source;
    }

    public String getDisplayText() {
        String statusText = status.getValue().replace("_", " ");
        return statusText + (notes != null && !notes.isEmpty() ? " - " + notes : "");
    }
}
