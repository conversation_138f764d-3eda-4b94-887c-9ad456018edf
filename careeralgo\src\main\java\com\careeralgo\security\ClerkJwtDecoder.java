package com.careeralgo.security;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;

import javax.crypto.SecretKey;
import java.time.Instant;
import java.util.Date;
import java.util.Map;

/**
 * Custom JWT decoder for Clerk tokens
 */
public class ClerkJwtDecoder implements JwtDecoder {

    private final SecretKey secretKey;

    public ClerkJwtDecoder(String publicKey) {
        // In a real implementation, you would parse the <PERSON> public key
        // For development, generate a secure key if placeholder is used
        if (publicKey == null || publicKey.isEmpty() || publicKey.startsWith("your-")) {
            // Generate a secure key for development
            this.secretKey = Keys.secretKeyFor(io.jsonwebtoken.SignatureAlgorithm.HS256);
        } else {
            // Ensure the key is long enough for HMAC-SHA256
            byte[] keyBytes = publicKey.getBytes();
            if (keyBytes.length < 32) { // 256 bits = 32 bytes
                // Pad the key to meet minimum requirements
                byte[] paddedKey = new byte[32];
                System.arraycopy(keyBytes, 0, paddedKey, 0, Math.min(keyBytes.length, 32));
                this.secretKey = Keys.hmacShaKeyFor(paddedKey);
            } else {
                this.secretKey = Keys.hmacShaKeyFor(keyBytes);
            }
        }
    }

    @Override
    public Jwt decode(String token) throws JwtException {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            return createJwt(token, claims);
        } catch (Exception e) {
            throw new JwtException("Failed to decode JWT token", e);
        }
    }

    private Jwt createJwt(String token, Claims claims) {
        Instant issuedAt = claims.getIssuedAt() != null ?
                claims.getIssuedAt().toInstant() : Instant.now();
        Instant expiresAt = claims.getExpiration() != null ?
                claims.getExpiration().toInstant() : Instant.now().plus(1, java.time.temporal.ChronoUnit.HOURS);

        Map<String, Object> headers = Map.of(
                "alg", "HS256",
                "typ", "JWT"
        );

        return new Jwt(
                token,
                issuedAt,
                expiresAt,
                headers,
                claims
        );
    }
}
