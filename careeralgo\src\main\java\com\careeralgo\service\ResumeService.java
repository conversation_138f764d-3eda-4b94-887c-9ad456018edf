package com.careeralgo.service;

import com.careeralgo.constant.FileType;
import com.careeralgo.dto.ResumeResponse;
import com.careeralgo.dto.ResumeUploadRequest;
import com.careeralgo.exception.ResourceNotFoundException;
import com.careeralgo.model.Resume;
import com.careeralgo.model.User;
import com.careeralgo.repository.ResumeRepository;
import com.careeralgo.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service for resume management operations
 */
@Service
public class ResumeService {

    private static final Logger logger = LoggerFactory.getLogger(ResumeService.class);
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

    @Autowired
    private ResumeRepository resumeRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DocumentParsingService documentParsingService;

    @Autowired(required = false)
    private CloudinaryService cloudinaryService;

    /**
     * Get user's resumes with pagination
     */
    public Page<ResumeResponse> getUserResumes(Authentication authentication, int page, int size) {
        String userId = getUserId(authentication);
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());

        Page<Resume> resumes = resumeRepository.findByUserIdAndIsActiveTrue(userId, pageable);
        return resumes.map(ResumeResponse::new);
    }

    /**
     * Get specific resume by ID
     */
    public ResumeResponse getResumeById(Authentication authentication, String resumeId) {
        String userId = getUserId(authentication);
        Resume resume = findResumeByIdAndUser(resumeId, userId);
        return new ResumeResponse(resume);
    }

    /**
     * Upload and process new resume
     */
    public ResumeResponse uploadResume(Authentication authentication, MultipartFile file,
                                     ResumeUploadRequest request) {
        String userId = getUserId(authentication);

        // Validate file
        validateFile(file);

        try {
            // Upload to Cloudinary (or create mock result if service not available)
            CloudinaryService.UploadResult uploadResult;
            if (cloudinaryService != null) {
                uploadResult = cloudinaryService.uploadResume(file, userId);
            } else {
                logger.warn("CloudinaryService not available - creating mock upload result");
                uploadResult = new CloudinaryService.UploadResult(
                    "http://localhost:8080/mock/resume/" + userId,
                    "mock_" + userId
                );
            }

            // Create resume record
            Resume resume = new Resume();
            resume.setUserId(userId);
            resume.setFileName(generateFileName(file.getOriginalFilename()));
            resume.setOriginalFileName(file.getOriginalFilename());
            resume.setCloudinaryUrl(uploadResult.getUrl());
            resume.setCloudinaryPublicId(uploadResult.getPublicId());
            resume.setFileType(FileType.fromMimeType(file.getContentType()));
            resume.setFileSize(file.getSize());

            if (request != null) {
                resume.setTemplateId(request.getTemplateId());
                resume.setPrimary(request.isPrimary());
            }

            // If this is set as primary, unset other primary resumes
            if (resume.isPrimary()) {
                unsetOtherPrimaryResumes(userId);
            }

            // Save resume
            Resume savedResume = resumeRepository.save(resume);

            // Parse document asynchronously
            documentParsingService.parseResumeAsync(savedResume.getId());

            logger.info("Uploaded resume for user: {}, file: {}", userId, file.getOriginalFilename());
            return new ResumeResponse(savedResume);

        } catch (Exception e) {
            logger.error("Error uploading resume", e);
            throw new RuntimeException("Failed to upload resume: " + e.getMessage());
        }
    }

    /**
     * Update resume information
     */
    public ResumeResponse updateResume(Authentication authentication, String resumeId,
                                     ResumeUploadRequest request) {
        String userId = getUserId(authentication);
        Resume resume = findResumeByIdAndUser(resumeId, userId);

        if (request.getTemplateId() != null) {
            resume.setTemplateId(request.getTemplateId());
        }

        if (request.isPrimary() != resume.isPrimary()) {
            if (request.isPrimary()) {
                unsetOtherPrimaryResumes(userId);
            }
            resume.setPrimary(request.isPrimary());
        }

        Resume savedResume = resumeRepository.save(resume);
        logger.info("Updated resume: {} for user: {}", resumeId, userId);

        return new ResumeResponse(savedResume);
    }

    /**
     * Delete resume
     */
    public void deleteResume(Authentication authentication, String resumeId) {
        String userId = getUserId(authentication);
        Resume resume = findResumeByIdAndUser(resumeId, userId);

        try {
            // Delete from Cloudinary (if service is available)
            if (resume.getCloudinaryPublicId() != null && cloudinaryService != null) {
                cloudinaryService.deleteFile(resume.getCloudinaryPublicId());
            } else if (resume.getCloudinaryPublicId() != null) {
                logger.warn("CloudinaryService not available - skipping file deletion for publicId: {}",
                    resume.getCloudinaryPublicId());
            }

            // Soft delete
            resume.setActive(false);
            resumeRepository.save(resume);

            logger.info("Deleted resume: {} for user: {}", resumeId, userId);

        } catch (Exception e) {
            logger.error("Error deleting resume", e);
            throw new RuntimeException("Failed to delete resume: " + e.getMessage());
        }
    }

    /**
     * Set resume as primary
     */
    public ResumeResponse setPrimaryResume(Authentication authentication, String resumeId) {
        String userId = getUserId(authentication);
        Resume resume = findResumeByIdAndUser(resumeId, userId);

        // Unset other primary resumes
        unsetOtherPrimaryResumes(userId);

        // Set this as primary
        resume.setPrimary(true);
        Resume savedResume = resumeRepository.save(resume);

        logger.info("Set primary resume: {} for user: {}", resumeId, userId);
        return new ResumeResponse(savedResume);
    }

    /**
     * Get user's primary resume
     */
    public Optional<ResumeResponse> getPrimaryResume(Authentication authentication) {
        String userId = getUserId(authentication);
        Optional<Resume> primaryResume = resumeRepository.findByUserIdAndIsPrimaryTrue(userId);
        return primaryResume.map(ResumeResponse::new);
    }

    /**
     * Duplicate resume
     */
    public ResumeResponse duplicateResume(Authentication authentication, String resumeId) {
        String userId = getUserId(authentication);
        Resume originalResume = findResumeByIdAndUser(resumeId, userId);

        // Create duplicate
        Resume duplicate = new Resume();
        duplicate.setUserId(userId);
        duplicate.setFileName("Copy of " + originalResume.getFileName());
        duplicate.setOriginalFileName("Copy of " + originalResume.getOriginalFileName());
        duplicate.setCloudinaryUrl(originalResume.getCloudinaryUrl());
        duplicate.setCloudinaryPublicId(originalResume.getCloudinaryPublicId());
        duplicate.setFileType(originalResume.getFileType());
        duplicate.setFileSize(originalResume.getFileSize());
        duplicate.setTemplateId(originalResume.getTemplateId());
        duplicate.setParsedContent(originalResume.getParsedContent());
        duplicate.setVersion(originalResume.getVersion() + 1);
        duplicate.setPrimary(false); // Duplicates are never primary by default

        Resume savedDuplicate = resumeRepository.save(duplicate);
        logger.info("Duplicated resume: {} for user: {}", resumeId, userId);

        return new ResumeResponse(savedDuplicate);
    }

    /**
     * Generate shareable link for resume
     */
    public String generateShareableLink(Authentication authentication, String resumeId) {
        String userId = getUserId(authentication);
        Resume resume = findResumeByIdAndUser(resumeId, userId);

        String shareableToken = UUID.randomUUID().toString();
        String shareableLink = "https://careeralgo.com/resume/share/" + shareableToken;

        resume.setShareableLink(shareableLink);
        resumeRepository.save(resume);

        logger.info("Generated shareable link for resume: {} for user: {}", resumeId, userId);
        return shareableLink;
    }

    /**
     * Get resume by shareable link (public access)
     */
    public ResumeResponse getResumeByShareableLink(String shareableLink) {
        Resume resume = resumeRepository.findByShareableLink(shareableLink)
                .orElseThrow(() -> new ResourceNotFoundException("Resume not found for shareable link"));

        // Increment view count (could be tracked separately)
        return new ResumeResponse(resume);
    }

    /**
     * Get resume download URL
     */
    public String getResumeDownloadUrl(Authentication authentication, String resumeId) {
        String userId = getUserId(authentication);
        Resume resume = findResumeByIdAndUser(resumeId, userId);

        // Increment download count
        resume.incrementDownloadCount();
        resumeRepository.save(resume);

        return resume.getCloudinaryUrl();
    }

    /**
     * Search user's resumes
     */
    public List<ResumeResponse> searchResumes(Authentication authentication, String searchTerm) {
        String userId = getUserId(authentication);

        // Search in parsed content
        List<Resume> resumes = resumeRepository.searchResumesByContent(searchTerm)
                .stream()
                .filter(resume -> resume.getUserId().equals(userId) && resume.isActive())
                .collect(Collectors.toList());

        return resumes.stream()
                .map(ResumeResponse::new)
                .collect(Collectors.toList());
    }

    // Helper methods

    private String getUserId(Authentication authentication) {
        if (authentication.getPrincipal() instanceof Jwt jwt) {
            String clerkUserId = jwt.getSubject();
            User user = userRepository.findByClerkUserId(clerkUserId)
                    .orElseThrow(() -> new ResourceNotFoundException("User not found"));
            return user.getId();
        }
        throw new IllegalArgumentException("Invalid authentication type");
    }

    private Resume findResumeByIdAndUser(String resumeId, String userId) {
        Resume resume = resumeRepository.findById(resumeId)
                .orElseThrow(() -> new ResourceNotFoundException("Resume not found: " + resumeId));

        if (!resume.getUserId().equals(userId)) {
            throw new ResourceNotFoundException("Resume not found: " + resumeId);
        }

        if (!resume.isActive()) {
            throw new ResourceNotFoundException("Resume not found: " + resumeId);
        }

        return resume;
    }

    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("File cannot be empty");
        }

        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("File size exceeds maximum limit of 10MB");
        }

        String contentType = file.getContentType();
        if (contentType == null || (!contentType.equals("application/pdf") &&
            !contentType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document") &&
            !contentType.equals("application/msword"))) {
            throw new IllegalArgumentException("Only PDF and Word documents are supported");
        }
    }

    private String generateFileName(String originalFilename) {
        if (originalFilename == null) {
            return "resume_" + System.currentTimeMillis();
        }

        String nameWithoutExtension = originalFilename.contains(".") ?
                originalFilename.substring(0, originalFilename.lastIndexOf(".")) : originalFilename;
        String extension = originalFilename.contains(".") ?
                originalFilename.substring(originalFilename.lastIndexOf(".")) : "";

        return nameWithoutExtension + "_" + System.currentTimeMillis() + extension;
    }

    private void unsetOtherPrimaryResumes(String userId) {
        Optional<Resume> currentPrimary = resumeRepository.findByUserIdAndIsPrimaryTrue(userId);
        if (currentPrimary.isPresent()) {
            Resume primary = currentPrimary.get();
            primary.setPrimary(false);
            resumeRepository.save(primary);
        }
    }
}
