package com.careeralgo.model;

import java.time.LocalDateTime;

/**
 * User integrations with external services
 */
public class Integrations {

    private LinkedInIntegration linkedIn;
    private GitHubIntegration gitHub;
    private GoogleIntegration google;

    public Integrations() {
        this.linkedIn = new LinkedInIntegration();
        this.gitHub = new GitHubIntegration();
        this.google = new GoogleIntegration();
    }

    // Getters and Setters
    public LinkedInIntegration getLinkedIn() {
        return linkedIn;
    }

    public void setLinkedIn(LinkedInIntegration linkedIn) {
        this.linkedIn = linkedIn;
    }

    public GitHubIntegration getGitHub() {
        return gitHub;
    }

    public void setGitHub(GitHubIntegration gitHub) {
        this.gitHub = gitHub;
    }

    public GoogleIntegration getGoogle() {
        return google;
    }

    public void setGoogle(GoogleIntegration google) {
        this.google = google;
    }

    // Convenience methods
    public boolean isLinkedInConnected() {
        return linkedIn != null && linkedIn.isConnected();
    }

    public void setLinkedInConnected(boolean connected) {
        if (linkedIn == null) {
            linkedIn = new LinkedInIntegration();
        }
        linkedIn.setConnected(connected);
    }

    public void setLinkedInProfileUrl(String profileUrl) {
        if (linkedIn == null) {
            linkedIn = new LinkedInIntegration();
        }
        linkedIn.setProfileUrl(profileUrl);
    }

    public boolean isGitHubConnected() {
        return gitHub != null && gitHub.isConnected();
    }

    public boolean isGoogleConnected() {
        return google != null && google.isConnected();
    }

    // Nested classes for specific integrations
    public static class LinkedInIntegration {
        private boolean connected = false;
        private String accessToken;
        private String refreshToken;
        private LocalDateTime connectedAt;
        private LocalDateTime lastSyncAt;
        private String profileId;
        private String profileUrl;

        public boolean isConnected() {
            return connected;
        }

        public void setConnected(boolean connected) {
            this.connected = connected;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public void setRefreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
        }

        public LocalDateTime getConnectedAt() {
            return connectedAt;
        }

        public void setConnectedAt(LocalDateTime connectedAt) {
            this.connectedAt = connectedAt;
        }

        public LocalDateTime getLastSyncAt() {
            return lastSyncAt;
        }

        public void setLastSyncAt(LocalDateTime lastSyncAt) {
            this.lastSyncAt = lastSyncAt;
        }

        public String getProfileId() {
            return profileId;
        }

        public void setProfileId(String profileId) {
            this.profileId = profileId;
        }

        public String getProfileUrl() {
            return profileUrl;
        }

        public void setProfileUrl(String profileUrl) {
            this.profileUrl = profileUrl;
        }
    }

    public static class GitHubIntegration {
        private boolean connected = false;
        private String accessToken;
        private LocalDateTime connectedAt;
        private LocalDateTime lastSyncAt;
        private String username;
        private String profileUrl;

        public boolean isConnected() {
            return connected;
        }

        public void setConnected(boolean connected) {
            this.connected = connected;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public LocalDateTime getConnectedAt() {
            return connectedAt;
        }

        public void setConnectedAt(LocalDateTime connectedAt) {
            this.connectedAt = connectedAt;
        }

        public LocalDateTime getLastSyncAt() {
            return lastSyncAt;
        }

        public void setLastSyncAt(LocalDateTime lastSyncAt) {
            this.lastSyncAt = lastSyncAt;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getProfileUrl() {
            return profileUrl;
        }

        public void setProfileUrl(String profileUrl) {
            this.profileUrl = profileUrl;
        }
    }

    public static class GoogleIntegration {
        private boolean connected = false;
        private String accessToken;
        private String refreshToken;
        private LocalDateTime connectedAt;
        private LocalDateTime lastSyncAt;
        private String email;

        public boolean isConnected() {
            return connected;
        }

        public void setConnected(boolean connected) {
            this.connected = connected;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public void setRefreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
        }

        public LocalDateTime getConnectedAt() {
            return connectedAt;
        }

        public void setConnectedAt(LocalDateTime connectedAt) {
            this.connectedAt = connectedAt;
        }

        public LocalDateTime getLastSyncAt() {
            return lastSyncAt;
        }

        public void setLastSyncAt(LocalDateTime lastSyncAt) {
            this.lastSyncAt = lastSyncAt;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }
    }
}
