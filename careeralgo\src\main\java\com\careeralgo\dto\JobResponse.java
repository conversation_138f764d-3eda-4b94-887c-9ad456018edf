package com.careeralgo.dto;

import com.careeralgo.constant.ExperienceLevel;
import com.careeralgo.model.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO for job response data
 */
public class JobResponse {

    private String id;
    private String externalId;
    private Job.JobSource source;
    private String title;
    private String slug;
    private Company company;
    private JobLocation location;
    private String description;
    private List<String> requirements;
    private List<String> responsibilities;
    private Salary salary;
    private Job.EmploymentType employmentType;
    private ExperienceLevel experienceLevel;
    private List<String> skills;
    private List<String> benefits;
    private LocalDateTime applicationDeadline;
    private String applicationUrl;
    private String contactEmail;
    private boolean isActive;
    private boolean isFeatured;
    private Integer viewCount;
    private Integer applicationCount;
    private List<String> tags;
    private LocalDateTime postedDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public JobResponse() {}

    public JobResponse(Job job) {
        this.id = job.getId();
        this.externalId = job.getExternalId();
        this.source = job.getSource();
        this.title = job.getTitle();
        this.slug = job.getSlug();
        this.company = job.getCompany();
        this.location = job.getLocation();
        this.description = job.getDescription();
        this.requirements = job.getRequirements();
        this.responsibilities = job.getResponsibilities();
        this.salary = job.getSalary();
        this.employmentType = job.getEmploymentType();
        this.experienceLevel = job.getExperienceLevel();
        this.skills = job.getSkills();
        this.benefits = job.getBenefits();
        this.applicationDeadline = job.getApplicationDeadline();
        this.applicationUrl = job.getApplicationUrl();
        this.contactEmail = job.getContactEmail();
        this.isActive = job.isActive();
        this.isFeatured = job.isFeatured();
        this.viewCount = job.getViewCount();
        this.applicationCount = job.getApplicationCount();
        this.tags = job.getTags();
        this.postedDate = job.getPostedDate();
        this.createdAt = job.getCreatedAt();
        this.updatedAt = job.getUpdatedAt();
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public Job.JobSource getSource() {
        return source;
    }

    public void setSource(Job.JobSource source) {
        this.source = source;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSlug() {
        return slug;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public Company getCompany() {
        return company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    public JobLocation getLocation() {
        return location;
    }

    public void setLocation(JobLocation location) {
        this.location = location;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getRequirements() {
        return requirements;
    }

    public void setRequirements(List<String> requirements) {
        this.requirements = requirements;
    }

    public List<String> getResponsibilities() {
        return responsibilities;
    }

    public void setResponsibilities(List<String> responsibilities) {
        this.responsibilities = responsibilities;
    }

    public Salary getSalary() {
        return salary;
    }

    public void setSalary(Salary salary) {
        this.salary = salary;
    }

    public Job.EmploymentType getEmploymentType() {
        return employmentType;
    }

    public void setEmploymentType(Job.EmploymentType employmentType) {
        this.employmentType = employmentType;
    }

    public ExperienceLevel getExperienceLevel() {
        return experienceLevel;
    }

    public void setExperienceLevel(ExperienceLevel experienceLevel) {
        this.experienceLevel = experienceLevel;
    }

    public List<String> getSkills() {
        return skills;
    }

    public void setSkills(List<String> skills) {
        this.skills = skills;
    }

    public List<String> getBenefits() {
        return benefits;
    }

    public void setBenefits(List<String> benefits) {
        this.benefits = benefits;
    }

    public LocalDateTime getApplicationDeadline() {
        return applicationDeadline;
    }

    public void setApplicationDeadline(LocalDateTime applicationDeadline) {
        this.applicationDeadline = applicationDeadline;
    }

    public String getApplicationUrl() {
        return applicationUrl;
    }

    public void setApplicationUrl(String applicationUrl) {
        this.applicationUrl = applicationUrl;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public boolean isFeatured() {
        return isFeatured;
    }

    public void setFeatured(boolean featured) {
        isFeatured = featured;
    }

    public Integer getViewCount() {
        return viewCount;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }

    public Integer getApplicationCount() {
        return applicationCount;
    }

    public void setApplicationCount(Integer applicationCount) {
        this.applicationCount = applicationCount;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public LocalDateTime getPostedDate() {
        return postedDate;
    }

    public void setPostedDate(LocalDateTime postedDate) {
        this.postedDate = postedDate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Helper methods
    public boolean isExpired() {
        return applicationDeadline != null && applicationDeadline.isBefore(LocalDateTime.now());
    }

    public String getCompanyName() {
        return company != null ? company.getName() : null;
    }

    public String getLocationDisplay() {
        return location != null ? location.getDisplayLocation() : null;
    }

    public String getSalaryDisplay() {
        return salary != null ? salary.getFormattedRange() : "Not disclosed";
    }

    public String getSkillsDisplay() {
        return skills != null ? String.join(", ", skills) : "";
    }
}
