package com.careeralgo.controller;

import com.careeralgo.dto.UserProfileUpdateRequest;
import com.careeralgo.dto.UserResponse;
import com.careeralgo.model.Preferences;
import com.careeralgo.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * REST controller for user management operations
 */
@RestController
@RequestMapping("/users")
@Tag(name = "User Management", description = "APIs for user profile and account management")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * Get current user profile
     */
    @GetMapping("/me")
    @Operation(summary = "Get current user profile", description = "Retrieve the authenticated user's profile information")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User profile retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Invalid or missing authentication"),
            @ApiResponse(responseCode = "404", description = "User not found")
    })
    public ResponseEntity<UserResponse> getCurrentUser(Authentication authentication) {
        UserResponse user = userService.getCurrentUser(authentication);
        return ResponseEntity.ok(user);
    }

    /**
     * Update user profile
     */
    @PutMapping("/me")
    @Operation(summary = "Update user profile", description = "Update the authenticated user's profile information")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Profile updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "User not found")
    })
    public ResponseEntity<UserResponse> updateUserProfile(
            Authentication authentication,
            @Valid @RequestBody UserProfileUpdateRequest request) {
        UserResponse updatedUser = userService.updateUserProfile(authentication, request);
        return ResponseEntity.ok(updatedUser);
    }

    /**
     * Update user preferences
     */
    @PutMapping("/me/preferences")
    @Operation(summary = "Update user preferences", description = "Update the authenticated user's preferences")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Preferences updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "User not found")
    })
    public ResponseEntity<UserResponse> updateUserPreferences(
            Authentication authentication,
            @Valid @RequestBody Preferences preferences) {
        UserResponse updatedUser = userService.updateUserPreferences(authentication, preferences);
        return ResponseEntity.ok(updatedUser);
    }

    /**
     * Get user statistics
     */
    @GetMapping("/me/stats")
    @Operation(summary = "Get user statistics", description = "Retrieve statistics and metrics for the authenticated user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "User not found")
    })
    public ResponseEntity<UserService.UserStatsResponse> getUserStats(Authentication authentication) {
        UserService.UserStatsResponse stats = userService.getUserStats(authentication);
        return ResponseEntity.ok(stats);
    }

    /**
     * Upload profile picture
     */
    @PostMapping("/me/avatar")
    @Operation(summary = "Upload profile picture", description = "Upload a new profile picture for the authenticated user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Profile picture uploaded successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid file or request"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "413", description = "File too large"),
            @ApiResponse(responseCode = "415", description = "Unsupported file type")
    })
    public ResponseEntity<UserResponse> uploadProfilePicture(
            Authentication authentication,
            @RequestParam("file") MultipartFile file) {
        
        // TODO: Implement file upload to Cloudinary
        // For now, return a placeholder response
        String profilePictureUrl = "https://example.com/placeholder.jpg";
        UserResponse updatedUser = userService.updateProfilePicture(authentication, profilePictureUrl);
        return ResponseEntity.ok(updatedUser);
    }

    /**
     * Remove profile picture
     */
    @DeleteMapping("/me/avatar")
    @Operation(summary = "Remove profile picture", description = "Remove the authenticated user's profile picture")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Profile picture removed successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "User not found")
    })
    public ResponseEntity<UserResponse> removeProfilePicture(Authentication authentication) {
        UserResponse updatedUser = userService.removeProfilePicture(authentication);
        return ResponseEntity.ok(updatedUser);
    }

    /**
     * Deactivate user account
     */
    @PostMapping("/me/deactivate")
    @Operation(summary = "Deactivate account", description = "Deactivate the authenticated user's account")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Account deactivated successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "User not found")
    })
    public ResponseEntity<String> deactivateAccount(Authentication authentication) {
        userService.deactivateUser(authentication);
        return ResponseEntity.ok("Account deactivated successfully");
    }

    /**
     * Export user data (GDPR compliance)
     */
    @PostMapping("/me/export-data")
    @Operation(summary = "Export user data", description = "Export all user data for GDPR compliance")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data export initiated"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "User not found")
    })
    public ResponseEntity<String> exportUserData(Authentication authentication) {
        // TODO: Implement data export functionality
        // This should generate a comprehensive export of all user data
        return ResponseEntity.ok("Data export request submitted. You will receive an email when ready.");
    }
}
