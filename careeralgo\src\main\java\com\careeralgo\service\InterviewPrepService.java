package com.careeralgo.service;

import com.careeralgo.exception.ResourceNotFoundException;
import com.careeralgo.model.*;
import com.careeralgo.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for interview preparation functionality
 */
@Service
public class InterviewPrepService {

    private static final Logger logger = LoggerFactory.getLogger(InterviewPrepService.class);

    @Autowired
    private InterviewPrepRepository interviewPrepRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private OpenAIService openAIService;

    /**
     * Get user's interview prep sessions
     */
    public Page<InterviewPrep> getUserSessions(Authentication authentication, int page, int size, 
                                             InterviewPrep.SessionType type, Boolean completed) {
        String userId = getUserId(authentication);
        Pageable pageable = PageRequest.of(page, size);
        
        if (type != null && completed != null) {
            return interviewPrepRepository.findByUserIdAndTypeOrderByCreatedAtDesc(userId, type, pageable);
        } else if (type != null) {
            return interviewPrepRepository.findByUserIdAndTypeOrderByCreatedAtDesc(userId, type, pageable);
        } else if (completed != null) {
            return interviewPrepRepository.findByUserIdAndIsCompletedOrderByCreatedAtDesc(userId, completed, pageable);
        } else {
            return interviewPrepRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        }
    }

    /**
     * Get session by ID
     */
    public InterviewPrep getSessionById(Authentication authentication, String sessionId) {
        String userId = getUserId(authentication);
        return interviewPrepRepository.findByUserIdAndSessionId(userId, sessionId)
                .orElseThrow(() -> new ResourceNotFoundException("Interview prep session not found: " + sessionId));
    }

    /**
     * Create new interview prep session
     */
    public InterviewPrep createSession(Authentication authentication, CreateSessionRequest request) {
        String userId = getUserId(authentication);
        
        InterviewPrep session = new InterviewPrep(userId, request.getType(), request.getSessionName());
        session.setJobId(request.getJobId());
        
        // Generate questions based on session type and job
        List<InterviewPrep.InterviewQuestion> questions = generateQuestions(request, userId);
        session.setQuestions(questions);
        
        // Initialize session stats
        InterviewPrep.SessionStats stats = new InterviewPrep.SessionStats();
        stats.setTotalQuestions(questions.size());
        stats.setCompletedQuestions(0);
        session.setSessionStats(stats);
        
        // Add company research if job-specific
        if (request.getJobId() != null) {
            InterviewPrep.CompanyResearch research = generateCompanyResearch(request.getJobId());
            session.setCompanyResearch(research);
        }
        
        InterviewPrep savedSession = interviewPrepRepository.save(session);
        logger.info("Created interview prep session: {} for user: {}", savedSession.getSessionId(), userId);
        
        return savedSession;
    }

    /**
     * Answer a question in the session
     */
    public InterviewPrep answerQuestion(Authentication authentication, String sessionId, 
                                      AnswerQuestionRequest request) {
        String userId = getUserId(authentication);
        InterviewPrep session = getSessionById(authentication, sessionId);
        
        if (session.isCompleted()) {
            throw new IllegalStateException("Cannot answer questions in completed session");
        }
        
        // Find the question
        List<InterviewPrep.InterviewQuestion> questions = session.getQuestions();
        InterviewPrep.InterviewQuestion question = questions.stream()
                .filter(q -> q.getQuestionId().equals(request.getQuestionId()))
                .findFirst()
                .orElseThrow(() -> new ResourceNotFoundException("Question not found: " + request.getQuestionId()));
        
        // Update question with answer
        question.setUserAnswer(request.getAnswer());
        question.setAnswerDuration(request.getAnswerDuration());
        question.setAudioRecordingUrl(request.getAudioRecordingUrl());
        question.setAnsweredAt(LocalDateTime.now());
        question.setAttemptCount(question.getAttemptCount() + 1);
        
        // Generate AI evaluation
        InterviewPrep.AIEvaluation evaluation = generateAIEvaluation(question, session);
        question.setAiEvaluation(evaluation);
        
        // Update best score
        if (question.getBestScore() == null || evaluation.getOverallScore() > question.getBestScore()) {
            question.setBestScore(evaluation.getOverallScore());
        }
        
        // Update session stats
        updateSessionStats(session);
        
        InterviewPrep savedSession = interviewPrepRepository.save(session);
        logger.info("Answered question in session: {} for user: {}", sessionId, userId);
        
        return savedSession;
    }

    /**
     * Complete interview prep session
     */
    public InterviewPrep completeSession(Authentication authentication, String sessionId) {
        String userId = getUserId(authentication);
        InterviewPrep session = getSessionById(authentication, sessionId);
        
        if (session.isCompleted()) {
            throw new IllegalStateException("Session is already completed");
        }
        
        // Generate session feedback
        InterviewPrep.SessionFeedback feedback = generateSessionFeedback(session);
        session.setFeedback(feedback);
        
        // Mark as completed
        session.completeSession();
        
        InterviewPrep savedSession = interviewPrepRepository.save(session);
        logger.info("Completed interview prep session: {} for user: {}", sessionId, userId);
        
        return savedSession;
    }

    /**
     * Get session analytics
     */
    public SessionAnalytics getSessionAnalytics(Authentication authentication) {
        String userId = getUserId(authentication);
        
        SessionAnalytics analytics = new SessionAnalytics();
        analytics.setUserId(userId);
        analytics.setGeneratedAt(LocalDateTime.now());
        
        // Basic stats
        analytics.setTotalSessions(interviewPrepRepository.countByUserId(userId));
        analytics.setCompletedSessions(interviewPrepRepository.countByUserIdAndIsCompleted(userId, true));
        
        // Sessions by type
        Map<String, Long> sessionsByType = new HashMap<>();
        for (InterviewPrep.SessionType type : InterviewPrep.SessionType.values()) {
            long count = interviewPrepRepository.countByUserIdAndType(userId, type);
            sessionsByType.put(type.getDisplayName(), count);
        }
        analytics.setSessionsByType(sessionsByType);
        
        // Recent activity
        LocalDateTime weekAgo = LocalDateTime.now().minusWeeks(1);
        analytics.setRecentSessions(interviewPrepRepository.findByUserIdAndCreatedAtAfter(userId, weekAgo).size());
        
        // Performance metrics
        List<InterviewPrep> sessionsWithScores = interviewPrepRepository.findByUserIdWithScores(userId);
        if (!sessionsWithScores.isEmpty()) {
            double averageScore = sessionsWithScores.stream()
                    .filter(s -> s.getSessionStats() != null && s.getSessionStats().getAverageScore() != null)
                    .mapToInt(s -> s.getSessionStats().getAverageScore())
                    .average()
                    .orElse(0.0);
            analytics.setAverageScore(averageScore);
        }
        
        // Improvement areas
        analytics.setImprovementAreas(getCommonImprovementAreas(userId));
        
        return analytics;
    }

    /**
     * Get question bank
     */
    public List<InterviewPrep.InterviewQuestion> getQuestionBank(InterviewPrep.QuestionCategory category, 
                                                               InterviewPrep.QuestionDifficulty difficulty, 
                                                               int limit) {
        // This would typically come from a database of questions
        // For now, we'll generate some sample questions
        return generateSampleQuestions(category, difficulty, limit);
    }

    /**
     * Get company research for job
     */
    public InterviewPrep.CompanyResearch getCompanyResearch(String jobId) {
        return generateCompanyResearch(jobId);
    }

    /**
     * Delete session
     */
    public void deleteSession(Authentication authentication, String sessionId) {
        String userId = getUserId(authentication);
        InterviewPrep session = getSessionById(authentication, sessionId);
        
        interviewPrepRepository.delete(session);
        logger.info("Deleted interview prep session: {} for user: {}", sessionId, userId);
    }

    // Private helper methods
    
    private String getUserId(Authentication authentication) {
        if (authentication.getPrincipal() instanceof Jwt jwt) {
            String clerkUserId = jwt.getSubject();
            User user = userRepository.findByClerkUserId(clerkUserId)
                    .orElseThrow(() -> new ResourceNotFoundException("User not found"));
            return user.getId();
        }
        throw new IllegalArgumentException("Invalid authentication type");
    }

    private List<InterviewPrep.InterviewQuestion> generateQuestions(CreateSessionRequest request, String userId) {
        List<InterviewPrep.InterviewQuestion> questions = new ArrayList<>();
        
        // Get job details if job-specific session
        Job job = null;
        if (request.getJobId() != null) {
            job = jobRepository.findById(request.getJobId()).orElse(null);
        }
        
        // Generate questions based on session type
        switch (request.getType()) {
            case BEHAVIORAL_PREP -> questions.addAll(generateBehavioralQuestions(request.getQuestionCount()));
            case TECHNICAL_PREP -> questions.addAll(generateTechnicalQuestions(request.getQuestionCount(), job));
            case COMPANY_RESEARCH -> questions.addAll(generateCompanyQuestions(request.getQuestionCount(), job));
            case MOCK_INTERVIEW -> questions.addAll(generateMixedQuestions(request.getQuestionCount(), job));
            default -> questions.addAll(generateGeneralQuestions(request.getQuestionCount()));
        }
        
        // Assign question IDs
        for (int i = 0; i < questions.size(); i++) {
            questions.get(i).setQuestionId("q_" + i + "_" + System.currentTimeMillis());
        }
        
        return questions;
    }

    private List<InterviewPrep.InterviewQuestion> generateBehavioralQuestions(int count) {
        List<String> behavioralQuestions = Arrays.asList(
                "Tell me about a time when you had to work with a difficult team member.",
                "Describe a situation where you had to meet a tight deadline.",
                "Give me an example of a time when you had to learn something new quickly.",
                "Tell me about a time when you made a mistake and how you handled it.",
                "Describe a situation where you had to persuade someone to see your point of view.",
                "Tell me about a time when you had to work under pressure.",
                "Give me an example of when you showed leadership skills.",
                "Describe a time when you had to adapt to a significant change.",
                "Tell me about a conflict you had at work and how you resolved it.",
                "Give me an example of when you went above and beyond your job requirements."
        );
        
        return behavioralQuestions.stream()
                .limit(count)
                .map(q -> new InterviewPrep.InterviewQuestion(q, 
                        InterviewPrep.QuestionCategory.BEHAVIORAL, 
                        InterviewPrep.QuestionDifficulty.MEDIUM))
                .collect(Collectors.toList());
    }

    private List<InterviewPrep.InterviewQuestion> generateTechnicalQuestions(int count, Job job) {
        List<String> technicalQuestions = Arrays.asList(
                "Explain the difference between abstract classes and interfaces.",
                "What is the time complexity of common sorting algorithms?",
                "How would you design a scalable web application?",
                "Explain the concept of database normalization.",
                "What are the principles of object-oriented programming?",
                "How do you handle exceptions in your preferred programming language?",
                "Explain the difference between SQL and NoSQL databases.",
                "What is the purpose of version control systems?",
                "How would you optimize a slow-running query?",
                "Explain the concept of microservices architecture."
        );
        
        return technicalQuestions.stream()
                .limit(count)
                .map(q -> new InterviewPrep.InterviewQuestion(q, 
                        InterviewPrep.QuestionCategory.TECHNICAL, 
                        InterviewPrep.QuestionDifficulty.MEDIUM))
                .collect(Collectors.toList());
    }

    private List<InterviewPrep.InterviewQuestion> generateCompanyQuestions(int count, Job job) {
        List<String> companyQuestions = Arrays.asList(
                "Why do you want to work for our company?",
                "What do you know about our company culture?",
                "How do you see yourself contributing to our team?",
                "What interests you most about this role?",
                "How do your values align with our company values?",
                "What do you think are the biggest challenges facing our industry?",
                "How would you handle working in our fast-paced environment?",
                "What questions do you have about our company?",
                "How do you stay updated with industry trends?",
                "What do you think sets our company apart from competitors?"
        );
        
        return companyQuestions.stream()
                .limit(count)
                .map(q -> new InterviewPrep.InterviewQuestion(q, 
                        InterviewPrep.QuestionCategory.COMPANY_SPECIFIC, 
                        InterviewPrep.QuestionDifficulty.EASY))
                .collect(Collectors.toList());
    }

    private List<InterviewPrep.InterviewQuestion> generateMixedQuestions(int count, Job job) {
        List<InterviewPrep.InterviewQuestion> questions = new ArrayList<>();
        
        // Mix of different question types
        int behavioralCount = count / 3;
        int technicalCount = count / 3;
        int companyCount = count - behavioralCount - technicalCount;
        
        questions.addAll(generateBehavioralQuestions(behavioralCount));
        questions.addAll(generateTechnicalQuestions(technicalCount, job));
        questions.addAll(generateCompanyQuestions(companyCount, job));
        
        // Shuffle for variety
        Collections.shuffle(questions);
        
        return questions;
    }

    private List<InterviewPrep.InterviewQuestion> generateGeneralQuestions(int count) {
        List<String> generalQuestions = Arrays.asList(
                "Tell me about yourself.",
                "What are your greatest strengths?",
                "What is your biggest weakness?",
                "Where do you see yourself in 5 years?",
                "Why are you looking for a new job?",
                "What motivates you?",
                "How do you handle stress?",
                "What are your salary expectations?",
                "Do you have any questions for us?",
                "Why should we hire you?"
        );
        
        return generalQuestions.stream()
                .limit(count)
                .map(q -> new InterviewPrep.InterviewQuestion(q, 
                        InterviewPrep.QuestionCategory.BEHAVIORAL, 
                        InterviewPrep.QuestionDifficulty.EASY))
                .collect(Collectors.toList());
    }

    private List<InterviewPrep.InterviewQuestion> generateSampleQuestions(InterviewPrep.QuestionCategory category, 
                                                                         InterviewPrep.QuestionDifficulty difficulty, 
                                                                         int limit) {
        // Generate sample questions based on category and difficulty
        switch (category) {
            case BEHAVIORAL -> { return generateBehavioralQuestions(limit); }
            case TECHNICAL -> { return generateTechnicalQuestions(limit, null); }
            case COMPANY_SPECIFIC -> { return generateCompanyQuestions(limit, null); }
            default -> { return generateGeneralQuestions(limit); }
        }
    }

    private InterviewPrep.CompanyResearch generateCompanyResearch(String jobId) {
        // This would typically fetch real company data
        // For now, return sample data
        InterviewPrep.CompanyResearch research = new InterviewPrep.CompanyResearch();
        research.setCompanyName("Sample Company");
        research.setKeyFacts(Arrays.asList(
                "Founded in 2010",
                "Over 1000 employees worldwide",
                "Leading provider in the industry"
        ));
        research.setRecentNews(Arrays.asList(
                "Recently launched new product line",
                "Expanded to international markets",
                "Received industry award for innovation"
        ));
        research.setInterviewTips(Arrays.asList(
                "Research the company's recent projects",
                "Prepare questions about company culture",
                "Be ready to discuss how you can contribute"
        ));
        research.setCulture("Collaborative and innovative work environment");
        research.setMission("To provide innovative solutions that make a difference");
        research.setValues(Arrays.asList("Innovation", "Integrity", "Collaboration", "Excellence"));
        
        return research;
    }

    private InterviewPrep.AIEvaluation generateAIEvaluation(InterviewPrep.InterviewQuestion question, 
                                                          InterviewPrep session) {
        // This would use OpenAI to evaluate the answer
        // For now, return sample evaluation
        InterviewPrep.AIEvaluation evaluation = new InterviewPrep.AIEvaluation();
        
        // Generate a score based on answer length and content (simplified)
        String answer = question.getUserAnswer();
        int score = Math.min(100, Math.max(20, answer.length() / 10 + (int)(Math.random() * 30)));
        
        evaluation.setOverallScore(score);
        
        Map<String, Integer> criteria = new HashMap<>();
        criteria.put("clarity", score + (int)(Math.random() * 20) - 10);
        criteria.put("relevance", score + (int)(Math.random() * 20) - 10);
        criteria.put("structure", score + (int)(Math.random() * 20) - 10);
        criteria.put("confidence", score + (int)(Math.random() * 20) - 10);
        evaluation.setCriteria(criteria);
        
        evaluation.setFeedback("Good answer with room for improvement in specific areas.");
        evaluation.setImprovements(Arrays.asList(
                "Provide more specific examples",
                "Structure your answer using the STAR method",
                "Be more confident in your delivery"
        ));
        
        return evaluation;
    }

    private InterviewPrep.SessionFeedback generateSessionFeedback(InterviewPrep session) {
        InterviewPrep.SessionFeedback feedback = new InterviewPrep.SessionFeedback();
        
        // Calculate overall score from questions
        List<InterviewPrep.InterviewQuestion> answeredQuestions = session.getQuestions().stream()
                .filter(q -> q.getAiEvaluation() != null)
                .collect(Collectors.toList());
        
        if (!answeredQuestions.isEmpty()) {
            double averageScore = answeredQuestions.stream()
                    .mapToInt(q -> q.getAiEvaluation().getOverallScore())
                    .average()
                    .orElse(0.0);
            feedback.setOverallScore((int) averageScore);
        }
        
        feedback.setStrengths(Arrays.asList(
                "Good communication skills",
                "Relevant experience",
                "Positive attitude"
        ));
        
        feedback.setImprovementAreas(Arrays.asList(
                "Provide more specific examples",
                "Practice technical explanations",
                "Work on confidence"
        ));
        
        feedback.setNextSteps(Arrays.asList(
                "Practice more behavioral questions",
                "Research company-specific information",
                "Prepare questions to ask the interviewer"
        ));
        
        feedback.setRecommendedResources(Arrays.asList(
                "STAR method guide",
                "Company research checklist",
                "Technical interview preparation"
        ));
        
        return feedback;
    }

    private void updateSessionStats(InterviewPrep session) {
        List<InterviewPrep.InterviewQuestion> questions = session.getQuestions();
        InterviewPrep.SessionStats stats = session.getSessionStats();
        
        if (stats == null) {
            stats = new InterviewPrep.SessionStats();
            session.setSessionStats(stats);
        }
        
        // Count completed questions
        long completedCount = questions.stream()
                .filter(q -> q.getUserAnswer() != null && !q.getUserAnswer().trim().isEmpty())
                .count();
        stats.setCompletedQuestions((int) completedCount);
        
        // Calculate average score
        List<InterviewPrep.InterviewQuestion> scoredQuestions = questions.stream()
                .filter(q -> q.getAiEvaluation() != null)
                .collect(Collectors.toList());
        
        if (!scoredQuestions.isEmpty()) {
            double averageScore = scoredQuestions.stream()
                    .mapToInt(q -> q.getAiEvaluation().getOverallScore())
                    .average()
                    .orElse(0.0);
            stats.setAverageScore((int) averageScore);
        }
        
        // Calculate total duration
        int totalDuration = questions.stream()
                .filter(q -> q.getAnswerDuration() != null)
                .mapToInt(InterviewPrep.InterviewQuestion::getAnswerDuration)
                .sum();
        stats.setTotalDuration(totalDuration);
        
        session.updateTimestamp();
    }

    private List<String> getCommonImprovementAreas(String userId) {
        // Analyze user's past sessions to identify common improvement areas
        // For now, return sample areas
        return Arrays.asList(
                "Provide more specific examples",
                "Improve technical explanations",
                "Work on confidence and delivery",
                "Better structure in answers"
        );
    }

    // DTOs
    
    public static class CreateSessionRequest {
        private InterviewPrep.SessionType type;
        private String sessionName;
        private String jobId;
        private Integer questionCount = 10;

        // Getters and setters
        public InterviewPrep.SessionType getType() { return type; }
        public void setType(InterviewPrep.SessionType type) { this.type = type; }
        public String getSessionName() { return sessionName; }
        public void setSessionName(String sessionName) { this.sessionName = sessionName; }
        public String getJobId() { return jobId; }
        public void setJobId(String jobId) { this.jobId = jobId; }
        public Integer getQuestionCount() { return questionCount; }
        public void setQuestionCount(Integer questionCount) { this.questionCount = questionCount; }
    }

    public static class AnswerQuestionRequest {
        private String questionId;
        private String answer;
        private Integer answerDuration;
        private String audioRecordingUrl;

        // Getters and setters
        public String getQuestionId() { return questionId; }
        public void setQuestionId(String questionId) { this.questionId = questionId; }
        public String getAnswer() { return answer; }
        public void setAnswer(String answer) { this.answer = answer; }
        public Integer getAnswerDuration() { return answerDuration; }
        public void setAnswerDuration(Integer answerDuration) { this.answerDuration = answerDuration; }
        public String getAudioRecordingUrl() { return audioRecordingUrl; }
        public void setAudioRecordingUrl(String audioRecordingUrl) { this.audioRecordingUrl = audioRecordingUrl; }
    }

    public static class SessionAnalytics {
        private String userId;
        private LocalDateTime generatedAt;
        private Long totalSessions;
        private Long completedSessions;
        private Map<String, Long> sessionsByType;
        private Integer recentSessions;
        private Double averageScore;
        private List<String> improvementAreas;

        // Getters and setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public LocalDateTime getGeneratedAt() { return generatedAt; }
        public void setGeneratedAt(LocalDateTime generatedAt) { this.generatedAt = generatedAt; }
        public Long getTotalSessions() { return totalSessions; }
        public void setTotalSessions(Long totalSessions) { this.totalSessions = totalSessions; }
        public Long getCompletedSessions() { return completedSessions; }
        public void setCompletedSessions(Long completedSessions) { this.completedSessions = completedSessions; }
        public Map<String, Long> getSessionsByType() { return sessionsByType; }
        public void setSessionsByType(Map<String, Long> sessionsByType) { this.sessionsByType = sessionsByType; }
        public Integer getRecentSessions() { return recentSessions; }
        public void setRecentSessions(Integer recentSessions) { this.recentSessions = recentSessions; }
        public Double getAverageScore() { return averageScore; }
        public void setAverageScore(Double averageScore) { this.averageScore = averageScore; }
        public List<String> getImprovementAreas() { return improvementAreas; }
        public void setImprovementAreas(List<String> improvementAreas) { this.improvementAreas = improvementAreas; }
    }
}
