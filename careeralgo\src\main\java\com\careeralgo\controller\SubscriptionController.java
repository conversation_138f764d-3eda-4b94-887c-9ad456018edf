package com.careeralgo.controller;

import com.careeralgo.constant.SubscriptionPlan;
import com.careeralgo.model.UserSubscription;
import com.careeralgo.service.SubscriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for subscription management
 */
@RestController
@RequestMapping("/subscriptions")
@Tag(name = "Subscription Management", description = "APIs for managing user subscriptions and billing")
public class SubscriptionController {

    @Autowired
    private SubscriptionService subscriptionService;

    /**
     * Get user's current subscription
     */
    @GetMapping("/current")
    @Operation(summary = "Get current subscription", description = "Get the user's current subscription details")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Subscription retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<UserSubscription> getCurrentSubscription(Authentication authentication) {
        UserSubscription subscription = subscriptionService.getUserSubscription(authentication);
        return ResponseEntity.ok(subscription);
    }

    /**
     * Get subscription usage summary
     */
    @GetMapping("/usage")
    @Operation(summary = "Get usage summary", description = "Get current usage summary for the user's subscription")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Usage summary retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<SubscriptionService.SubscriptionUsageSummary> getUsageSummary(Authentication authentication) {
        SubscriptionService.SubscriptionUsageSummary summary = subscriptionService.getUsageSummary(authentication);
        return ResponseEntity.ok(summary);
    }

    /**
     * Get available subscription plans
     */
    @GetMapping("/plans")
    @Operation(summary = "Get subscription plans", description = "Get all available subscription plans with pricing and features")
    @ApiResponse(responseCode = "200", description = "Subscription plans retrieved successfully")
    public ResponseEntity<List<SubscriptionService.SubscriptionPlanInfo>> getAvailablePlans() {
        List<SubscriptionService.SubscriptionPlanInfo> plans = subscriptionService.getAvailablePlans();
        return ResponseEntity.ok(plans);
    }

    /**
     * Create or upgrade subscription
     */
    @PostMapping("/subscribe")
    @Operation(summary = "Create or upgrade subscription", description = "Create a new subscription or upgrade existing one")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Subscription created/upgraded successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid subscription data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<UserSubscription> createOrUpgradeSubscription(
            Authentication authentication,
            @Valid @RequestBody SubscriptionRequest request) {
        
        UserSubscription subscription = subscriptionService.createOrUpgradeSubscription(
                authentication, 
                request.getPlan(), 
                request.getBillingCycle()
        );
        
        return ResponseEntity.status(201).body(subscription);
    }

    /**
     * Cancel subscription
     */
    @PostMapping("/cancel")
    @Operation(summary = "Cancel subscription", description = "Cancel the user's current subscription")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Subscription canceled successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid cancellation request"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "No active subscription found")
    })
    public ResponseEntity<UserSubscription> cancelSubscription(
            Authentication authentication,
            @RequestBody CancelSubscriptionRequest request) {
        
        UserSubscription subscription = subscriptionService.cancelSubscription(
                authentication, 
                request.getReason(), 
                request.isImmediate()
        );
        
        return ResponseEntity.ok(subscription);
    }

    /**
     * Reactivate subscription
     */
    @PostMapping("/reactivate")
    @Operation(summary = "Reactivate subscription", description = "Reactivate a canceled subscription")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Subscription reactivated successfully"),
            @ApiResponse(responseCode = "400", description = "Subscription cannot be reactivated"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "No subscription found")
    })
    public ResponseEntity<UserSubscription> reactivateSubscription(Authentication authentication) {
        UserSubscription subscription = subscriptionService.reactivateSubscription(authentication);
        return ResponseEntity.ok(subscription);
    }

    /**
     * Check feature access
     */
    @GetMapping("/features/{feature}/access")
    @Operation(summary = "Check feature access", description = "Check if user can access a specific feature")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Feature access checked successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, Object>> checkFeatureAccess(
            Authentication authentication,
            @Parameter(description = "Feature name") @PathVariable String feature) {
        
        boolean canUse = subscriptionService.canUseFeature(authentication, feature);
        
        return ResponseEntity.ok(Map.of(
                "feature", feature,
                "hasAccess", canUse,
                "message", canUse ? "Feature access granted" : "Feature requires subscription upgrade"
        ));
    }

    /**
     * Check usage remaining
     */
    @GetMapping("/usage/{usageType}/remaining")
    @Operation(summary = "Check usage remaining", description = "Check if user has remaining usage for a specific type")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Usage checked successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, Object>> checkUsageRemaining(
            Authentication authentication,
            @Parameter(description = "Usage type (applications, resumes, interviews, ai)") @PathVariable String usageType) {
        
        boolean hasRemaining = subscriptionService.hasUsageRemaining(authentication, usageType);
        
        return ResponseEntity.ok(Map.of(
                "usageType", usageType,
                "hasRemaining", hasRemaining,
                "message", hasRemaining ? "Usage available" : "Usage limit reached"
        ));
    }

    /**
     * Increment usage counter
     */
    @PostMapping("/usage/{usageType}/increment")
    @Operation(summary = "Increment usage", description = "Increment usage counter for a specific type")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Usage incremented successfully"),
            @ApiResponse(responseCode = "400", description = "Usage limit exceeded"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, String>> incrementUsage(
            Authentication authentication,
            @Parameter(description = "Usage type") @PathVariable String usageType) {
        
        try {
            subscriptionService.incrementUsage(authentication, usageType);
            
            return ResponseEntity.ok(Map.of(
                    "message", "Usage incremented successfully",
                    "usageType", usageType
            ));
            
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of(
                    "error", "Usage limit exceeded",
                    "message", e.getMessage()
            ));
        }
    }

    /**
     * Get subscription analytics (admin only)
     */
    @GetMapping("/analytics")
    @Operation(summary = "Get subscription analytics", description = "Get subscription analytics and metrics (admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Analytics retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Admin access required")
    })
    public ResponseEntity<SubscriptionService.SubscriptionAnalytics> getSubscriptionAnalytics() {
        // TODO: Add admin role check
        SubscriptionService.SubscriptionAnalytics analytics = subscriptionService.getSubscriptionAnalytics();
        return ResponseEntity.ok(analytics);
    }

    /**
     * Get subscription plans enum values
     */
    @GetMapping("/plans/enum")
    @Operation(summary = "Get subscription plan enums", description = "Get all subscription plan enum values")
    @ApiResponse(responseCode = "200", description = "Plan enums retrieved successfully")
    public ResponseEntity<SubscriptionPlan[]> getSubscriptionPlanEnums() {
        return ResponseEntity.ok(SubscriptionPlan.values());
    }

    /**
     * Get billing cycle enum values
     */
    @GetMapping("/billing-cycles")
    @Operation(summary = "Get billing cycle enums", description = "Get all billing cycle enum values")
    @ApiResponse(responseCode = "200", description = "Billing cycle enums retrieved successfully")
    public ResponseEntity<UserSubscription.BillingCycle[]> getBillingCycleEnums() {
        return ResponseEntity.ok(UserSubscription.BillingCycle.values());
    }

    /**
     * Get subscription status enum values
     */
    @GetMapping("/statuses")
    @Operation(summary = "Get subscription status enums", description = "Get all subscription status enum values")
    @ApiResponse(responseCode = "200", description = "Status enums retrieved successfully")
    public ResponseEntity<UserSubscription.SubscriptionStatus[]> getSubscriptionStatusEnums() {
        return ResponseEntity.ok(UserSubscription.SubscriptionStatus.values());
    }

    /**
     * Validate subscription access
     */
    @PostMapping("/validate")
    @Operation(summary = "Validate subscription access", description = "Validate if user can perform a specific action")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Validation completed successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, Object>> validateSubscriptionAccess(
            Authentication authentication,
            @RequestBody ValidationRequest request) {
        
        boolean canUseFeature = subscriptionService.canUseFeature(authentication, request.getFeature());
        boolean hasUsageRemaining = subscriptionService.hasUsageRemaining(authentication, request.getUsageType());
        
        boolean canProceed = canUseFeature && hasUsageRemaining;
        
        return ResponseEntity.ok(Map.of(
                "canProceed", canProceed,
                "featureAccess", canUseFeature,
                "usageRemaining", hasUsageRemaining,
                "feature", request.getFeature(),
                "usageType", request.getUsageType(),
                "message", canProceed ? "Action allowed" : "Subscription upgrade required"
        ));
    }

    // Request DTOs
    
    public static class SubscriptionRequest {
        private SubscriptionPlan plan;
        private UserSubscription.BillingCycle billingCycle;

        // Getters and setters
        public SubscriptionPlan getPlan() { return plan; }
        public void setPlan(SubscriptionPlan plan) { this.plan = plan; }
        public UserSubscription.BillingCycle getBillingCycle() { return billingCycle; }
        public void setBillingCycle(UserSubscription.BillingCycle billingCycle) { this.billingCycle = billingCycle; }
    }

    public static class CancelSubscriptionRequest {
        private String reason;
        private boolean immediate;

        // Getters and setters
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        public boolean isImmediate() { return immediate; }
        public void setImmediate(boolean immediate) { this.immediate = immediate; }
    }

    public static class ValidationRequest {
        private String feature;
        private String usageType;

        // Getters and setters
        public String getFeature() { return feature; }
        public void setFeature(String feature) { this.feature = feature; }
        public String getUsageType() { return usageType; }
        public void setUsageType(String usageType) { this.usageType = usageType; }
    }
}
