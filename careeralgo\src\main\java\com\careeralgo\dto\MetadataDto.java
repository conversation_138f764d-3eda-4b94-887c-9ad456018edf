package com.careeralgo.dto;

import com.careeralgo.model.Metadata;

import java.time.LocalDateTime;

/**
 * DTO for user metadata
 */
public class MetadataDto {

    private LocalDateTime lastLoginAt;
    private Integer loginCount;
    private Integer profileCompleteness;
    private boolean onboardingCompleted;
    private String referralCode;

    // Constructors
    public MetadataDto() {}

    public MetadataDto(Metadata metadata) {
        this.lastLoginAt = metadata.getLastLoginAt();
        this.loginCount = metadata.getLoginCount();
        this.profileCompleteness = metadata.getProfileCompleteness();
        this.onboardingCompleted = metadata.isOnboardingCompleted();
        this.referralCode = metadata.getReferralCode();
        // Note: referredBy is excluded for privacy
    }

    // Getters and Setters
    public LocalDateTime getLastLoginAt() {
        return lastLoginAt;
    }

    public void setLastLoginAt(LocalDateTime lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
    }

    public Integer getLoginCount() {
        return loginCount;
    }

    public void setLoginCount(Integer loginCount) {
        this.loginCount = loginCount;
    }

    public Integer getProfileCompleteness() {
        return profileCompleteness;
    }

    public void setProfileCompleteness(Integer profileCompleteness) {
        this.profileCompleteness = profileCompleteness;
    }

    public boolean isOnboardingCompleted() {
        return onboardingCompleted;
    }

    public void setOnboardingCompleted(boolean onboardingCompleted) {
        this.onboardingCompleted = onboardingCompleted;
    }

    public String getReferralCode() {
        return referralCode;
    }

    public void setReferralCode(String referralCode) {
        this.referralCode = referralCode;
    }
}
